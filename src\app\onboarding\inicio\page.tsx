'use client';
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export default function InicioOnboardingPage() {
  const router = useRouter();
  const { user, loading: authLoading, refreshUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [plano, setPlano] = useState<string | null>(null);

  useEffect(() => {
    const checkOnboardingStatus = async () => {
      if (authLoading) return;

      if (!user) {
        router.push('/login');
        return;
      }

      // Forçar atualização dos dados do usuário para garantir que temos os dados mais recentes
      await refreshUser();

      // Aguardar um pouco para garantir que a atualização foi processada
      setTimeout(async () => {
        // Verificar se o usuário tem pagamento confirmado e onboarding pendente
        const supabase = createClient();
        const { data: { user: authUser } } = await supabase.auth.getUser();

        console.log('Dados do usuário na página de início:', authUser?.user_metadata);

        if (!authUser?.user_metadata?.pagamento_confirmado) {
          console.log('Pagamento não confirmado, redirecionando para planos');
          router.push('/planos');
          return;
        }

        if (!authUser?.user_metadata?.onboarding_pendente) {
          // Onboarding já foi concluído, redirecionar para dashboard
          console.log('Onboarding já concluído, redirecionando para dashboard');
          router.push('/proprietario/dashboard');
          return;
        }

        // Verificar se já tem dados de empresa salvos
        const dadosEmpresa = localStorage.getItem('dadosEstabelecimento');
        if (dadosEmpresa) {
          // Continuar de onde parou
          router.push('/onboarding/cadastro-servico');
          return;
        }

        setPlano(authUser.user_metadata.plano_selecionado);
        setLoading(false);
      }, 1000); // Aguardar 1 segundo para garantir que os dados foram atualizados
    };

    checkOnboardingStatus();
  }, [user, authLoading, router, refreshUser]);

  const handleIniciarOnboarding = () => {
    router.push('/onboarding/registro-empresa');
  };

  if (loading || authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600 mb-4"></div>
          <p className="text-gray-600">Verificando status do onboarding...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
          {/* Header de Sucesso */}
          <div className="text-center mb-8">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
              <svg className="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">
              Pagamento Confirmado!
            </h1>
            <p className="text-gray-600 text-lg">
              Seu plano {plano === 'essencial' ? 'Essencial' : 'Premium'} foi ativado com sucesso.
            </p>
          </div>

          {/* Informações do Plano */}
          <div className="bg-blue-50 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-blue-800 mb-4">
              Plano {plano === 'essencial' ? 'Essencial' : 'Premium'} Ativado
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
              <div>
                <strong>Valor mensal:</strong> {plano === 'essencial' ? 'R$ 99,00' : 'R$ 199,00'}
              </div>
              <div>
                <strong>Serviços:</strong> Até {plano === 'essencial' ? '6' : '12'} serviços
              </div>
              <div>
                <strong>Colaboradores:</strong> Até {plano === 'essencial' ? '2' : '6'} colaboradores extras
              </div>
              <div>
                <strong>Relatórios:</strong> {plano === 'essencial' ? 'Básicos' : 'Completos'}
              </div>
            </div>
          </div>

          {/* Próximos Passos */}
          <div className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">
              Próximos Passos
            </h2>
            <p className="text-gray-600 mb-6">
              Agora vamos configurar seu estabelecimento para que você possa começar a receber agendamentos. 
              O processo é rápido e simples, levando apenas alguns minutos.
            </p>
            
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="flex-shrink-0 h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center mr-3 mt-1">
                  <span className="text-blue-600 text-sm font-medium">1</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-800">Dados do Estabelecimento</h3>
                  <p className="text-gray-600 text-sm">Nome, endereço e informações básicas da sua empresa</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0 h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center mr-3 mt-1">
                  <span className="text-blue-600 text-sm font-medium">2</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-800">Cadastro de Serviços</h3>
                  <p className="text-gray-600 text-sm">Adicione os serviços que seu estabelecimento oferece</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0 h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center mr-3 mt-1">
                  <span className="text-blue-600 text-sm font-medium">3</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-800">Horários de Funcionamento</h3>
                  <p className="text-gray-600 text-sm">Configure os dias e horários de atendimento</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0 h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center mr-3 mt-1">
                  <span className="text-blue-600 text-sm font-medium">4</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-800">Finalização</h3>
                  <p className="text-gray-600 text-sm">Revisão e ativação do seu estabelecimento</p>
                </div>
              </div>
            </div>
          </div>

          {/* Botão de Ação */}
          <div className="text-center">
            <button
              onClick={handleIniciarOnboarding}
              className="bg-blue-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors duration-300"
            >
              Iniciar Configuração do Estabelecimento
            </button>
          </div>

          {/* Suporte */}
          <div className="mt-8 pt-6 border-t border-gray-200 text-center">
            <p className="text-gray-600 text-sm">
              Precisa de ajuda? Entre em contato com nosso suporte em{' '}
              <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-800">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
