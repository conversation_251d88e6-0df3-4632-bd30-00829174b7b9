import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    // Verificar se é administrador
    const { data: userData } = await supabase
      .from('auth.users')
      .select('raw_user_meta_data')
      .eq('id', user.id)
      .single();

    const userRole = userData?.raw_user_meta_data?.role;
    if (userRole !== 'Administrador') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado. Apenas administradores podem gerenciar sessões.' },
        { status: 403 }
      );
    }

    // Buscar parâmetros de filtro
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const data_inicio = searchParams.get('data_inicio');
    const data_fim = searchParams.get('data_fim');
    const moderador = searchParams.get('moderador');

    // Construir query
    let query = supabase
      .from('sessoes_teste')
      .select(`
        *,
        moderador:auth.users!moderador_user_id(
          id,
          email,
          raw_user_meta_data
        )
      `)
      .order('created_at', { ascending: false });

    // Aplicar filtros
    if (status) {
      query = query.eq('status', status);
    }
    if (data_inicio) {
      query = query.gte('data_inicio', data_inicio);
    }
    if (data_fim) {
      query = query.lte('data_fim', data_fim);
    }
    if (moderador) {
      query = query.eq('moderador_user_id', moderador);
    }

    const { data: sessoes, error } = await query;

    if (error) {
      console.error('Erro ao buscar sessões:', error);
      return NextResponse.json(
        { success: false, error: 'Erro ao buscar sessões de teste' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: sessoes
    });

  } catch (error: any) {
    console.error('Erro na API de sessões:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    // Verificar se é administrador
    const { data: userData } = await supabase
      .from('auth.users')
      .select('raw_user_meta_data')
      .eq('id', user.id)
      .single();

    const userRole = userData?.raw_user_meta_data?.role;
    if (userRole !== 'Administrador') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado. Apenas administradores podem criar sessões.' },
        { status: 403 }
      );
    }

    const body = await request.json();
    
    // Validar dados obrigatórios
    const {
      nome_sessao,
      descricao,
      data_inicio,
      data_fim,
      cenarios_incluidos,
      participantes_alvo
    } = body;

    if (!nome_sessao || !descricao || !data_inicio || !data_fim) {
      return NextResponse.json(
        { success: false, error: 'Campos obrigatórios não preenchidos' },
        { status: 400 }
      );
    }

    if (!cenarios_incluidos || !Array.isArray(cenarios_incluidos) || cenarios_incluidos.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Sessão deve incluir pelo menos um cenário' },
        { status: 400 }
      );
    }

    // Validar datas
    const dataInicio = new Date(data_inicio);
    const dataFim = new Date(data_fim);
    const agora = new Date();

    if (dataInicio < agora) {
      return NextResponse.json(
        { success: false, error: 'Data de início deve ser futura' },
        { status: 400 }
      );
    }

    if (dataFim <= dataInicio) {
      return NextResponse.json(
        { success: false, error: 'Data de fim deve ser posterior à data de início' },
        { status: 400 }
      );
    }

    // Verificar se os cenários existem
    const { data: cenariosExistentes, error: cenariosError } = await supabase
      .from('cenarios_teste')
      .select('cenario_id')
      .in('cenario_id', cenarios_incluidos)
      .eq('ativo', true);

    if (cenariosError) {
      console.error('Erro ao verificar cenários:', cenariosError);
      return NextResponse.json(
        { success: false, error: 'Erro ao verificar cenários' },
        { status: 500 }
      );
    }

    if (!cenariosExistentes || cenariosExistentes.length !== cenarios_incluidos.length) {
      return NextResponse.json(
        { success: false, error: 'Um ou mais cenários não existem ou estão inativos' },
        { status: 400 }
      );
    }

    // Criar sessão
    const { data: novaSessao, error } = await supabase
      .from('sessoes_teste')
      .insert({
        nome_sessao,
        descricao,
        data_inicio,
        data_fim,
        status: 'Planejada',
        cenarios_incluidos,
        participantes_alvo: participantes_alvo || 5,
        participantes_confirmados: 0,
        moderador_user_id: user.id,
        observacoes_gerais: body.observacoes_gerais
      })
      .select()
      .single();

    if (error) {
      console.error('Erro ao criar sessão:', error);
      return NextResponse.json(
        { success: false, error: 'Erro ao criar sessão de teste' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: novaSessao,
      message: 'Sessão de teste criada com sucesso'
    });

  } catch (error: any) {
    console.error('Erro na API de criação de sessão:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
