'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

interface BrandingColors {
  primary: string;
  primaryHover: string;
  secondary: string;
  accent: string;
  accentHover: string;
}

interface BrandingConfig {
  colors: BrandingColors;
  logoUrl?: string;
  companyName?: string;
  isCustomized: boolean;
}

interface BrandingContextType {
  branding: BrandingConfig;
  setBranding: (branding: Partial<BrandingConfig>) => void;
  resetBranding: () => void;
  applyBranding: (colors: Partial<BrandingColors>) => void;
  previewBranding: (colors: Partial<BrandingColors>) => void;
  clearPreview: () => void;
  isPreviewMode: boolean;
}

const BrandingContext = createContext<BrandingContextType | undefined>(undefined);

// Cores padrão do ServiceTech
const DEFAULT_COLORS: BrandingColors = {
  primary: '#3B82F6',
  primaryHover: '#2563EB',
  secondary: '#6B7280',
  accent: '#F59E0B',
  accentHover: '#D97706',
};

const DEFAULT_BRANDING: BrandingConfig = {
  colors: DEFAULT_COLORS,
  isCustomized: false,
};

export function BrandingProvider({ children }: { children: React.ReactNode }) {
  const [branding, setBrandingState] = useState<BrandingConfig>(DEFAULT_BRANDING);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [originalColors, setOriginalColors] = useState<BrandingColors | null>(null);

  // Carregar configuração salva
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('company-branding');
      if (saved) {
        try {
          const savedBranding = JSON.parse(saved);
          setBrandingState(savedBranding);
          applyColorsToCSS(savedBranding.colors);
        } catch (error) {
          console.error('Erro ao carregar configuração de marca:', error);
        }
      }
    }
  }, []);

  // Aplicar cores ao CSS
  const applyColorsToCSS = (colors: BrandingColors) => {
    if (typeof window === 'undefined') return;

    const root = document.documentElement;
    root.style.setProperty('--primary', colors.primary);
    root.style.setProperty('--primary-hover', colors.primaryHover);
    root.style.setProperty('--secondary', colors.secondary);
    root.style.setProperty('--accent', colors.accent);
    root.style.setProperty('--accent-hover', colors.accentHover);
  };

  // Salvar configuração
  const saveBranding = (newBranding: BrandingConfig) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('company-branding', JSON.stringify(newBranding));
    }
  };

  const setBranding = (updates: Partial<BrandingConfig>) => {
    const newBranding = { ...branding, ...updates };
    setBrandingState(newBranding);
    
    if (updates.colors) {
      applyColorsToCSS(newBranding.colors);
    }
    
    saveBranding(newBranding);
  };

  const resetBranding = () => {
    setBrandingState(DEFAULT_BRANDING);
    applyColorsToCSS(DEFAULT_COLORS);
    
    if (typeof window !== 'undefined') {
      localStorage.removeItem('company-branding');
    }
  };

  const applyBranding = (colors: Partial<BrandingColors>) => {
    const newColors = { ...branding.colors, ...colors };
    const newBranding = {
      ...branding,
      colors: newColors,
      isCustomized: true,
    };
    
    setBrandingState(newBranding);
    applyColorsToCSS(newColors);
    saveBranding(newBranding);
    
    // Limpar preview se estiver ativo
    if (isPreviewMode) {
      setIsPreviewMode(false);
      setOriginalColors(null);
    }
  };

  const previewBranding = (colors: Partial<BrandingColors>) => {
    // Salvar cores originais se não estiver em preview
    if (!isPreviewMode) {
      setOriginalColors(branding.colors);
      setIsPreviewMode(true);
    }
    
    const previewColors = { ...branding.colors, ...colors };
    applyColorsToCSS(previewColors);
  };

  const clearPreview = () => {
    if (isPreviewMode && originalColors) {
      applyColorsToCSS(originalColors);
      setIsPreviewMode(false);
      setOriginalColors(null);
    }
  };

  const value: BrandingContextType = {
    branding,
    setBranding,
    resetBranding,
    applyBranding,
    previewBranding,
    clearPreview,
    isPreviewMode,
  };

  return (
    <BrandingContext.Provider value={value}>
      {children}
    </BrandingContext.Provider>
  );
}

export function useBranding() {
  const context = useContext(BrandingContext);
  if (context === undefined) {
    throw new Error('useBranding must be used within a BrandingProvider');
  }
  return context;
}

// Hook para aplicar branding de uma empresa específica
export function useCompanyBranding(companySlug?: string) {
  const { setBranding, resetBranding } = useBranding();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!companySlug) {
      resetBranding();
      return;
    }

    const loadCompanyBranding = async () => {
      setIsLoading(true);
      try {
        // Aqui você faria uma chamada à API para buscar as cores da empresa
        const response = await fetch(`/api/empresas/${companySlug}/branding`);
        
        if (response.ok) {
          const companyBranding = await response.json();
          
          if (companyBranding.colors) {
            setBranding({
              colors: companyBranding.colors,
              logoUrl: companyBranding.logoUrl,
              companyName: companyBranding.companyName,
              isCustomized: true,
            });
          }
        } else {
          // Se não encontrar branding customizado, usar padrão
          resetBranding();
        }
      } catch (error) {
        console.error('Erro ao carregar branding da empresa:', error);
        resetBranding();
      } finally {
        setIsLoading(false);
      }
    };

    loadCompanyBranding();
  }, [companySlug, setBranding, resetBranding]);

  return { isLoading };
}

// Utilitários para cores
export const colorUtils = {
  // Converter hex para RGB
  hexToRgb: (hex: string): { r: number; g: number; b: number } | null => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  },

  // Converter RGB para hex
  rgbToHex: (r: number, g: number, b: number): string => {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
  },

  // Escurecer uma cor
  darken: (hex: string, amount: number = 0.1): string => {
    const rgb = colorUtils.hexToRgb(hex);
    if (!rgb) return hex;

    const r = Math.max(0, Math.floor(rgb.r * (1 - amount)));
    const g = Math.max(0, Math.floor(rgb.g * (1 - amount)));
    const b = Math.max(0, Math.floor(rgb.b * (1 - amount)));

    return colorUtils.rgbToHex(r, g, b);
  },

  // Clarear uma cor
  lighten: (hex: string, amount: number = 0.1): string => {
    const rgb = colorUtils.hexToRgb(hex);
    if (!rgb) return hex;

    const r = Math.min(255, Math.floor(rgb.r + (255 - rgb.r) * amount));
    const g = Math.min(255, Math.floor(rgb.g + (255 - rgb.g) * amount));
    const b = Math.min(255, Math.floor(rgb.b + (255 - rgb.b) * amount));

    return colorUtils.rgbToHex(r, g, b);
  },

  // Verificar se uma cor é clara ou escura
  isLight: (hex: string): boolean => {
    const rgb = colorUtils.hexToRgb(hex);
    if (!rgb) return true;

    // Calcular luminância
    const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
    return luminance > 0.5;
  },

  // Gerar cor de hover automaticamente
  generateHoverColor: (hex: string): string => {
    return colorUtils.isLight(hex) 
      ? colorUtils.darken(hex, 0.1) 
      : colorUtils.lighten(hex, 0.1);
  },

  // Validar se é uma cor hex válida
  isValidHex: (hex: string): boolean => {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(hex);
  },
};
