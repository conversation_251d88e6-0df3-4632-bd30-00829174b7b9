/**
 * Testes para o componente ListaServicos
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ListaServicos } from '../ListaServicos';
import { Ser<PERSON><PERSON> } from '@/types/servicos';

describe('ListaServicos', () => {
  const mockServicos: Servico[] = [
    {
      servico_id: 1,
      nome_servico: 'Corte de Cabelo',
      descricao: 'Corte masculino moderno',
      duracao_minutos: 60,
      preco: 50.00,
      ativo: true,
      categoria: 'Cabelo',
      empresa_id: 1,
      created_at: '2025-01-01T00:00:00Z',
      updated_at: '2025-01-01T00:00:00Z'
    },
    {
      servico_id: 2,
      nome_servico: 'Barba',
      descricao: 'Aparar e modelar barba',
      duracao_minutos: 30,
      preco: 25.00,
      ativo: true,
      categoria: 'Barba',
      empresa_id: 1,
      created_at: '2025-01-01T00:00:00Z',
      updated_at: '2025-01-01T00:00:00Z'
    },
    {
      servico_id: 3,
      nome_servico: 'Massagem',
      descricao: 'Massagem relaxante',
      duracao_minutos: 90,
      preco: 80.00,
      ativo: false,
      categoria: 'Bem-estar',
      empresa_id: 1,
      created_at: '2025-01-01T00:00:00Z',
      updated_at: '2025-01-01T00:00:00Z'
    }
  ];

  const mockCallbacks = {
    onEditar: jest.fn(),
    onExcluir: jest.fn(),
    onAlternarStatus: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('deve renderizar lista de serviços corretamente', () => {
    render(
      <ListaServicos 
        servicos={mockServicos}
        {...mockCallbacks}
      />
    );

    expect(screen.getByText('Corte de Cabelo')).toBeInTheDocument();
    expect(screen.getByText('Barba')).toBeInTheDocument();
    expect(screen.getByText('Massagem')).toBeInTheDocument();
  });

  it('deve mostrar estatísticas corretas', () => {
    render(
      <ListaServicos 
        servicos={mockServicos}
        {...mockCallbacks}
      />
    );

    expect(screen.getByText('3')).toBeInTheDocument(); // Total
    expect(screen.getByText('2')).toBeInTheDocument(); // Ativos
    expect(screen.getByText('1')).toBeInTheDocument(); // Inativos
    expect(screen.getByText('3')).toBeInTheDocument(); // Categorias
  });

  it('deve formatar preço corretamente', () => {
    render(
      <ListaServicos 
        servicos={mockServicos}
        {...mockCallbacks}
      />
    );

    expect(screen.getByText('R$ 50,00')).toBeInTheDocument();
    expect(screen.getByText('R$ 25,00')).toBeInTheDocument();
    expect(screen.getByText('R$ 80,00')).toBeInTheDocument();
  });

  it('deve formatar duração corretamente', () => {
    render(
      <ListaServicos 
        servicos={mockServicos}
        {...mockCallbacks}
      />
    );

    expect(screen.getByText('1h')).toBeInTheDocument(); // 60 minutos
    expect(screen.getByText('30min')).toBeInTheDocument();
    expect(screen.getByText('1h 30min')).toBeInTheDocument(); // 90 minutos
  });

  it('deve mostrar status ativo/inativo', () => {
    render(
      <ListaServicos 
        servicos={mockServicos}
        {...mockCallbacks}
      />
    );

    const statusAtivo = screen.getAllByText('Ativo');
    const statusInativo = screen.getAllByText('Inativo');

    expect(statusAtivo).toHaveLength(2);
    expect(statusInativo).toHaveLength(1);
  });

  it('deve mostrar categorias como badges', () => {
    render(
      <ListaServicos 
        servicos={mockServicos}
        {...mockCallbacks}
      />
    );

    expect(screen.getByText('Cabelo')).toBeInTheDocument();
    expect(screen.getByText('Barba')).toBeInTheDocument();
    expect(screen.getByText('Bem-estar')).toBeInTheDocument();
  });

  it('deve chamar onEditar quando botão editar é clicado', () => {
    render(
      <ListaServicos 
        servicos={mockServicos}
        {...mockCallbacks}
      />
    );

    const botoesEditar = screen.getAllByText('Editar');
    fireEvent.click(botoesEditar[0]);

    expect(mockCallbacks.onEditar).toHaveBeenCalledWith(mockServicos[0]);
  });

  it('deve chamar onExcluir quando botão excluir é clicado', () => {
    render(
      <ListaServicos 
        servicos={mockServicos}
        {...mockCallbacks}
      />
    );

    const botoesExcluir = screen.getAllByText('Excluir');
    fireEvent.click(botoesExcluir[0]);

    expect(mockCallbacks.onExcluir).toHaveBeenCalledWith(1);
  });

  it('deve chamar onAlternarStatus quando botão ativar/desativar é clicado', () => {
    render(
      <ListaServicos 
        servicos={mockServicos}
        {...mockCallbacks}
      />
    );

    const botaoDesativar = screen.getByText('Desativar');
    fireEvent.click(botaoDesativar);

    expect(mockCallbacks.onAlternarStatus).toHaveBeenCalledWith(1, false);

    const botaoAtivar = screen.getByText('Ativar');
    fireEvent.click(botaoAtivar);

    expect(mockCallbacks.onAlternarStatus).toHaveBeenCalledWith(3, true);
  });

  it('deve não mostrar ações quando mostrarAcoes é false', () => {
    render(
      <ListaServicos 
        servicos={mockServicos}
        mostrarAcoes={false}
      />
    );

    expect(screen.queryByText('Editar')).not.toBeInTheDocument();
    expect(screen.queryByText('Excluir')).not.toBeInTheDocument();
    expect(screen.queryByText('Desativar')).not.toBeInTheDocument();
  });

  it('deve filtrar por categoria', () => {
    render(
      <ListaServicos 
        servicos={mockServicos}
        filtroCategoria="Cabelo"
        {...mockCallbacks}
      />
    );

    expect(screen.getByText('Corte de Cabelo')).toBeInTheDocument();
    expect(screen.queryByText('Barba')).not.toBeInTheDocument();
    expect(screen.queryByText('Massagem')).not.toBeInTheDocument();

    // Estatísticas devem refletir filtro
    expect(screen.getByText('1')).toBeInTheDocument(); // Total filtrado
  });

  it('deve filtrar por status ativo', () => {
    render(
      <ListaServicos 
        servicos={mockServicos}
        filtroAtivo={true}
        {...mockCallbacks}
      />
    );

    expect(screen.getByText('Corte de Cabelo')).toBeInTheDocument();
    expect(screen.getByText('Barba')).toBeInTheDocument();
    expect(screen.queryByText('Massagem')).not.toBeInTheDocument();

    // Estatísticas devem refletir filtro
    expect(screen.getByText('2')).toBeInTheDocument(); // Total filtrado
  });

  it('deve mostrar estado de loading', () => {
    render(
      <ListaServicos 
        servicos={[]}
        loading={true}
        {...mockCallbacks}
      />
    );

    // Deve mostrar skeleton loading
    const skeletons = screen.getAllByRole('generic');
    expect(skeletons.length).toBeGreaterThan(0);
  });

  it('deve mostrar mensagem quando não há serviços', () => {
    render(
      <ListaServicos 
        servicos={[]}
        {...mockCallbacks}
      />
    );

    expect(screen.getByText('Nenhum serviço encontrado')).toBeInTheDocument();
    expect(screen.getByText('Cadastre seu primeiro serviço para começar.')).toBeInTheDocument();
  });

  it('deve mostrar mensagem específica quando há filtros aplicados', () => {
    render(
      <ListaServicos 
        servicos={[]}
        filtroCategoria="Inexistente"
        {...mockCallbacks}
      />
    );

    expect(screen.getByText('Nenhum serviço encontrado')).toBeInTheDocument();
    expect(screen.getByText('Tente alterar os filtros para ver outros serviços.')).toBeInTheDocument();
  });

  it('deve aplicar opacidade para serviços inativos', () => {
    render(
      <ListaServicos 
        servicos={mockServicos}
        {...mockCallbacks}
      />
    );

    const cards = screen.getAllByRole('generic');
    const cardInativo = cards.find(card => 
      card.textContent?.includes('Massagem') && 
      card.className.includes('opacity-60')
    );

    expect(cardInativo).toBeInTheDocument();
  });

  it('deve renderizar descrição quando disponível', () => {
    render(
      <ListaServicos 
        servicos={mockServicos}
        {...mockCallbacks}
      />
    );

    expect(screen.getByText('Corte masculino moderno')).toBeInTheDocument();
    expect(screen.getByText('Aparar e modelar barba')).toBeInTheDocument();
    expect(screen.getByText('Massagem relaxante')).toBeInTheDocument();
  });

  it('deve manter performance com muitos serviços', () => {
    const muitosServicos = Array.from({ length: 100 }, (_, i) => ({
      ...mockServicos[0],
      servico_id: i + 1,
      nome_servico: `Serviço ${i + 1}`
    }));

    const startTime = performance.now();
    
    render(
      <ListaServicos 
        servicos={muitosServicos}
        {...mockCallbacks}
      />
    );

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // Renderização deve ser rápida (menos de 100ms)
    expect(renderTime).toBeLessThan(100);
  });
});
