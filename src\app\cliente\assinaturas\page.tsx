'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
   
  CreditCard, 
  Pause, 
  Play, 
  X, 
  Star,
  Clock,
  CheckCircle
} from 'lucide-react';
import { useAssinaturasCliente } from '@/hooks/useAssinaturasCliente';
import { PlanoAssinaturaExpandido } from '@/types/planosAssinatura';
import { formatCurrency } from '@/utils/formatters';

export default function AssinaturasClientePage() {
  const { 
    assinaturas, 
    loading, 
    error, 
    cancelarAssinatura, 
    pausarAssinatura, 
    reativarAssinatura,
    refresh 
  } = useAssinaturasCliente();

  const [assinaturaAcao, setAssinaturaAcao] = useState<number | null>(null);

  const assinaturasAtivas = assinaturas.filter(a => a.status === 'ativa' && a.ativo);
  const assinaturasInativas = assinaturas.filter(a => a.status !== 'ativa' || !a.ativo);

  const handleCancelar = async (assinatura: PlanoAssinaturaExpandido) => {
    const motivo = prompt('Motivo do cancelamento (opcional):');
    if (motivo !== null) { // null = cancelou o prompt
      setAssinaturaAcao(assinatura.plano_cliente_id);
      const sucesso = await cancelarAssinatura(assinatura.plano_cliente_id, motivo);
      setAssinaturaAcao(null);
      
      if (sucesso) {
        alert('Assinatura cancelada com sucesso!');
      }
    }
  };

  const handlePausar = async (assinatura: PlanoAssinaturaExpandido) => {
    if (confirm('Tem certeza que deseja pausar esta assinatura?')) {
      setAssinaturaAcao(assinatura.plano_cliente_id);
      const sucesso = await pausarAssinatura(assinatura.plano_cliente_id);
      setAssinaturaAcao(null);
      
      if (sucesso) {
        alert('Assinatura pausada com sucesso!');
      }
    }
  };

  const handleReativar = async (assinatura: PlanoAssinaturaExpandido) => {
    if (confirm('Tem certeza que deseja reativar esta assinatura?')) {
      setAssinaturaAcao(assinatura.plano_cliente_id);
      const sucesso = await reativarAssinatura(assinatura.plano_cliente_id);
      setAssinaturaAcao(null);
      
      if (sucesso) {
        alert('Assinatura reativada com sucesso!');
      }
    }
  };

  const calcularProgressoUso = (assinatura: PlanoAssinaturaExpandido) => {
    if (!assinatura.limite_usos_mes) return 0;
    return (assinatura.usos_consumidos_ciclo_atual / assinatura.limite_usos_mes) * 100;
  };

  const diasParaRenovacao = (dataRenovacao: string) => {
    const hoje = new Date();
    const renovacao = new Date(dataRenovacao);
    const diffTime = renovacao.getTime() - hoje.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p>Carregando suas assinaturas...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="text-center text-red-600">
              <p className="font-medium">Erro ao carregar assinaturas</p>
              <p className="text-sm mt-1">{error}</p>
              <Button 
                onClick={refresh} 
                variant="outline" 
                className="mt-4"
              >
                Tentar novamente
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Minhas Assinaturas</h1>
        <p className="text-gray-600 mt-1">
          Gerencie suas assinaturas mensais de serviços
        </p>
      </div>

      {/* Resumo */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Assinaturas Ativas</p>
                <p className="text-2xl font-bold">{assinaturasAtivas.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CreditCard className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Gasto Mensal</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(
                    assinaturasAtivas.reduce((total, a) => total + a.preco_mensal_assinatura, 0)
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Star className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600">Economia Total</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(
                    assinaturasAtivas.reduce((total, a) => {
                      const precoOriginal = a.servico?.preco || 0;
                      const desconto = precoOriginal * 0.2; // 20% de desconto
                      return total + desconto;
                    }, 0)
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="ativas" className="space-y-4">
        <TabsList>
          <TabsTrigger value="ativas">
            Ativas ({assinaturasAtivas.length})
          </TabsTrigger>
          <TabsTrigger value="inativas">
            Inativas ({assinaturasInativas.length})
          </TabsTrigger>
        </TabsList>

        {/* Assinaturas Ativas */}
        <TabsContent value="ativas" className="space-y-4">
          {assinaturasAtivas.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="text-gray-500">
                  <Star className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium mb-2">Nenhuma assinatura ativa</h3>
                  <p className="mb-4">
                    Explore os serviços disponíveis e assine planos mensais para economizar
                  </p>
                  <Button>
                    Explorar Serviços
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {assinaturasAtivas.map((assinatura) => (
                <Card key={assinatura.plano_cliente_id} className="relative">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">
                          {assinatura.nome_plano_empresa}
                        </CardTitle>
                        <CardDescription>
                          {assinatura.servico?.nome_servico} - {assinatura.empresa?.nome_empresa}
                        </CardDescription>
                      </div>
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        Ativa
                      </Badge>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Preço e Economia */}
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-2xl font-bold text-green-600">
                          {formatCurrency(assinatura.preco_mensal_assinatura)}/mês
                        </p>
                        <p className="text-sm text-gray-600">
                          Economia de 20% por agendamento
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-600">Próxima cobrança</p>
                        <p className="font-medium">
                          {new Date(assinatura.data_proxima_cobranca!).toLocaleDateString()}
                        </p>
                        <p className="text-xs text-gray-500">
                          {diasParaRenovacao(assinatura.data_proxima_cobranca!)} dias
                        </p>
                      </div>
                    </div>

                    {/* Uso do Plano */}
                    {assinatura.limite_usos_mes && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Usos este mês</span>
                          <span>
                            {assinatura.usos_consumidos_ciclo_atual} / {assinatura.limite_usos_mes}
                          </span>
                        </div>
                        <Progress 
                          value={calcularProgressoUso(assinatura)} 
                          className="h-2"
                        />
                        <p className="text-xs text-gray-600">
                          {assinatura.limite_usos_mes - assinatura.usos_consumidos_ciclo_atual} usos restantes
                        </p>
                      </div>
                    )}

                    {/* Benefícios */}
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Seus benefícios:</h4>
                      <ul className="text-xs text-gray-600 space-y-1">
                        <li className="flex items-center gap-2">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          20% de desconto em cada agendamento
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          Prioridade no agendamento
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          Cancelamento flexível
                        </li>
                        {assinatura.limite_usos_mes ? (
                          <li className="flex items-center gap-2">
                            <CheckCircle className="h-3 w-3 text-green-500" />
                            Até {assinatura.limite_usos_mes} usos por mês
                          </li>
                        ) : (
                          <li className="flex items-center gap-2">
                            <CheckCircle className="h-3 w-3 text-green-500" />
                            Usos ilimitados por mês
                          </li>
                        )}
                      </ul>
                    </div>

                    {/* Ações */}
                    <div className="flex gap-2 pt-4 border-t">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePausar(assinatura)}
                        disabled={assinaturaAcao === assinatura.plano_cliente_id}
                        className="flex items-center gap-1"
                      >
                        <Pause className="h-4 w-4" />
                        Pausar
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleCancelar(assinatura)}
                        disabled={assinaturaAcao === assinatura.plano_cliente_id}
                        className="flex items-center gap-1 text-red-600 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                        Cancelar
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Assinaturas Inativas */}
        <TabsContent value="inativas" className="space-y-4">
          {assinaturasInativas.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="text-gray-500">
                  <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium mb-2">Nenhuma assinatura inativa</h3>
                  <p>
                    Assinaturas pausadas ou canceladas aparecerão aqui
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {assinaturasInativas.map((assinatura) => (
                <Card key={assinatura.plano_cliente_id} className="opacity-75">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">
                          {assinatura.nome_plano_empresa}
                        </CardTitle>
                        <CardDescription>
                          {assinatura.servico?.nome_servico} - {assinatura.empresa?.nome_empresa}
                        </CardDescription>
                      </div>
                      <Badge 
                        variant={
                          assinatura.status === 'pausada' ? 'secondary' :
                          assinatura.status === 'cancelada' ? 'destructive' :
                          'outline'
                        }
                      >
                        {assinatura.status}
                      </Badge>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-lg font-medium text-gray-600">
                          {formatCurrency(assinatura.preco_mensal_assinatura)}/mês
                        </p>
                      </div>
                      {assinatura.data_cancelamento && (
                        <div className="text-right text-sm text-gray-600">
                          <p>Cancelada em</p>
                          <p>{new Date(assinatura.data_cancelamento).toLocaleDateString()}</p>
                        </div>
                      )}
                    </div>

                    {assinatura.motivo_cancelamento && (
                      <div className="text-sm text-gray-600">
                        <p className="font-medium">Motivo:</p>
                        <p>{assinatura.motivo_cancelamento}</p>
                      </div>
                    )}

                    {assinatura.status === 'pausada' && (
                      <div className="pt-4 border-t">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleReativar(assinatura)}
                          disabled={assinaturaAcao === assinatura.plano_cliente_id}
                          className="flex items-center gap-1"
                        >
                          <Play className="h-4 w-4" />
                          Reativar
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
