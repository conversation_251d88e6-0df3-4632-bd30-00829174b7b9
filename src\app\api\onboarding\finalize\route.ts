import { NextResponse } from 'next/server';
import { supabaseAdmin } from '@/utils/supabase/admin';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const {
      dadosEstabelecimento,
      servicosCadastrados,
      horariosComerciais,
      planoSelecionado,
      userId
    } = body;

    console.log('🚀 Iniciando finalização do onboarding via API...');

    // Validar dados obrigatórios
    if (!userId || !dadosEstabelecimento || !servicosCadastrados || !horariosComerciais || !planoSelecionado) {
      return NextResponse.json({
        success: false,
        error: 'Dados obrigatórios faltando'
      }, { status: 400 });
    }

    // Usar a classe administrativa para finalizar onboarding
    const result = await supabaseAdmin.finalizeOnboarding({
      userId,
      dadosEstabelecimento,
      servicosCadastrados,
      horariosComerciais,
      planoSelecionado
    });

    console.log('🎉 Onboarding finalizado com sucesso!');

    return NextResponse.json({
      success: true,
      message: 'Onboarding finalizado com sucesso!',
      data: result
    });

  } catch (error: any) {
    console.error('❌ Erro geral ao finalizar onboarding:', error);
    return NextResponse.json({
      success: false,
      error: 'Erro interno',
      details: error.message
    }, { status: 500 });
  }
}
