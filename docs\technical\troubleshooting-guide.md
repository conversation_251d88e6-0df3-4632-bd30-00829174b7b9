# 🔧 Guia de Resolução de Problemas - ServiceTech

## 📋 Índice

- [Problemas de Autenticação](#problemas-de-autenticação)
- [Problemas de Agendamento](#problemas-de-agendamento)
- [Problemas de Pagamento](#problemas-de-pagamento)
- [Problemas de Performance](#problemas-de-performance)
- [Problemas de Notificação](#problemas-de-notificação)
- [Problemas de Build/Deploy](#problemas-de-builddeploy)
- [Problemas de Banco de Dados](#problemas-de-banco-de-dados)
- [Logs e Monitoramento](#logs-e-monitoramento)

Para detalhes sobre a configuração correta de chaves de API, variáveis de ambiente e URLs de callback, consulte o [Guia de Deploy](deployment-guide.md). Para detalhes sobre os endpoints da API, consulte a [Documentação de APIs](api-documentation.md).

## 🔐 Problemas de Autenticação

### Usuário não consegue fazer login

#### Sintomas
- <PERSON>rro "Credenciais inválidas"
- Redirecionamento para página de erro
- Loop infinito de login

#### Diagnóstico
1. **Verificar dados do usuário no Supabase**:
```sql
SELECT id, email, email_confirmed_at, user_metadata 
FROM auth.users 
WHERE email = '<EMAIL>';
```

2. **Verificar políticas RLS**:
```sql
SELECT * FROM auth.users WHERE id = 'user_id';
```

3. **Verificar logs de autenticação**:
```javascript
// No console do navegador
console.log(await supabase.auth.getSession());
```

#### Soluções
1. **Email não confirmado**:
   - Reenviar email de confirmação
   - Verificar pasta de spam
   - Confirmar manualmente no Supabase (admin)

2. **Senha incorreta**:
   - Usar recuperação de senha
   - Reset manual pelo admin

3. **Problemas de RLS**:
   - Verificar políticas na tabela auth.users
   - Testar com cliente administrativo

### Magic Link não funciona

#### Sintomas
- Link não redireciona corretamente
- Erro 404 ao clicar no link
- Token expirado

#### Diagnóstico
1. **Verificar configuração de URLs no Supabase**:
   - Site URL: `https://servicetech.vercel.app`
   - Redirect URLs: `https://servicetech.vercel.app/auth/callback`

2. **Verificar callback handler**:
```typescript
// app/auth/callback/page.tsx
const { data, error } = await supabase.auth.exchangeCodeForSession(code);
```

#### Soluções
1. **URLs incorretas**: Atualizar no painel do Supabase
2. **Callback quebrado**: Verificar implementação
3. **Token expirado**: Gerar novo Magic Link

### Problemas de papéis/permissões

#### Sintomas
- Usuário não tem acesso a páginas específicas
- Erro 403 (Forbidden)
- Papel incorreto após login

#### Diagnóstico
1. **Verificar user_metadata**:
```sql
SELECT user_metadata FROM auth.users WHERE id = 'user_id';
```

2. **Verificar middleware**:
```typescript
// middleware.ts
const userRole = user?.user_metadata?.role;
console.log('User role:', userRole);
```

#### Soluções
1. **Papel incorreto**: Atualizar user_metadata
2. **Middleware**: Verificar lógica de redirecionamento
3. **RLS**: Ajustar políticas de acesso

## 📅 Problemas de Agendamento

### Horários não aparecem como disponíveis

#### Sintomas
- Calendário vazio
- Erro "Nenhum horário disponível"
- Horários incorretos

#### Diagnóstico
1. **Verificar horários da empresa**:
```sql
SELECT horario_funcionamento FROM empresas WHERE empresa_id = 1;
```

2. **Verificar colaboradores ativos**:
```sql
SELECT * FROM colaboradores_empresa 
WHERE empresa_id = 1 AND ativo = true AND ativo_como_prestador = true;
```

3. **Verificar API de disponibilidade**:
```bash
curl -X GET "/api/agendamentos/disponibilidade?empresa_id=1&servico_id=1&data=2025-01-27"
```

#### Soluções
1. **Horários não configurados**: Configurar horário de funcionamento
2. **Sem colaboradores**: Ativar pelo menos um colaborador
3. **Serviços não associados**: Associar serviços aos colaboradores
4. **Bloqueios**: Verificar bloqueios de horário

### Agendamento não é criado

#### Sintomas
- Erro ao finalizar agendamento
- Dados não salvos no banco
- Timeout na requisição

#### Diagnóstico
1. **Verificar logs da API**:
```bash
# Logs do Vercel
vercel logs --app=servicetech
```

2. **Verificar dados enviados**:
```javascript
// Network tab do navegador
console.log('Payload:', agendamentoData);
```

3. **Verificar políticas RLS**:
```sql
SELECT * FROM agendamentos WHERE cliente_user_id = 'user_id';
```

#### Soluções
1. **Dados inválidos**: Validar payload
2. **RLS**: Ajustar políticas de inserção
3. **Conflito de horário**: Verificar disponibilidade
4. **Timeout**: Otimizar queries

### Confirmação automática não funciona

#### Sintomas
- Agendamentos não são recusados após 24h
- Status não atualiza automaticamente
- Reembolsos não processados

#### Diagnóstico
1. **Verificar cron jobs** (se implementados)
2. **Verificar triggers do banco**:
```sql
SELECT * FROM pg_trigger WHERE tgname LIKE '%agendamento%';
```

3. **Verificar logs de webhook**:
```bash
# Verificar logs do Stripe
```

#### Soluções
1. **Implementar cron job**: Para verificação periódica (ex: usando Supabase Cron Jobs ou um serviço externo).
2. **Webhook**: Garanta que o endpoint do webhook (`/api/webhook/stripe`) esteja funcionando e processando os eventos corretamente. Veja a [Documentação de APIs](api-documentation.md#webhooks).
3. **Trigger**: Para certas lógicas, triggers no banco de dados podem ser uma solução. Consulte o [Esquema do Banco de Dados](../database_schema.md) para exemplos.

## 💳 Problemas de Pagamento

### Pagamentos Stripe falham

#### Sintomas
- Erro "Payment failed"
- Webhook não recebido
- Status não atualizado

#### Diagnóstico
1. **Verificar chaves do Stripe**:
```bash
# .env.local
STRIPE_SECRET_KEY=sk_test_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
```

2. **Verificar webhook endpoint**:
```bash
curl -X POST "/api/webhook/stripe" \
  -H "Stripe-Signature: ..." \
  -d "webhook_payload"
```

3. **Verificar logs do Stripe Dashboard**

#### Soluções
1. **Chaves incorretas**: Atualizar variáveis de ambiente
2. **Webhook não configurado**: Configurar no Stripe Dashboard
3. **Assinatura inválida**: Verificar validação de webhook
4. **Endpoint inacessível**: Verificar deploy

### Reembolsos não processados

#### Sintomas
- Reembolso não aparece no Stripe
- Status não atualizado
- Cliente não recebe valor

#### Diagnóstico
1. **Verificar API de reembolso**:
```bash
curl -X POST "/api/agendamentos/123/refund" \
  -H "Authorization: Bearer token"
```

2. **Verificar logs de reembolso**:
```sql
SELECT * FROM agendamentos WHERE agendamento_id = 123;
```

#### Soluções
1. **Payment Intent inválido**: Verificar ID do pagamento
2. **Valor incorreto**: Verificar cálculo de reembolso
3. **Stripe Connect**: Verificar configuração da conta

### Stripe Connect não funciona

#### Sintomas
- OAuth falha
- Conta não conecta
- Pagamentos não chegam ao proprietário

#### Diagnóstico
1. **Verificar configuração OAuth**:
```javascript
const authUrl = `https://connect.stripe.com/oauth/authorize?response_type=code&client_id=${clientId}&scope=read_write`;
```

2. **Verificar webhook de conta**:
```bash
# Verificar eventos account.updated
```

#### Soluções
1. **Client ID incorreto**: Verificar configuração
2. **Redirect URI**: Configurar corretamente
3. **Permissões**: Verificar scopes necessários

## ⚡ Problemas de Performance

### Páginas carregam lentamente

#### Sintomas
- Tempo de carregamento > 3s
- Timeout em requisições
- Interface travada

#### Diagnóstico
1. **Verificar métricas do Vercel**:
```bash
vercel analytics
```

2. **Verificar queries do banco**:
```sql
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
ORDER BY mean_exec_time DESC;
```

3. **Verificar bundle size**:
```bash
npm run build
# Verificar tamanho dos chunks
```

#### Soluções
1. **Otimizar queries**: Adicionar índices, reduzir JOINs
2. **Code splitting**: Dividir componentes grandes
3. **Caching**: Implementar cache de dados
4. **CDN**: Otimizar assets estáticos

### Banco de dados lento

#### Sintomas
- Queries demoram > 1s
- Timeout em operações
- Alto uso de CPU

#### Diagnóstico
1. **Verificar queries lentas**:
```sql
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
WHERE mean_exec_time > 1000
ORDER BY mean_exec_time DESC;
```

2. **Verificar índices**:
```sql
SELECT schemaname, tablename, indexname, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_tup_read DESC;
```

#### Soluções
1. **Criar índices**: Para colunas frequentemente consultadas
2. **Otimizar queries**: Reduzir complexidade
3. **Particionamento**: Para tabelas grandes
4. **Connection pooling**: Otimizar conexões

## 📧 Problemas de Notificação

### Emails não são enviados

#### Sintomas
- Usuários não recebem emails
- Erro na API de notificação
- Rate limit excedido

#### Diagnóstico
1. **Verificar configuração Resend**:
```bash
# .env.local
RESEND_API_KEY=re_...
```

2. **Verificar logs de email**:
```bash
curl -X GET "/api/notifications" \
  -H "Authorization: Bearer token"
```

3. **Verificar rate limits**:
```javascript
// Verificar headers de resposta
X-RateLimit-Remaining: 0
```

#### Soluções
1. **API key inválida**: Atualizar chave do Resend
2. **Template quebrado**: Verificar HTML do template
3. **Rate limit**: Implementar queue de emails
4. **Domínio não verificado**: Verificar no Resend

### SMS não funcionam

#### Sintomas
- SMS não chegam ao destinatário
- Erro de autenticação Twilio
- Número inválido

#### Diagnóstico
1. **Verificar configuração Twilio**:
```bash
# .env.local
TWILIO_ACCOUNT_SID=AC...
TWILIO_AUTH_TOKEN=...
TWILIO_PHONE_NUMBER=+1...
```

2. **Verificar formato do número**:
```javascript
// Deve estar no formato +*************
const phoneNumber = formatPhoneNumber(phone);
```

#### Soluções
1. **Credenciais incorretas**: Atualizar Twilio
2. **Número inválido**: Validar formato brasileiro
3. **Saldo insuficiente**: Verificar créditos Twilio
4. **Número não verificado**: Verificar no Twilio Console

### Push notifications não funcionam

#### Sintomas
- Notificações não aparecem
- Service Worker não registra
- Token FCM inválido

#### Diagnóstico
1. **Verificar Service Worker**:
```javascript
// Console do navegador
navigator.serviceWorker.getRegistrations();
```

2. **Verificar token FCM**:
```javascript
// Verificar se token foi gerado
console.log('FCM Token:', token);
```

3. **Verificar configuração Firebase**:
```bash
# .env.local
NEXT_PUBLIC_FIREBASE_VAPID_KEY=...
FIREBASE_ADMIN_PRIVATE_KEY=...
```

#### Soluções
1. **Service Worker**: Verificar registro e escopo
2. **Permissões**: Solicitar permissão do usuário
3. **Firebase config**: Verificar chaves e projeto
4. **HTTPS**: Push só funciona em HTTPS

## 🚀 Problemas de Build/Deploy

### Build falha no Vercel

#### Sintomas
- Erro de TypeScript
- Dependências não encontradas
- Timeout no build

#### Diagnóstico
1. **Verificar logs de build**:
```bash
vercel logs --app=servicetech
```

2. **Testar build local**:
```bash
npm run build
```

3. **Verificar dependências**:
```bash
npm audit
npm outdated
```

#### Soluções
1. **Erros TypeScript**: Corrija os erros de tipo e verifique os imports.
2. **Dependências**: Use `npm audit` ou `yarn audit` para verificar vulnerabilidades e atualize `package.json` se necessário.
3. **Memory limit**: Para builds no Vercel, pode ser necessário otimizar o uso de memória ou considerar um plano com limites maiores.
4. **Cache**: Limpe o cache de build no Vercel ou localmente (`.next/cache`).
5. Consulte o [Guia de Deploy](deployment-guide.md) para configurações de build corretas.

### Variáveis de ambiente não funcionam

#### Sintomas
- Erro "Environment variable not found"
- Funcionalidades não funcionam
- Conexões falham

#### Diagnóstico
1. **Verificar Vercel Dashboard**:
   - Environment Variables section
   - Verificar se estão definidas para produção

2. **Verificar prefixos**:
```bash
# Variáveis do cliente devem ter NEXT_PUBLIC_
NEXT_PUBLIC_SUPABASE_URL=...
# Variáveis do servidor não precisam
SUPABASE_SERVICE_ROLE_KEY=...
```

#### Soluções
1. **Adicionar no Vercel**: Via dashboard ou CLI
2. **Redeploy**: Após adicionar variáveis
3. **Prefixos**: Adicionar NEXT_PUBLIC_ quando necessário

## 🗄️ Problemas de Banco de Dados

### Políticas RLS não funcionam

#### Sintomas
- Usuários veem dados de outros.
- Erro "insufficient privileges" ao acessar ou modificar dados.
- Queries retornam vazio inesperadamente para certos usuários.
- Erro `infinite recursion detected in policy` durante operações de escrita que envolvem tabelas com RLS interdependentes.

#### Diagnóstico
1. **Verificar Definição das Políticas**:
   ```sql
   SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
   FROM pg_policies
   WHERE schemaname = 'public' AND tablename = 'nome_da_tabela';
   ```
2. **Testar com Usuário Específico**: Simule o papel e UID do usuário para testar a política.
   ```sql
   -- Exemplo:
   -- SET ROLE authenticated;
   -- SET request.jwt.claims = '{"role": "Proprietario", "sub": "user_id_aqui"}';
   SELECT * FROM nome_da_tabela;
   -- RESET ROLE;
   ```
3. **Verificar `auth.uid()` e `auth.role()`**: Garanta que estas funções estão sendo usadas corretamente nas políticas e que os metadados do usuário (`user_metadata.role`) estão corretos.
   ```sql
   SELECT auth.uid(), auth.role();
   -- Verifique o user_metadata no painel do Supabase ou com uma query de admin:
   -- SELECT id, raw_user_meta_data FROM auth.users WHERE id = 'user_id_aqui';
   ```
4. **Analisar Recursão**: Se houver erro de recursão, revise as políticas das tabelas envolvidas na query para identificar dependências circulares. Por exemplo, uma política na tabela A que consulta a tabela B, enquanto uma política na tabela B consulta a tabela A.

#### Soluções
1. **Criar ou Corrigir Políticas**: Garanta que as políticas RLS cubram todas as operações (SELECT, INSERT, UPDATE, DELETE) e todos os papéis de usuário relevantes. As condições `USING` (para acesso) e `WITH CHECK` (para escrita) devem ser precisas.
2. **Simplificar Condições**: Evite joins excessivamente complexos ou subqueries dentro das definições de políticas RLS se possível. Use funções SQL `SECURITY DEFINER` para encapsular lógica complexa quando necessário.
3. **Habilitar RLS**: Confirme que RLS está habilitada para a tabela: `ALTER TABLE nome_da_tabela ENABLE ROW LEVEL SECURITY;`.
4. **Arquitetura Híbrida para Casos de Recursão**: Para problemas persistentes de recursão em operações de sistema (como finalização de onboarding ou processamento de webhooks), considere usar um cliente Supabase com `service_role_key` que bypassa RLS para essas operações específicas. Veja a discussão sobre "Arquitetura Híbrida" no [Histórico de Implementação](../historico.md#problema-crítico-e-solução-recursão-infinita-em-políticas-rls-e-arquitetura-híbrida-janeiro-2025).
5. **Ordem de Operações**: Em alguns casos, alterar a ordem das operações em uma transação pode evitar conflitos de RLS.

### Migrações falham

#### Sintomas
- Erro ao executar SQL
- Tabelas não criadas
- Constraints violadas

#### Diagnóstico
1. **Verificar ordem de execução**
2. **Verificar dependências entre tabelas**
3. **Verificar dados existentes**

#### Soluções
1. **Ordem correta**: Executar em sequência
2. **Rollback**: Reverter mudanças problemáticas
3. **Backup**: Sempre fazer backup antes

## 📊 Logs e Monitoramento

### Como acessar logs

#### Vercel Logs
```bash
vercel logs --app=servicetech --since=1h
```

#### Supabase Logs
- Dashboard > Logs > API/Database

#### Browser Console
```javascript
// Habilitar debug
localStorage.setItem('debug', 'servicetech:*');
```

### Monitoramento de Performance

#### Métricas importantes
- **Response time**: < 200ms para APIs
- **Error rate**: < 1%
- **Uptime**: > 99.9%
- **Database connections**: < 80% do limite

#### Ferramentas
- **Vercel Analytics**: Performance de páginas
- **Supabase Dashboard**: Métricas de banco
- **Sentry**: Monitoramento de erros
- **Custom monitoring**: `/admin/monitoring`

### Alertas automáticos

#### Configurar alertas para:
- Erro rate > 5%
- Response time > 1s
- Database connections > 90%
- Disk usage > 80%
- Failed payments > 10/hour

---

**ServiceTech Troubleshooting** - Resolva problemas rapidamente 🔧
