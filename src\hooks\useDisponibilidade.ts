import { useState, useCallback } from 'react';
import { 
  ParametrosDisponibilidade,
  HorarioDisponivel,
  ColaboradorDisponivel,
  DisponibilidadeResponse
} from '@/types/disponibilidade';

interface EstadoDisponibilidade {
  horariosDisponiveis: HorarioDisponivel[];
  colaboradoresDisponiveis: ColaboradorDisponivel[];
  loading: boolean;
  error: string | null;
  parametrosAtuais: ParametrosDisponibilidade | null;
}

export function useDisponibilidade() {
  const [estado, setEstado] = useState<EstadoDisponibilidade>({
    horariosDisponiveis: [],
    colaboradoresDisponiveis: [],
    loading: false,
    error: null,
    parametrosAtuais: null
  });

  // Buscar disponibilidade
  const buscarDisponibilidade = useCallback(async (parametros: ParametrosDisponibilidade) => {
    setEstado(prev => ({ ...prev, loading: true, error: null, parametrosAtuais: parametros }));

    try {
      const params = new URLSearchParams({
        empresa_id: parametros.empresa_id.toString(),
        servico_id: parametros.servico_id.toString(),
        data_inicio: parametros.data_inicio,
        data_fim: parametros.data_fim
      });

      if (parametros.colaborador_user_id) {
        params.append('colaborador_user_id', parametros.colaborador_user_id);
      }

      if (parametros.incluir_ocupados) {
        params.append('incluir_ocupados', 'true');
      }

      const response = await fetch(`/api/agendamentos/disponibilidade?${params.toString()}`);
      const result: DisponibilidadeResponse = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao buscar disponibilidade');
      }

      if (result.success && result.data) {
        setEstado(prev => ({
          ...prev,
          horariosDisponiveis: result.data!.horarios_disponiveis || [],
          colaboradoresDisponiveis: result.data!.colaboradores_disponiveis || [],
          loading: false
        }));
      } else {
        throw new Error(result.error || 'Dados de disponibilidade inválidos');
      }

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        error: error.message,
        loading: false,
        horariosDisponiveis: [],
        colaboradoresDisponiveis: []
      }));
    }
  }, []);

  // Buscar disponibilidade para um período específico
  const buscarDisponibilidadePeriodo = useCallback(async (
    empresa_id: number,
    servico_id: number,
    dataInicio: Date,
    dataFim: Date,
    colaborador_user_id?: string
  ) => {
    const parametros: ParametrosDisponibilidade = {
      empresa_id,
      servico_id,
      data_inicio: dataInicio.toISOString().split('T')[0],
      data_fim: dataFim.toISOString().split('T')[0],
      colaborador_user_id
    };

    await buscarDisponibilidade(parametros);
  }, [buscarDisponibilidade]);

  // Buscar disponibilidade para próximos 7 dias
  const buscarDisponibilidadeProximosDias = useCallback(async (
    empresa_id: number,
    servico_id: number,
    dias: number = 7,
    colaborador_user_id?: string
  ) => {
    const hoje = new Date();
    const dataFim = new Date();
    dataFim.setDate(hoje.getDate() + dias);

    await buscarDisponibilidadePeriodo(empresa_id, servico_id, hoje, dataFim, colaborador_user_id);
  }, [buscarDisponibilidadePeriodo]);

  // Filtrar horários por colaborador
  const filtrarPorColaborador = useCallback((colaborador_user_id?: string) => {
    if (!colaborador_user_id) {
      return estado.horariosDisponiveis;
    }

    return estado.horariosDisponiveis.filter(
      horario => horario.colaborador_user_id === colaborador_user_id
    );
  }, [estado.horariosDisponiveis]);

  // Agrupar horários por data
  const agruparPorData = useCallback((horarios?: HorarioDisponivel[]) => {
    const horariosParaAgrupar = horarios || estado.horariosDisponiveis;
    
    return horariosParaAgrupar.reduce((grupos, horario) => {
      const data = horario.data_hora_inicio.split('T')[0];
      if (!grupos[data]) {
        grupos[data] = [];
      }
      grupos[data].push(horario);
      return grupos;
    }, {} as Record<string, HorarioDisponivel[]>);
  }, [estado.horariosDisponiveis]);

  // Agrupar horários por colaborador
  const agruparPorColaborador = useCallback((horarios?: HorarioDisponivel[]) => {
    const horariosParaAgrupar = horarios || estado.horariosDisponiveis;
    
    return horariosParaAgrupar.reduce((grupos, horario) => {
      const colaboradorId = horario.colaborador_user_id || 'sem_colaborador';
      if (!grupos[colaboradorId]) {
        grupos[colaboradorId] = [];
      }
      grupos[colaboradorId].push(horario);
      return grupos;
    }, {} as Record<string, HorarioDisponivel[]>);
  }, [estado.horariosDisponiveis]);

  // Obter próximos horários disponíveis
  const obterProximosHorarios = useCallback((limite: number = 10) => {
    const agora = new Date();
    
    return estado.horariosDisponiveis
      .filter(horario => new Date(horario.data_hora_inicio) > agora)
      .sort((a, b) => new Date(a.data_hora_inicio).getTime() - new Date(b.data_hora_inicio).getTime())
      .slice(0, limite);
  }, [estado.horariosDisponiveis]);

  // Verificar se um horário específico está disponível
  const verificarHorarioDisponivel = useCallback((
    data_hora_inicio: string,
    colaborador_user_id?: string
  ) => {
    return estado.horariosDisponiveis.some(horario => 
      horario.data_hora_inicio === data_hora_inicio &&
      (!colaborador_user_id || horario.colaborador_user_id === colaborador_user_id) &&
      horario.disponivel
    );
  }, [estado.horariosDisponiveis]);

  // Obter estatísticas de disponibilidade
  const obterEstatisticas = useCallback(() => {
    const total = estado.horariosDisponiveis.length;
    const disponiveis = estado.horariosDisponiveis.filter(h => h.disponivel).length;
    const ocupados = total - disponiveis;

    const porColaborador = estado.colaboradoresDisponiveis.map(colaborador => ({
      colaborador_user_id: colaborador.colaborador_user_id,
      nome: colaborador.name,
      total_horarios: colaborador.horarios_disponiveis.length,
      horarios_disponiveis: colaborador.horarios_disponiveis.filter(h => h.disponivel).length
    }));

    const horariosAgrupados = agruparPorData();
    const porData = Object.entries(horariosAgrupados).map(([data, horarios]) => ({
      data,
      total_horarios: horarios.length,
      horarios_disponiveis: horarios.filter(h => h.disponivel).length
    }));

    return {
      total,
      disponiveis,
      ocupados,
      taxa_disponibilidade: total > 0 ? (disponiveis / total) * 100 : 0,
      por_colaborador: porColaborador,
      por_data: porData
    };
  }, [estado.horariosDisponiveis, estado.colaboradoresDisponiveis, agruparPorData]);

  // Limpar dados
  const limpar = useCallback(() => {
    setEstado({
      horariosDisponiveis: [],
      colaboradoresDisponiveis: [],
      loading: false,
      error: null,
      parametrosAtuais: null
    });
  }, []);

  // Limpar erro
  const limparErro = useCallback(() => {
    setEstado(prev => ({ ...prev, error: null }));
  }, []);

  return {
    // Estado
    horariosDisponiveis: estado.horariosDisponiveis,
    colaboradoresDisponiveis: estado.colaboradoresDisponiveis,
    loading: estado.loading,
    error: estado.error,
    parametrosAtuais: estado.parametrosAtuais,

    // Ações
    buscarDisponibilidade,
    buscarDisponibilidadePeriodo,
    buscarDisponibilidadeProximosDias,

    // Utilitários
    filtrarPorColaborador,
    agruparPorData,
    agruparPorColaborador,
    obterProximosHorarios,
    verificarHorarioDisponivel,
    obterEstatisticas,
    limpar,
    limparErro
  };
}
