import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { ResultadoAplicacaoCupom } from '@/types/marketing';

// POST - Validar e aplicar cupom
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { codigo_cupom, empresa_id, servicos_ids, valor_total } = await request.json();

    // Validações básicas
    if (!codigo_cupom || !empresa_id || !servicos_ids || !valor_total) {
      return NextResponse.json({ 
        success: false, 
        error: 'Dados obrigatórios: codigo_cupom, empresa_id, servicos_ids, valor_total' 
      }, { status: 400 });
    }

    // Buscar cupom
    const { data: cupom, error: cupomError } = await supabase
      .from('cupons')
      .select('*')
      .eq('codigo_cupom', codigo_cupom.toUpperCase())
      .eq('empresa_id', empresa_id)
      .single();

    if (cupomError || !cupom) {
      return NextResponse.json({
        success: true,
        data: {
          cupom_aplicado: false,
          valor_original: valor_total,
          valor_desconto: 0,
          valor_final: valor_total,
          economia: 0,
          erro: 'Cupom não encontrado ou inválido'
        } as ResultadoAplicacaoCupom
      });
    }

    // Verificar validade do cupom
    const agora = new Date();
    const dataInicio = new Date(cupom.data_inicio);
    const dataFim = new Date(cupom.data_fim);

    if (!cupom.ativo) {
      return NextResponse.json({
        success: true,
        data: {
          cupom_aplicado: false,
          valor_original: valor_total,
          valor_desconto: 0,
          valor_final: valor_total,
          economia: 0,
          erro: 'Cupom inativo'
        } as ResultadoAplicacaoCupom
      });
    }

    if (agora < dataInicio) {
      return NextResponse.json({
        success: true,
        data: {
          cupom_aplicado: false,
          valor_original: valor_total,
          valor_desconto: 0,
          valor_final: valor_total,
          economia: 0,
          erro: 'Cupom ainda não está válido'
        } as ResultadoAplicacaoCupom
      });
    }

    if (agora > dataFim) {
      return NextResponse.json({
        success: true,
        data: {
          cupom_aplicado: false,
          valor_original: valor_total,
          valor_desconto: 0,
          valor_final: valor_total,
          economia: 0,
          erro: 'Cupom expirado'
        } as ResultadoAplicacaoCupom
      });
    }

    // Verificar limite de usos total
    if (cupom.limite_usos_total && cupom.usos_realizados >= cupom.limite_usos_total) {
      return NextResponse.json({
        success: true,
        data: {
          cupom_aplicado: false,
          valor_original: valor_total,
          valor_desconto: 0,
          valor_final: valor_total,
          economia: 0,
          erro: 'Cupom esgotado'
        } as ResultadoAplicacaoCupom
      });
    }

    // Verificar valor mínimo do pedido
    if (cupom.valor_minimo_pedido && valor_total < cupom.valor_minimo_pedido) {
      return NextResponse.json({
        success: true,
        data: {
          cupom_aplicado: false,
          valor_original: valor_total,
          valor_desconto: 0,
          valor_final: valor_total,
          economia: 0,
          erro: `Valor mínimo do pedido: R$ ${cupom.valor_minimo_pedido.toFixed(2)}`
        } as ResultadoAplicacaoCupom
      });
    }

    // Verificar serviços aplicáveis
    if (cupom.aplicavel_servicos && cupom.aplicavel_servicos.length > 0) {
      const servicosAplicaveis = servicos_ids.some((id: number) => 
        cupom.aplicavel_servicos!.includes(id)
      );
      
      if (!servicosAplicaveis) {
        return NextResponse.json({
          success: true,
          data: {
            cupom_aplicado: false,
            valor_original: valor_total,
            valor_desconto: 0,
            valor_final: valor_total,
            economia: 0,
            erro: 'Cupom não aplicável aos serviços selecionados'
          } as ResultadoAplicacaoCupom
        });
      }
    }

    // Calcular desconto
    let valorDesconto = 0;
    if (cupom.tipo_desconto === 'valor_fixo') {
      valorDesconto = Math.min(cupom.valor_desconto, valor_total);
    } else if (cupom.tipo_desconto === 'percentual') {
      valorDesconto = (valor_total * cupom.valor_desconto) / 100;
    }

    const valorFinal = Math.max(0, valor_total - valorDesconto);
    const economia = valor_total - valorFinal;

    return NextResponse.json({
      success: true,
      data: {
        cupom_aplicado: true,
        cupom,
        valor_original: valor_total,
        valor_desconto: valorDesconto,
        valor_final: valorFinal,
        economia
      } as ResultadoAplicacaoCupom
    });

  } catch (error) {
    console.error('Erro na validação de cupom:', error);
    return NextResponse.json({ success: false, error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// GET - Verificar limite de uso por cliente
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    const codigo_cupom = searchParams.get('codigo_cupom');
    const cliente_id = searchParams.get('cliente_id');
    const empresa_id = searchParams.get('empresa_id');

    if (!codigo_cupom || !cliente_id || !empresa_id) {
      return NextResponse.json({ 
        success: false, 
        error: 'Parâmetros obrigatórios: codigo_cupom, cliente_id, empresa_id' 
      }, { status: 400 });
    }

    // Buscar cupom
    const { data: cupom, error: cupomError } = await supabase
      .from('cupons')
      .select('cupom_id, limite_usos_por_cliente')
      .eq('codigo_cupom', codigo_cupom.toUpperCase())
      .eq('empresa_id', empresa_id)
      .single();

    if (cupomError || !cupom) {
      return NextResponse.json({
        success: true,
        data: {
          pode_usar: false,
          motivo: 'Cupom não encontrado'
        }
      });
    }

    // Contar usos do cliente
    const { count: usosCliente, error: usosError } = await supabase
      .from('agendamentos')
      .select('*', { count: 'exact', head: true })
      .eq('cliente_user_id', cliente_id)
      .eq('cupom_id', cupom.cupom_id)
      .neq('status_agendamento', 'cancelado');

    if (usosError) {
      console.error('Erro ao contar usos do cliente:', usosError);
      return NextResponse.json({ success: false, error: 'Erro ao verificar usos' }, { status: 500 });
    }

    const podeUsar = !cupom.limite_usos_por_cliente || (usosCliente || 0) < cupom.limite_usos_por_cliente;
    const usosRestantes = cupom.limite_usos_por_cliente ? cupom.limite_usos_por_cliente - (usosCliente || 0) : null;

    return NextResponse.json({
      success: true,
      data: {
        pode_usar: podeUsar,
        usos_realizados: usosCliente || 0,
        limite_usos_por_cliente: cupom.limite_usos_por_cliente,
        usos_restantes: usosRestantes,
        motivo: podeUsar ? null : 'Limite de usos por cliente atingido'
      }
    });

  } catch (error) {
    console.error('Erro na verificação de limite de uso:', error);
    return NextResponse.json({ success: false, error: 'Erro interno do servidor' }, { status: 500 });
  }
}
