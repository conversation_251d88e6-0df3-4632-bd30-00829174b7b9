import { useState, useEffect, useCallback } from 'react';
import { 
  CampanhaMarketing, 
  CriarCampanhaData, 
  FiltrosCampanhas, 
  RespostaCampanhas,
  ResultadoSegmentacao,
  SegmentacaoCampanha
} from '@/types/marketing';

interface UseCampanhasReturn {
  // Estado
  campanhas: CampanhaMarketing[];
  carregando: boolean;
  erro: string | null;
  total: number;
  paginaAtual: number;
  totalPaginas: number;

  // Ações
  buscarCampanhas: (filtros?: FiltrosCampanhas, pagina?: number) => Promise<void>;
  criarCampanha: (dados: CriarCampanhaData) => Promise<boolean>;
  atualizarCampanha: (id: number, dados: Partial<CriarCampanhaData>) => Promise<boolean>;
  excluirCampanha: (id: number) => Promise<boolean>;
  enviarCampanha: (id: number) => Promise<boolean>;
  cancelarCampanha: (id: number) => Promise<boolean>;
  segmentarClientes: (segmentacao: SegmentacaoCampanha) => Promise<ResultadoSegmentacao | null>;
  buscarCampanhaPorId: (id: number) => Promise<CampanhaMarketing | null>;
  
  // Utilitários
  limparErro: () => void;
  recarregar: () => Promise<void>;
}

export function useCampanhas(): UseCampanhasReturn {
  const [campanhas, setCampanhas] = useState<CampanhaMarketing[]>([]);
  const [carregando, setCarregando] = useState(false);
  const [erro, setErro] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [paginaAtual, setPaginaAtual] = useState(1);
  const [totalPaginas, setTotalPaginas] = useState(0);
  const [filtrosAtuais, setFiltrosAtuais] = useState<FiltrosCampanhas>({});

  // Buscar campanhas
  const buscarCampanhas = useCallback(async (filtros: FiltrosCampanhas = {}, pagina: number = 1) => {
    try {
      setCarregando(true);
      setErro(null);

      const params = new URLSearchParams();
      
      if (filtros.busca) params.append('busca', filtros.busca);
      if (filtros.status && filtros.status !== 'todos') params.append('status', filtros.status);
      if (filtros.tipo_campanha && filtros.tipo_campanha !== 'todos') params.append('tipo_campanha', filtros.tipo_campanha);
      if (filtros.data_inicio) params.append('data_inicio', filtros.data_inicio);
      if (filtros.data_fim) params.append('data_fim', filtros.data_fim);
      
      params.append('pagina', pagina.toString());
      params.append('limite', '10');

      const response = await fetch(`/api/campanhas?${params.toString()}`);
      const resultado: RespostaCampanhas = await response.json();

      if (!resultado.success) {
        throw new Error(resultado.error || 'Erro ao buscar campanhas');
      }

      if (resultado.data) {
        setCampanhas(resultado.data.campanhas);
        setTotal(resultado.data.total);
        setPaginaAtual(resultado.data.pagina);
        setTotalPaginas(resultado.data.total_paginas);
        setFiltrosAtuais(filtros);
      }

    } catch (error) {
      console.error('Erro ao buscar campanhas:', error);
      setErro(error instanceof Error ? error.message : 'Erro desconhecido');
      setCampanhas([]);
    } finally {
      setCarregando(false);
    }
  }, []);

  // Criar campanha
  const criarCampanha = useCallback(async (dados: CriarCampanhaData): Promise<boolean> => {
    try {
      setCarregando(true);
      setErro(null);

      const response = await fetch('/api/campanhas', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dados),
      });

      const resultado = await response.json();

      if (!resultado.success) {
        throw new Error(resultado.error || 'Erro ao criar campanha');
      }

      // Recarregar lista
      await buscarCampanhas(filtrosAtuais, paginaAtual);
      return true;

    } catch (error) {
      console.error('Erro ao criar campanha:', error);
      setErro(error instanceof Error ? error.message : 'Erro desconhecido');
      return false;
    } finally {
      setCarregando(false);
    }
  }, [buscarCampanhas, filtrosAtuais, paginaAtual]);

  // Atualizar campanha
  const atualizarCampanha = useCallback(async (id: number, dados: Partial<CriarCampanhaData>): Promise<boolean> => {
    try {
      setCarregando(true);
      setErro(null);

      const response = await fetch(`/api/campanhas/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dados),
      });

      const resultado = await response.json();

      if (!resultado.success) {
        throw new Error(resultado.error || 'Erro ao atualizar campanha');
      }

      // Atualizar campanha na lista local
      setCampanhas(prev => prev.map(campanha => 
        campanha.campanha_id === id ? { ...campanha, ...dados } : campanha
      ));

      return true;

    } catch (error) {
      console.error('Erro ao atualizar campanha:', error);
      setErro(error instanceof Error ? error.message : 'Erro desconhecido');
      return false;
    } finally {
      setCarregando(false);
    }
  }, []);

  // Excluir campanha
  const excluirCampanha = useCallback(async (id: number): Promise<boolean> => {
    try {
      setCarregando(true);
      setErro(null);

      const response = await fetch(`/api/campanhas/${id}`, {
        method: 'DELETE',
      });

      const resultado = await response.json();

      if (!resultado.success) {
        throw new Error(resultado.error || 'Erro ao excluir campanha');
      }

      // Remover campanha da lista local
      setCampanhas(prev => prev.filter(campanha => campanha.campanha_id !== id));
      setTotal(prev => prev - 1);

      return true;

    } catch (error) {
      console.error('Erro ao excluir campanha:', error);
      setErro(error instanceof Error ? error.message : 'Erro desconhecido');
      return false;
    } finally {
      setCarregando(false);
    }
  }, []);

  // Enviar campanha
  const enviarCampanha = useCallback(async (id: number): Promise<boolean> => {
    try {
      setCarregando(true);
      setErro(null);

      const response = await fetch(`/api/campanhas/${id}/enviar`, {
        method: 'POST',
      });

      const resultado = await response.json();

      if (!resultado.success) {
        throw new Error(resultado.error || 'Erro ao enviar campanha');
      }

      // Atualizar status da campanha
      setCampanhas(prev => prev.map(campanha => 
        campanha.campanha_id === id 
          ? { ...campanha, status: 'enviando' as const }
          : campanha
      ));

      return true;

    } catch (error) {
      console.error('Erro ao enviar campanha:', error);
      setErro(error instanceof Error ? error.message : 'Erro desconhecido');
      return false;
    } finally {
      setCarregando(false);
    }
  }, []);

  // Cancelar campanha
  const cancelarCampanha = useCallback(async (id: number): Promise<boolean> => {
    try {
      setCarregando(true);
      setErro(null);

      const response = await fetch(`/api/campanhas/${id}/cancelar`, {
        method: 'POST',
      });

      const resultado = await response.json();

      if (!resultado.success) {
        throw new Error(resultado.error || 'Erro ao cancelar campanha');
      }

      // Atualizar status da campanha
      setCampanhas(prev => prev.map(campanha => 
        campanha.campanha_id === id 
          ? { ...campanha, status: 'cancelada' as const }
          : campanha
      ));

      return true;

    } catch (error) {
      console.error('Erro ao cancelar campanha:', error);
      setErro(error instanceof Error ? error.message : 'Erro desconhecido');
      return false;
    } finally {
      setCarregando(false);
    }
  }, []);

  // Segmentar clientes
  const segmentarClientes = useCallback(async (segmentacao: SegmentacaoCampanha): Promise<ResultadoSegmentacao | null> => {
    try {
      setCarregando(true);
      setErro(null);

      const response = await fetch('/api/campanhas/segmentacao', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ segmentacao }),
      });

      const resultado = await response.json();

      if (!resultado.success) {
        throw new Error(resultado.error || 'Erro ao segmentar clientes');
      }

      return resultado.data;

    } catch (error) {
      console.error('Erro ao segmentar clientes:', error);
      setErro(error instanceof Error ? error.message : 'Erro desconhecido');
      return null;
    } finally {
      setCarregando(false);
    }
  }, []);

  // Buscar campanha por ID
  const buscarCampanhaPorId = useCallback(async (id: number): Promise<CampanhaMarketing | null> => {
    try {
      const response = await fetch(`/api/campanhas/${id}`);
      const resultado = await response.json();

      if (!resultado.success) {
        throw new Error(resultado.error || 'Erro ao buscar campanha');
      }

      return resultado.data;

    } catch (error) {
      console.error('Erro ao buscar campanha por ID:', error);
      setErro(error instanceof Error ? error.message : 'Erro desconhecido');
      return null;
    }
  }, []);

  // Limpar erro
  const limparErro = useCallback(() => {
    setErro(null);
  }, []);

  // Recarregar
  const recarregar = useCallback(async () => {
    await buscarCampanhas(filtrosAtuais, paginaAtual);
  }, [buscarCampanhas, filtrosAtuais, paginaAtual]);

  // Carregar campanhas iniciais
  useEffect(() => {
    buscarCampanhas();
  }, [buscarCampanhas]);

  return {
    // Estado
    campanhas,
    carregando,
    erro,
    total,
    paginaAtual,
    totalPaginas,

    // Ações
    buscarCampanhas,
    criarCampanha,
    atualizarCampanha,
    excluirCampanha,
    enviarCampanha,
    cancelarCampanha,
    segmentarClientes,
    buscarCampanhaPorId,

    // Utilitários
    limparErro,
    recarregar,
  };
}
