"use client";

import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import React, { ComponentPropsWithoutRef } from "react";

export interface InteractiveHoverButtonProps extends ComponentPropsWithoutRef<"button"> {
  text?: string;
  className?: string;
  children?: React.ReactNode;
}

export const InteractiveHoverButton = React.forwardRef<
  HTMLButtonElement,
  InteractiveHoverButtonProps
>(({ text, className, children, ...props }, ref) => {
  // Remove propriedades conflitantes do framer-motion
  const {
    onDrag,
    onDragStart,
    onDragEnd,
    onAnimationStart,
    onAnimationEnd,
    onTransitionEnd,
    ...safeProps
  } = props;
  return (
    <motion.button
      ref={ref}
      className={cn(
        "group relative w-32 cursor-pointer overflow-hidden rounded-md border border-neutral-200 bg-transparent p-2 text-center font-semibold",
        className,
      )}
      whileHover="hover"
      whileTap="tap"
      variants={{
        hover: { scale: 1.05 },
        tap: { scale: 0.95 },
      }}
      {...safeProps}
    >
      <span className="relative z-10 text-neutral-700 transition-colors duration-300 group-hover:text-white dark:text-neutral-300">
        {children || text}
      </span>
      <motion.div
        className="absolute inset-0 z-0 rounded-md bg-gradient-to-r from-blue-500 to-purple-500"
        initial={{ scale: 0, opacity: 0 }}
        variants={{
          hover: {
            scale: 1,
            opacity: 1,
            transition: { duration: 0.3, ease: "easeOut" },
          },
        }}
      />
      <motion.div
        className="absolute inset-0 z-0 rounded-md bg-gradient-to-r from-blue-400 to-purple-400 opacity-0"
        variants={{
          hover: {
            opacity: 0.8,
            transition: { duration: 0.3, ease: "easeOut" },
          },
        }}
      />
    </motion.button>
  );
});

InteractiveHoverButton.displayName = "InteractiveHoverButton";
