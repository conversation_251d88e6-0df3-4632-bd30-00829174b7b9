'use client';

import React from 'react';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { LogoEmpresa } from '@/components/ui/LogoEmpresa';
import { useEmpresaProprietario } from '@/hooks/useEmpresaProprietario';

interface InformacoesEmpresaProps {
  className?: string;
}

export function InformacoesEmpresa({ className = '' }: InformacoesEmpresaProps) {
  const { 
    empresa, 
    planoSaas, 
    statusConfiguracao, 
    loading, 
    error,
    temEmpresa,
    empresaAtiva,
    podeReceberPagamentos,
    precisaConfiguracaoInicial,
    obterProximosPassos
  } = useEmpresaProprietario();

  if (loading) {
    return (
      <Card className={`animate-pulse ${className}`}>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gray-200 rounded-lg"></div>
            <div className="flex-1 space-y-2">
              <div className="h-6 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`border-red-200 bg-red-50 ${className}`}>
        <CardContent className="p-6">
          <div className="flex items-center gap-3">
            <div className="text-red-600">⚠️</div>
            <div>
              <div className="font-semibold text-red-800">Erro ao carregar dados da empresa</div>
              <div className="text-sm text-red-700">{error}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!temEmpresa) {
    return (
      <Card className={`border-yellow-200 bg-yellow-50 ${className}`}>
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <div className="text-yellow-600 text-4xl">🏢</div>
            <div>
              <h3 className="font-semibold text-yellow-800 text-lg">Empresa não encontrada</h3>
              <p className="text-sm text-yellow-700 mt-2">
                Você precisa criar uma empresa para acessar o dashboard do proprietário.
              </p>
            </div>
            <Link href="/onboarding">
              <Button className="bg-yellow-600 hover:bg-yellow-700">
                Criar Empresa
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    );
  }

  const proximosPassos = obterProximosPassos();
  const precisaConfiguracao = precisaConfiguracaoInicial();

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Card principal da empresa */}
      <Card className="overflow-hidden">
        <CardContent className="p-6">
          <div className="flex items-start space-x-4">
            {/* Logo da empresa */}
            <div className="flex-shrink-0">
              <LogoEmpresa
                logoUrl={empresa?.logo_url}
                nomeEmpresa={empresa?.nome_empresa || ''}
                width={64}
                height={64}
                className="rounded-lg border border-[var(--border-color)]"
              />
            </div>

            {/* Informações principais */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div>
                  <h2 className="text-xl font-bold text-[var(--text-primary)] truncate">
                    {empresa?.nome_empresa}
                  </h2>
                  {empresa?.segmento && (
                    <p className="text-sm text-[var(--text-secondary)] mt-1">
                      {empresa.segmento}
                    </p>
                  )}
                  {empresa?.endereco_completo && (
                    <p className="text-sm text-[var(--text-secondary)] mt-1 truncate">
                      📍 {empresa.endereco_completo}
                    </p>
                  )}
                </div>

                {/* Status da empresa */}
                <div className="flex flex-col items-end space-y-2">
                  <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                    empresaAtiva 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {empresaAtiva ? '✅ Ativa' : '❌ Inativa'}
                  </div>
                  
                  {/* Status dos pagamentos */}
                  <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                    podeReceberPagamentos() 
                      ? 'bg-blue-100 text-blue-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {podeReceberPagamentos() ? '💳 Pagamentos OK' : '💳 Pagamentos Pendentes'}
                  </div>
                </div>
              </div>

              {/* Informações do plano */}
              {planoSaas && (
                <div className="mt-4 p-3 bg-[var(--surface-hover)] rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="text-sm font-medium text-[var(--text-primary)]">
                        Plano {planoSaas.nome_plano}
                      </span>
                      <span className="text-xs text-[var(--text-secondary)] ml-2">
                        R$ {planoSaas.preco_mensal}/mês
                      </span>
                    </div>
                    <div className="text-xs text-[var(--text-secondary)]">
                      {planoSaas.limite_servicos} serviços • {planoSaas.limite_colaboradores} colaboradores
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Card de configuração (se necessário) */}
      {precisaConfiguracao && proximosPassos.length > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-orange-800 text-lg flex items-center">
              ⚙️ Configuração Pendente
              <span className="ml-2 text-sm font-normal">
                ({statusConfiguracao?.percentual_conclusao}% concluído)
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              <p className="text-sm text-orange-700">
                Complete a configuração da sua empresa para começar a receber agendamentos:
              </p>
              
              {/* Barra de progresso */}
              <div className="w-full bg-orange-200 rounded-full h-2">
                <div 
                  className="bg-orange-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${statusConfiguracao?.percentual_conclusao || 0}%` }}
                ></div>
              </div>

              {/* Lista de próximos passos */}
              <div className="space-y-2">
                {proximosPassos.slice(0, 3).map((passo, index) => (
                  <div key={index} className="flex items-center text-sm text-orange-700">
                    <span className="w-4 h-4 rounded-full bg-orange-200 text-orange-800 text-xs flex items-center justify-center mr-2">
                      {index + 1}
                    </span>
                    {passo}
                  </div>
                ))}
              </div>

              <div className="flex gap-2 mt-4">
                <Link href="/proprietario/configuracoes">
                  <Button size="sm" className="bg-orange-600 hover:bg-orange-700">
                    Continuar Configuração
                  </Button>
                </Link>
                {proximosPassos.includes('Configurar Stripe Connect') && (
                  <Link href="/proprietario/configuracoes?tab=pagamentos">
                    <Button size="sm" variant="outline" className="border-orange-600 text-orange-600">
                      Configurar Pagamentos
                    </Button>
                  </Link>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
