'use client';

import React from 'react';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useEmpresaProprietario } from '@/hooks/useEmpresaProprietario';

interface StatusPlanoSaasProps {
  className?: string;
}

export function StatusPlanoSaas({ className = '' }: StatusPlanoSaasProps) {
  const { planoSaas, loading, error } = useEmpresaProprietario();

  if (loading) {
    return (
      <Card className={`animate-pulse ${className}`}>
        <CardHeader>
          <div className="h-6 bg-gray-200 rounded w-32"></div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded w-24"></div>
            <div className="h-8 bg-gray-200 rounded w-20"></div>
            <div className="h-4 bg-gray-200 rounded w-40"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`border-red-200 bg-red-50 ${className}`}>
        <CardContent className="p-6">
          <div className="text-center">
            <div className="text-red-600 text-2xl mb-2">⚠️</div>
            <div className="text-red-800 font-semibold">Erro ao carregar plano</div>
            <div className="text-red-700 text-sm mt-1">{error}</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!planoSaas) {
    return (
      <Card className={`border-yellow-200 bg-yellow-50 ${className}`}>
        <CardHeader>
          <CardTitle className="text-yellow-800 flex items-center">
            ⚠️ Plano não encontrado
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-yellow-700 text-sm">
              Não foi possível encontrar informações sobre seu plano SaaS. 
              Isso pode indicar um problema com sua assinatura.
            </p>
            <div className="flex gap-2">
              <Link href="/planos">
                <Button size="sm" className="bg-yellow-600 hover:bg-yellow-700">
                  Ver Planos
                </Button>
              </Link>
              <Link href="/proprietario/configuracoes?tab=assinatura">
                <Button size="sm" variant="outline" className="border-yellow-600 text-yellow-600">
                  Verificar Assinatura
                </Button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const formatarData = (dataString: string) => {
    return new Date(dataString).toLocaleDateString('pt-BR');
  };

  const formatarMoeda = (valor: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(valor);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ativa':
        return {
          bg: 'bg-green-50',
          border: 'border-green-200',
          text: 'text-green-800',
          icon: '✅'
        };
      case 'cancelada':
        return {
          bg: 'bg-red-50',
          border: 'border-red-200',
          text: 'text-red-800',
          icon: '❌'
        };
      case 'pausada':
        return {
          bg: 'bg-yellow-50',
          border: 'border-yellow-200',
          text: 'text-yellow-800',
          icon: '⏸️'
        };
      case 'expirada':
        return {
          bg: 'bg-gray-50',
          border: 'border-gray-200',
          text: 'text-gray-800',
          icon: '⏰'
        };
      default:
        return {
          bg: 'bg-gray-50',
          border: 'border-gray-200',
          text: 'text-gray-800',
          icon: '❓'
        };
    }
  };

  const statusStyle = getStatusColor(planoSaas.status_assinatura);
  const isPremium = planoSaas.recursos_premium;

  return (
    <Card className={`${statusStyle.bg} ${statusStyle.border} border-2 ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className={`${statusStyle.text} flex items-center justify-between`}>
          <span className="flex items-center">
            {statusStyle.icon} Plano {planoSaas.nome_plano}
            {isPremium && <span className="ml-2 text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">PREMIUM</span>}
          </span>
          <span className="text-lg font-bold">
            {formatarMoeda(planoSaas.preco_mensal)}/mês
          </span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-4">
          {/* Status da assinatura */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-[var(--text-secondary)]">Status:</span>
            <span className={`text-sm font-semibold ${statusStyle.text} capitalize`}>
              {planoSaas.status_assinatura}
            </span>
          </div>

          {/* Limites do plano */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-white rounded-lg border border-[var(--border-color)]">
              <div className="text-lg font-bold text-[var(--text-primary)]">
                {planoSaas.limite_servicos}
              </div>
              <div className="text-xs text-[var(--text-secondary)]">
                Serviços
              </div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg border border-[var(--border-color)]">
              <div className="text-lg font-bold text-[var(--text-primary)]">
                {planoSaas.limite_colaboradores}
              </div>
              <div className="text-xs text-[var(--text-secondary)]">
                Colaboradores
              </div>
            </div>
          </div>

          {/* Datas importantes */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-[var(--text-secondary)]">Início:</span>
              <span className="text-[var(--text-primary)]">
                {formatarData(planoSaas.data_inicio)}
              </span>
            </div>
            {planoSaas.data_fim && (
              <div className="flex justify-between text-sm">
                <span className="text-[var(--text-secondary)]">Fim:</span>
                <span className="text-[var(--text-primary)]">
                  {formatarData(planoSaas.data_fim)}
                </span>
              </div>
            )}
          </div>

          {/* Recursos premium */}
          {isPremium && (
            <div className="p-3 bg-purple-50 rounded-lg border border-purple-200">
              <div className="text-sm font-medium text-purple-800 mb-2">
                🌟 Recursos Premium Inclusos:
              </div>
              <div className="text-xs text-purple-700 space-y-1">
                <div>• Relatórios avançados e exportação</div>
                <div>• Campanhas de marketing</div>
                <div>• Planos de assinatura para clientes</div>
                <div>• Notificações SMS e Push</div>
                <div>• Suporte prioritário</div>
              </div>
            </div>
          )}

          {/* Ações */}
          <div className="flex gap-2 pt-2">
            {planoSaas.status_assinatura === 'ativa' && (
              <>
                <Link href="/proprietario/configuracoes?tab=assinatura">
                  <Button size="sm" variant="outline" className="flex-1">
                    Gerenciar Assinatura
                  </Button>
                </Link>
                {!isPremium && (
                  <Link href="/planos">
                    <Button size="sm" className="flex-1 bg-purple-600 hover:bg-purple-700">
                      Upgrade Premium
                    </Button>
                  </Link>
                )}
              </>
            )}
            
            {planoSaas.status_assinatura !== 'ativa' && (
              <Link href="/planos">
                <Button size="sm" className="w-full">
                  Reativar Plano
                </Button>
              </Link>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
