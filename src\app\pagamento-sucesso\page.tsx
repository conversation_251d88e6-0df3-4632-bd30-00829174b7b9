'use client';
import React, { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { usePaymentStatusPolling } from '@/hooks/usePaymentStatusPolling';

function PagamentoSucessoContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<'checking' | 'success' | 'failed'>('checking');

  // Hook de polling para verificar status do pagamento
  const {
    isPolling,
    attempts,
    maxAttempts,
    timeRemaining,
    error: pollingError,
    userStatus,
    startPolling,
    stopPolling
  } = usePaymentStatusPolling({
    maxAttempts: 15,
    intervalMs: 2000,
    timeoutMs: 30000,
    onSuccess: () => {
      console.log('🎉 Pagamento confirmado! Redirecionando para onboarding...');
      setTimeout(() => {
        router.push('/onboarding/inicio');
      }, 1500);
    },
    onTimeout: () => {
      console.log('⏰ Timeout atingido, oferecendo opções ao usuário');
      setError('O processamento está demorando mais que o esperado. Você pode tentar atualizar a página ou continuar manualmente.');
    },
    onError: (errorMsg) => {
      console.error('❌ Erro durante polling:', errorMsg);
      setError(errorMsg);
    }
  });

  useEffect(() => {
    const verifyPayment = async () => {
      try {
        // Obter parâmetros da URL
        const paymentIntent = searchParams.get('payment_intent');
        const paymentIntentClientSecret = searchParams.get('payment_intent_client_secret');
        const redirectStatus = searchParams.get('redirect_status');

        console.log('🔍 Parâmetros de retorno do Stripe:', {
          paymentIntent,
          paymentIntentClientSecret,
          redirectStatus
        });

        if (!paymentIntent || !paymentIntentClientSecret) {
          setError('Parâmetros de pagamento inválidos');
          setPaymentStatus('failed');
          setLoading(false);
          return;
        }

        if (redirectStatus === 'succeeded') {
          setPaymentStatus('success');
          setLoading(false);

          console.log('✅ Pagamento bem-sucedido! Iniciando verificação de status...');

          // Aguardar 1 segundo antes de iniciar o polling para dar tempo ao webhook
          setTimeout(() => {
            startPolling();
          }, 1000);

        } else {
          setError('Pagamento não foi concluído com sucesso');
          setPaymentStatus('failed');
          setLoading(false);
        }
      } catch (err) {
        console.error('❌ Erro ao verificar pagamento:', err);
        setError('Erro ao verificar status do pagamento');
        setPaymentStatus('failed');
        setLoading(false);
      }
    };

    verifyPayment();
  }, [searchParams, startPolling]);

  if (loading || paymentStatus === 'checking') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600 mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            Verificando pagamento...
          </h2>
          <p className="text-gray-600">
            Aguarde enquanto confirmamos seu pagamento
          </p>
        </div>
      </div>
    );
  }

  if (paymentStatus === 'failed' || error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
            <svg className="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            Problema com o Pagamento
          </h2>
          <p className="text-gray-600 mb-6">
            {error || 'Houve um problema ao processar seu pagamento. Por favor, tente novamente.'}
          </p>
          <div className="space-y-3">
            <button
              onClick={() => router.push('/planos')}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Tentar Novamente
            </button>
            <button
              onClick={() => router.push('/')}
              className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Voltar ao Início
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Função para formatar tempo restante
  const formatTimeRemaining = (ms: number) => {
    const seconds = Math.ceil(ms / 1000);
    return `${seconds}s`;
  };

  // Função para tentar novamente manualmente
  const handleTryAgain = () => {
    setError(null);
    startPolling();
  };

  // Função para continuar manualmente
  const handleContinueManually = () => {
    router.push('/onboarding/inicio');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
          <svg className="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>

        <h2 className="text-2xl font-bold text-gray-800 mb-4">
          Pagamento Confirmado!
        </h2>

        {isPolling ? (
          <>
            <p className="text-gray-600 mb-4">
              Processando seu pagamento e configurando sua conta...
            </p>

            {/* Barra de progresso */}
            <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(attempts / maxAttempts) * 100}%` }}
              ></div>
            </div>

            {/* Status detalhado */}
            <div className="text-sm text-gray-500 mb-4">
              <p>Tentativa {attempts} de {maxAttempts}</p>
              <p>Tempo restante: {formatTimeRemaining(timeRemaining)}</p>
              {userStatus && (
                <div className="mt-2 text-xs">
                  <p>Pagamento: {userStatus.pagamento_confirmado ? '✅' : '⏳'}</p>
                  <p>Papel: {userStatus.role}</p>
                </div>
              )}
            </div>

            <div className="flex items-center justify-center">
              <div className="inline-block animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Aguarde...</span>
            </div>
          </>
        ) : error ? (
          <>
            <p className="text-gray-600 mb-4">
              {error}
            </p>
            <div className="space-y-3">
              <button
                onClick={handleTryAgain}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Tentar Novamente
              </button>
              <button
                onClick={handleContinueManually}
                className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
              >
                Continuar Manualmente
              </button>
            </div>
          </>
        ) : (
          <>
            <p className="text-gray-600 mb-6">
              Seu pagamento foi processado com sucesso. Você será redirecionado para configurar seu estabelecimento em instantes.
            </p>
            <div className="flex items-center justify-center">
              <div className="inline-block animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Redirecionando...</span>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export default function PagamentoSucessoPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600 mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            Verificando pagamento...
          </h2>
          <p className="text-gray-600">
            Aguarde enquanto confirmamos seu pagamento
          </p>
        </div>
      </div>
    }>
      <PagamentoSucessoContent />
    </Suspense>
  );
}
