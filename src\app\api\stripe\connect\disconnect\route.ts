import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { StripeConnectUtils } from '@/utils/stripe/connect';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Verificar se é proprietário
    if (user.user_metadata?.role !== 'Proprietario') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado. Apenas proprietários podem desconectar contas Stripe.' },
        { status: 403 }
      );
    }

    // Buscar dados da empresa do proprietário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select(`
        empresa_id,
        nome_empresa,
        stripe_account_id,
        stripe_account_status
      `)
      .eq('proprietario_user_id', user.id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada' },
        { status: 404 }
      );
    }

    // Verificar se tem conta Stripe conectada
    if (!empresa.stripe_account_id) {
      return NextResponse.json(
        { success: false, error: 'Nenhuma conta Stripe conectada' },
        { status: 400 }
      );
    }

    // Verificar se há agendamentos pendentes com pagamento online
    const { data: agendamentosPendentes, error: agendamentosError } = await supabase
      .from('agendamentos')
      .select('agendamento_id, status_agendamento, status_pagamento, forma_pagamento')
      .eq('empresa_id', empresa.empresa_id)
      .eq('forma_pagamento', 'Online')
      .in('status_pagamento', ['Pendente', 'Processando'])
      .limit(1);

    if (agendamentosError) {
      console.error('Erro ao verificar agendamentos pendentes:', agendamentosError);
    }

    if (agendamentosPendentes && agendamentosPendentes.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Não é possível desconectar a conta Stripe. Existem agendamentos com pagamentos pendentes.' 
        },
        { status: 400 }
      );
    }

    // Tentar desconectar no Stripe (desativar)
    let stripeDesconectado = false;
    try {
      stripeDesconectado = await StripeConnectUtils.desconectarConta(empresa.stripe_account_id);
    } catch (error) {
      console.error('Erro ao desconectar conta no Stripe:', error);
      // Continuar mesmo se der erro no Stripe, para limpar os dados locais
    }

    // Limpar dados da conta Stripe na empresa
    const { error: updateError } = await supabase
      .from('empresas')
      .update({
        stripe_account_id: null,
        stripe_account_status: 'not_connected',
        stripe_charges_enabled: false,
        stripe_payouts_enabled: false,
        pagamentos_online_habilitados: false,
        updated_at: new Date().toISOString()
      })
      .eq('empresa_id', empresa.empresa_id);

    if (updateError) {
      console.error('Erro ao limpar dados da conta Stripe:', updateError);
      return NextResponse.json(
        { success: false, error: 'Erro ao desconectar conta' },
        { status: 500 }
      );
    }

    console.log('✅ Conta Stripe desconectada:', {
      empresa_id: empresa.empresa_id,
      account_id: empresa.stripe_account_id,
      proprietario: user.id,
      stripe_desconectado: stripeDesconectado
    });

    return NextResponse.json({
      success: true,
      data: {
        empresa_id: empresa.empresa_id,
        nome_empresa: empresa.nome_empresa,
        mensagem: 'Conta Stripe desconectada com sucesso',
        stripe_desconectado: stripeDesconectado
      }
    });

  } catch (error: any) {
    console.error('Erro ao desconectar conta Stripe:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Verificar se é proprietário
    if (user.user_metadata?.role !== 'Proprietario') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado' },
        { status: 403 }
      );
    }

    // Buscar dados da empresa do proprietário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select(`
        empresa_id,
        nome_empresa,
        stripe_account_id
      `)
      .eq('proprietario_user_id', user.id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada' },
        { status: 404 }
      );
    }

    // Verificar se tem conta Stripe conectada
    if (!empresa.stripe_account_id) {
      return NextResponse.json({
        success: true,
        data: {
          pode_desconectar: false,
          motivo: 'Nenhuma conta Stripe conectada'
        }
      });
    }

    // Verificar se há agendamentos pendentes com pagamento online
    const { data: agendamentosPendentes, error: agendamentosError } = await supabase
      .from('agendamentos')
      .select('agendamento_id, status_agendamento, status_pagamento, forma_pagamento, data_hora_inicio')
      .eq('empresa_id', empresa.empresa_id)
      .eq('forma_pagamento', 'Online')
      .in('status_pagamento', ['Pendente', 'Processando'])
      .order('data_hora_inicio', { ascending: true });

    if (agendamentosError) {
      console.error('Erro ao verificar agendamentos pendentes:', agendamentosError);
      return NextResponse.json(
        { success: false, error: 'Erro ao verificar agendamentos pendentes' },
        { status: 500 }
      );
    }

    const temAgendamentosPendentes = agendamentosPendentes && agendamentosPendentes.length > 0;

    return NextResponse.json({
      success: true,
      data: {
        pode_desconectar: !temAgendamentosPendentes,
        motivo: temAgendamentosPendentes 
          ? `Existem ${agendamentosPendentes.length} agendamento(s) com pagamentos pendentes`
          : 'Pode desconectar',
        agendamentos_pendentes: temAgendamentosPendentes ? agendamentosPendentes.length : 0,
        proximo_agendamento: temAgendamentosPendentes ? agendamentosPendentes[0].data_hora_inicio : null
      }
    });

  } catch (error: any) {
    console.error('Erro ao verificar possibilidade de desconexão:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
