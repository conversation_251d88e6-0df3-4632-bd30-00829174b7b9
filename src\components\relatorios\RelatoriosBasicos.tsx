/**
 * Componente de Relatórios Básicos (Plano Essencial)
 * Implementação da Tarefa #20 - Desenvolver Módulo de Relatórios
 */

'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { RelatorioAgendamentosBasico } from '@/types/relatorios';
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface RelatoriosBasicosProps {
  relatorio: RelatorioAgendamentosBasico;
  loading?: boolean;
}

export function RelatoriosBasicos({ relatorio, loading = false }: RelatoriosBasicosProps) {
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map(i => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                  <div className="h-8 bg-gray-200 rounded w-16"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        <Card className="animate-pulse">
          <CardContent className="p-6">
            <div className="h-64 bg-gray-200 rounded"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const formatarMoeda = (valor: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(valor);
  };

  const formatarPeriodo = (inicio: string, fim: string) => {
    const dataInicio = parseISO(inicio);
    const dataFim = parseISO(fim);
    
    return `${format(dataInicio, 'dd/MM/yyyy', { locale: ptBR })} - ${format(dataFim, 'dd/MM/yyyy', { locale: ptBR })}`;
  };

  const calcularVariacao = (dados: Array<{ data: string; faturamento: number }>) => {
    if (dados.length < 2) return 0;
    
    const primeiro = dados[0].faturamento;
    const ultimo = dados[dados.length - 1].faturamento;
    
    if (primeiro === 0) return ultimo > 0 ? 100 : 0;
    
    return ((ultimo - primeiro) / primeiro) * 100;
  };

  const variacao = calcularVariacao(relatorio.faturamento_por_periodo);

  return (
    <div className="space-y-6">
      {/* Header do Relatório */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Relatórios Básicos
            </h2>
            <p className="text-gray-600">
              Período: {formatarPeriodo(relatorio.periodo.inicio, relatorio.periodo.fim)}
            </p>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">Plano Essencial</div>
            <div className="text-xs text-gray-400">
              Atualizado em {format(new Date(), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
            </div>
          </div>
        </div>
      </div>

      {/* Cards de Métricas Principais */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Total de Agendamentos */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total de Agendamentos</p>
                <p className="text-3xl font-bold text-gray-900">{relatorio.total_agendamentos}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Faturamento Bruto */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Faturamento Bruto</p>
                <p className="text-3xl font-bold text-gray-900">{formatarMoeda(relatorio.faturamento_bruto)}</p>
                {variacao !== 0 && (
                  <p className={`text-sm ${variacao > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {variacao > 0 ? '+' : ''}{variacao.toFixed(1)}% vs período anterior
                  </p>
                )}
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Agendamentos Confirmados */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Confirmados</p>
                <p className="text-3xl font-bold text-gray-900">{relatorio.agendamentos_por_status.confirmados}</p>
                <p className="text-sm text-gray-500">
                  {relatorio.total_agendamentos > 0 
                    ? ((relatorio.agendamentos_por_status.confirmados / relatorio.total_agendamentos) * 100).toFixed(1)
                    : 0}% do total
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Agendamentos Pendentes */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pendentes</p>
                <p className="text-3xl font-bold text-gray-900">{relatorio.agendamentos_por_status.pendentes}</p>
                <p className="text-sm text-gray-500">
                  {relatorio.total_agendamentos > 0 
                    ? ((relatorio.agendamentos_por_status.pendentes / relatorio.total_agendamentos) * 100).toFixed(1)
                    : 0}% do total
                </p>
              </div>
              <div className="p-3 bg-yellow-100 rounded-full">
                <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Gráfico de Faturamento por Período */}
      <Card>
        <CardHeader>
          <CardTitle>Evolução do Faturamento</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {relatorio.faturamento_por_periodo.length > 0 ? (
              <div className="space-y-3">
                {relatorio.faturamento_por_periodo.map((item, index) => {
                  const maxFaturamento = Math.max(...relatorio.faturamento_por_periodo.map(i => i.faturamento));
                  const largura = maxFaturamento > 0 ? (item.faturamento / maxFaturamento) * 100 : 0;
                  
                  return (
                    <div key={index} className="flex items-center space-x-4">
                      <div className="w-20 text-sm text-gray-600">
                        {format(parseISO(item.data), 'dd/MM', { locale: ptBR })}
                      </div>
                      <div className="flex-1">
                        <div className="bg-gray-200 rounded-full h-6 relative">
                          <div 
                            className="bg-blue-500 h-6 rounded-full transition-all duration-300"
                            style={{ width: `${largura}%` }}
                          />
                          <div className="absolute inset-0 flex items-center justify-center text-xs font-medium text-white">
                            {formatarMoeda(item.faturamento)}
                          </div>
                        </div>
                      </div>
                      <div className="w-16 text-sm text-gray-600 text-right">
                        {item.agendamentos} agend.
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <p>Nenhum dado disponível para o período selecionado</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Status dos Agendamentos */}
      <Card>
        <CardHeader>
          <CardTitle>Distribuição por Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{relatorio.agendamentos_por_status.confirmados}</div>
              <div className="text-sm text-green-700">Confirmados</div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">{relatorio.agendamentos_por_status.pendentes}</div>
              <div className="text-sm text-yellow-700">Pendentes</div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{relatorio.agendamentos_por_status.cancelados}</div>
              <div className="text-sm text-red-700">Cancelados</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{relatorio.agendamentos_por_status.concluidos}</div>
              <div className="text-sm text-blue-700">Concluídos</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Upgrade para Premium */}
      <Card className="border-2 border-dashed border-blue-300 bg-blue-50">
        <CardContent className="p-6 text-center">
          <div className="space-y-4">
            <div className="text-blue-600">
              <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">
                Desbloqueie Relatórios Avançados
              </h3>
              <p className="text-blue-700 mb-4">
                Upgrade para o Plano Premium e tenha acesso a relatórios detalhados por colaborador, 
                serviço, forma de pagamento e muito mais!
              </p>
              <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                Fazer Upgrade
              </button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
