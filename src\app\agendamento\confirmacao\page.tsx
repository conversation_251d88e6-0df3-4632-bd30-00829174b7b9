'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Layout } from '@/components/layout/Layout';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { createClient } from '@/utils/supabase/client';

function ConfirmacaoAgendamentoContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [agendamento, setAgendamento] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const verificarAgendamento = async () => {
      try {
        setLoading(true);
        
        // Verificar se usuário está autenticado
        const supabase = createClient();
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          router.push('/auth/login');
          return;
        }

        // Buscar o último agendamento do usuário
        const response = await fetch('/api/agendamentos?limit=1');
        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error ?? 'Erro ao buscar agendamento');
        }

        if (result.data && result.data.length > 0) {
          setAgendamento(result.data[0]);
        } else {
          setError('Nenhum agendamento encontrado');
        }

      } catch (error: any) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    verificarAgendamento();
  }, [router]);

  const formatarDataHora = (dataHoraStr: string) => {
    const data = new Date(dataHoraStr);
    return {
      data: data.toLocaleDateString('pt-BR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      }),
      horario: data.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
      })
    };
  };

  const formatarPreco = (preco: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(preco);
  };

  const obterCorStatus = (status: string) => {
    switch (status) {
      case 'Pendente':
        return 'text-[var(--warning)]';
      case 'Confirmado':
        return 'text-[var(--success)]';
      case 'Cancelado':
      case 'Recusado':
        return 'text-[var(--error)]';
      default:
        return 'text-[var(--text-secondary)]';
    }
  };

  const obterIconeStatus = (status: string) => {
    switch (status) {
      case 'Pendente':
        return (
          <svg className="w-6 h-6 text-[var(--warning)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'Confirmado':
        return (
          <svg className="w-6 h-6 text-[var(--success)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return (
          <svg className="w-6 h-6 text-[var(--text-secondary)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="bg-[var(--background)] py-8">
          <div className="container mx-auto px-4">
            <div className="text-center py-12">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
              <p className="text-[var(--text-secondary)] mt-4">Carregando confirmação...</p>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !agendamento) {
    return (
      <Layout>
        <div className="bg-[var(--background)] py-8">
          <div className="container mx-auto px-4">
            <div className="max-w-2xl mx-auto text-center py-12">
              <svg className="mx-auto h-12 w-12 text-[var(--text-secondary)] mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <p className="text-[var(--error)] text-lg mb-4">
                {error ?? 'Agendamento não encontrado'}
              </p>
              <div className="space-x-4">
                <Button 
                  variant="outline" 
                  onClick={() => router.push('/buscar')}
                >
                  Buscar Estabelecimentos
                </Button>
                <Button 
                  onClick={() => router.push('/dashboard')}
                >
                  Ir para Dashboard
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  const { data: dataFormatada, horario } = formatarDataHora(agendamento.data_hora_inicio);

  return (
    <Layout>
      <div className="bg-[var(--background)] py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            {/* Header de sucesso */}
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-[var(--success)] rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h1 className="text-3xl font-bold text-[var(--text-primary)] mb-2">
                Agendamento Realizado!
              </h1>
              <p className="text-[var(--text-secondary)] text-lg">
                Seu agendamento foi criado com sucesso
              </p>
            </div>

            {/* Detalhes do agendamento */}
            <Card className="mb-6">
              <CardContent className="p-6">
                <div className="flex items-center gap-4 mb-6">
                  {obterIconeStatus(agendamento.status_agendamento)}
                  <div>
                    <h2 className="text-xl font-semibold text-[var(--text-primary)]">
                      Status: <span className={obterCorStatus(agendamento.status_agendamento)}>
                        {agendamento.status_agendamento}
                      </span>
                    </h2>
                    <p className="text-[var(--text-secondary)] text-sm">
                      Código de confirmação: <span className="font-mono font-medium">
                        {agendamento.codigo_confirmacao}
                      </span>
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  {/* Estabelecimento */}
                  <div className="flex items-start gap-4 p-4 bg-[var(--surface)] rounded-lg">
                    <div className="w-10 h-10 bg-[var(--primary)] rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium text-[var(--text-primary)]">
                        {agendamento.empresas?.nome_empresa}
                      </h3>
                      <p className="text-sm text-[var(--text-secondary)]">
                        {agendamento.empresas?.endereco}
                      </p>
                      {agendamento.empresas?.telefone && (
                        <p className="text-sm text-[var(--text-secondary)]">
                          📞 {agendamento.empresas.telefone}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Serviço */}
                  <div className="flex items-start gap-4 p-4 bg-[var(--surface)] rounded-lg">
                    <div className="w-10 h-10 bg-[var(--secondary)] rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-[var(--text-primary)]">
                        {agendamento.servicos?.nome_servico}
                      </h3>
                      <p className="text-sm text-[var(--text-secondary)]">
                        {agendamento.servicos?.descricao}
                      </p>
                      <div className="flex items-center gap-4 mt-2">
                        <span className="text-sm text-[var(--text-secondary)]">
                          ⏱️ {agendamento.servicos?.duracao_minutos}min
                        </span>
                        <span className="font-semibold text-[var(--primary)]">
                          {formatarPreco(agendamento.valor_total)}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Data e horário */}
                  <div className="flex items-start gap-4 p-4 bg-[var(--surface)] rounded-lg">
                    <div className="w-10 h-10 bg-[var(--info)] rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V6a2 2 0 012-2h2a2 2 0 012 2v1m-6 0h6m-6 0l-.5 6.5a2 2 0 002 2.2h3a2 2 0 002-2.2L16 7m-6 0h6" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium text-[var(--text-primary)]">
                        {dataFormatada}
                      </h3>
                      <p className="text-sm text-[var(--text-secondary)]">
                        Horário: {horario}
                      </p>
                    </div>
                  </div>

                  {/* Forma de pagamento */}
                  <div className="flex items-start gap-4 p-4 bg-[var(--surface)] rounded-lg">
                    <div className="w-10 h-10 bg-[var(--success)] rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium text-[var(--text-primary)]">
                        Pagamento: {agendamento.forma_pagamento}
                      </h3>
                      <p className="text-sm text-[var(--text-secondary)]">
                        Status: {agendamento.status_pagamento}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Próximos passos */}
            <Card className="mb-6 bg-[var(--warning-light)] border-[var(--warning)]">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-[var(--warning)] rounded-full flex items-center justify-center mt-1">
                    <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-[var(--text-primary)] mb-2">
                      Próximos Passos
                    </h3>
                    <ul className="text-sm text-[var(--text-secondary)] space-y-1">
                      <li>• Aguarde a confirmação do estabelecimento (até 24 horas)</li>
                      <li>• Você receberá uma notificação quando for confirmado</li>
                      <li>• Anote o código de confirmação para referência</li>
                      <li>• Chegue com 10 minutos de antecedência no dia do agendamento</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Ações */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={() => router.push('/dashboard')}
                className="flex-1"
              >
                Ver Meus Agendamentos
              </Button>
              <Button
                variant="outline"
                onClick={() => router.push('/buscar')}
                className="flex-1"
              >
                Fazer Novo Agendamento
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}

export default function ConfirmacaoAgendamentoPage() {
  return (
    <Suspense fallback={
      <Layout>
        <div className="bg-[var(--background)] py-8">
          <div className="container mx-auto px-4">
            <div className="text-center py-12">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
              <p className="text-[var(--text-secondary)] mt-4">Carregando confirmação...</p>
            </div>
          </div>
        </div>
      </Layout>
    }>
      <ConfirmacaoAgendamentoContent />
    </Suspense>
  );
}
