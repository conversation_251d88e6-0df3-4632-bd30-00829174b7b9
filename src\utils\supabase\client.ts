import { createBrowserClient } from '@supabase/ssr';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase URL or Anon Key');
}

export const createClient = () => createBrowserClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    flowType: 'pkce'
  },
  global: {
    headers: {
      'X-Client-Info': 'servicetech-web'
    }
  }
});

// Tipos para autenticação
export interface UserProfile {
  id: string;
  email: string;
  name?: string;
  phone?: string;
  role: 'Administrador' | 'Proprietario' | 'Colaborador' | 'Usuario';
  created_at: string;
  updated_at: string;
  pagamento_confirmado?: boolean;
  onboarding_pendente?: boolean;
  plano_selecionado?: string;
}

export interface AuthState {
  user: UserProfile | null;
  session: any | null;
  loading: boolean;
  initialized: boolean;
}