import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { getAuditEvents, getAuditStats, AuditLogger } from '@/utils/security/audit';
import { getRequestIdentifier, withRateLimit } from '@/utils/security/rateLimiting';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      AuditLogger.securityViolation(
        undefined,
        'Unauthorized access to audit endpoint',
        getRequestIdentifier(request)
      );
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    // Verificar se é administrador
    const userRole = user.user_metadata?.role;
    if (userRole !== 'Administrador') {
      AuditLogger.securityViolation(
        user.id,
        'Non-admin access attempt to audit endpoint',
        getRequestIdentifier(request),
        { userRole }
      );
      return NextResponse.json(
        { success: false, error: 'Acesso negado' },
        { status: 403 }
      );
    }

    // Aplicar rate limiting
    try {
      withRateLimit(
        getRequestIdentifier(request, user.id),
        '/api/security/audit',
        { maxRequests: 50, windowMs: 60 * 60 * 1000 } // 50 por hora
      );
    } catch (rateLimitError: any) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Rate limit excedido',
          retryAfter: rateLimitError.retryAfter 
        },
        { status: 429 }
      );
    }

    const { searchParams } = new URL(request.url);
    
    // Parâmetros de filtro
    const filters = {
      userId: searchParams.get('userId') || undefined,
      action: searchParams.get('action') || undefined,
      resource: searchParams.get('resource') || undefined,
      category: searchParams.get('category') || undefined,
      riskLevel: searchParams.get('riskLevel') || undefined,
      startDate: searchParams.get('startDate') || undefined,
      endDate: searchParams.get('endDate') || undefined,
      limit: parseInt(searchParams.get('limit') || '100')
    };

    // Buscar eventos de auditoria
    const events = getAuditEvents(filters);
    const stats = getAuditStats();

    // Log do acesso
    AuditLogger.adminAction(
      user.id,
      'VIEW_AUDIT_LOG',
      'audit',
      undefined,
      true
    );

    return NextResponse.json({
      success: true,
      data: {
        events,
        stats,
        filters: filters
      }
    });

  } catch (error: any) {
    console.error('Erro na API de auditoria:', error);
    
    AuditLogger.securityViolation(
      undefined,
      'Error in audit API',
      getRequestIdentifier(request),
      { error: error.message }
    );

    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    // Verificar se é administrador
    const userRole = user.user_metadata?.role;
    if (userRole !== 'Administrador') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado' },
        { status: 403 }
      );
    }

    // Aplicar rate limiting
    try {
      withRateLimit(
        getRequestIdentifier(request, user.id),
        '/api/security/audit',
        { maxRequests: 20, windowMs: 60 * 60 * 1000 } // 20 POST por hora
      );
    } catch (rateLimitError: any) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Rate limit excedido',
          retryAfter: rateLimitError.retryAfter 
        },
        { status: 429 }
      );
    }

    const body = await request.json();
    const { action, eventType } = body;

    switch (action) {
      case 'manual_log':
        // Permitir que administradores registrem eventos manuais
        const { eventData } = body;
        AuditLogger.adminAction(
          user.id,
          'MANUAL_AUDIT_LOG',
          'audit',
          undefined,
          true
        );
        
        return NextResponse.json({
          success: true,
          message: 'Evento registrado com sucesso'
        });

      case 'export':
        // Exportar logs de auditoria
        const exportFilters = body.filters || {};
        const exportEvents = getAuditEvents(exportFilters);
        
        AuditLogger.adminAction(
          user.id,
          'EXPORT_AUDIT_LOG',
          'audit',
          undefined,
          true
        );
        
        return NextResponse.json({
          success: true,
          data: exportEvents,
          exportedAt: new Date().toISOString(),
          totalEvents: exportEvents.length
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Ação não reconhecida' },
          { status: 400 }
        );
    }

  } catch (error: any) {
    console.error('Erro na API de auditoria (POST):', error);
    
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
