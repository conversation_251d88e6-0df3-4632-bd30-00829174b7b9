'use client';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

interface HorarioComercial {
  diaSemana: string;
  aberto: boolean;
  horarioAbertura: string;
  horarioFechamento: string;
  intervaloInicio?: string;
  intervaloFim?: string;
}

const diasSemana = [
  { id: 'domingo', nome: 'Domingo' },
  { id: 'segunda', nome: 'Segunda-feira' },
  { id: 'terca', nome: 'Terça-feira' },
  { id: 'quarta', nome: 'Quarta-feira' },
  { id: 'quinta', nome: 'Quinta-feira' },
  { id: 'sexta', nome: 'Sexta-feira' },
  { id: 'sabado', nome: 'Sábado' },
];

export default function DefinirHorarioComercialPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [planoSelecionado, setPlanoSelecionado] = useState<string | null>(null);
  const [dadosEstabelecimento, setDadosEstabelecimento] = useState<any>(null);
  const [servicosCadastrados, setServicosCadastrados] = useState<any[]>([]);
  const [horarios, setHorarios] = useState<HorarioComercial[]>([]);
  const [copiarHorario, setCopiarHorario] = useState(false);
  const [diaParaCopiar, setDiaParaCopiar] = useState<string>('');

  useEffect(() => {
    // Importar as funções utilitárias
    import('../../../utils/onboarding').then((utils) => {
      // Recuperar dados do localStorage usando as funções utilitárias
      const plano = utils.obterPlanoSelecionado();
      const dadosEmpresa = utils.obterDadosEstabelecimento();
      const servicos = utils.obterServicosCadastrados();
      const horariosComerciais = utils.obterHorariosComerciais();

      if (!plano || !dadosEmpresa || servicos.length === 0) {
        // Se não houver dados necessários, redirecionar para a etapa apropriada
        if (!plano) {
          router.push('/onboarding/selecao-plano');
        } else if (!dadosEmpresa) {
          router.push('/onboarding/registro-empresa');
        } else {
          router.push('/onboarding/cadastro-servico');
        }
      } else {
        setPlanoSelecionado(plano);
        setDadosEstabelecimento(dadosEmpresa);
        setServicosCadastrados(servicos);
        
        // Se já existirem horários salvos, carregá-los
        if (horariosComerciais && horariosComerciais.length > 0) {
          setHorarios(horariosComerciais);
        } else {
          // Inicializar horários padrão
          const horariosIniciais = diasSemana.map(dia => ({
            diaSemana: dia.id,
            aberto: dia.id !== 'domingo', // Fechado aos domingos por padrão
            horarioAbertura: '08:00',
            horarioFechamento: '18:00',
            intervaloInicio: '12:00',
            intervaloFim: '13:00',
          }));
          setHorarios(horariosIniciais);
        }
      }
    });
  }, [router]);

  const handleHorarioChange = (index: number, field: string, value: string | boolean) => {
    const novosHorarios = [...horarios];
    novosHorarios[index] = {
      ...novosHorarios[index],
      [field]: value,
    };
    setHorarios(novosHorarios);
  };

  const handleCopiarHorario = () => {
    if (!diaParaCopiar) return;

    const horarioParaCopiar = horarios.find(h => h.diaSemana === diaParaCopiar);
    if (!horarioParaCopiar) return;

    const novosHorarios = horarios.map(horario => {
      if (horario.diaSemana !== diaParaCopiar) {
        return {
          ...horario,
          aberto: horarioParaCopiar.aberto,
          horarioAbertura: horarioParaCopiar.horarioAbertura,
          horarioFechamento: horarioParaCopiar.horarioFechamento,
          intervaloInicio: horarioParaCopiar.intervaloInicio,
          intervaloFim: horarioParaCopiar.intervaloFim,
        };
      }
      return horario;
    });

    setHorarios(novosHorarios);
    setCopiarHorario(false);
  };

  const handleContinuar = () => {
    setLoading(true);
    
    // Importar as funções utilitárias e salvar horários
    import('../../../utils/onboarding').then((utils) => {
      // Salvar horários no localStorage usando a função utilitária
      utils.salvarHorariosComerciais(horarios);
      
      // Redirecionar para a próxima etapa do onboarding
      router.push('/onboarding/conclusao');
    });
  };

  return (
    <div className="min-h-screen bg-gray-100 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-center text-gray-800 mb-2">
            Definir Horário Comercial
          </h1>
          <p className="text-gray-600 text-center mb-8">
            Configure os horários de funcionamento do seu estabelecimento
          </p>
          
          {planoSelecionado && dadosEstabelecimento && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg">
              <p className="text-blue-800 font-medium">
                Estabelecimento: {dadosEstabelecimento.nomeEstabelecimento}
              </p>
              <p className="text-gray-700 mt-1">
                Plano: {planoSelecionado === 'essencial' ? 'Essencial' : 'Premium'}
              </p>
            </div>
          )}

          {/* Ferramenta para copiar horários */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center mb-2">
              <input
                type="checkbox"
                id="copiarHorario"
                checked={copiarHorario}
                onChange={(e) => setCopiarHorario(e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="copiarHorario" className="text-gray-700 font-medium">
                Copiar horário para todos os dias úteis
              </label>
            </div>
            
            {copiarHorario && (
              <div className="flex items-center mt-2">
                <select
                  value={diaParaCopiar}
                  onChange={(e) => setDiaParaCopiar(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md mr-3"
                >
                  <option value="">Selecione o dia para copiar</option>
                  {diasSemana.map(dia => (
                    <option key={dia.id} value={dia.id}>{dia.nome}</option>
                  ))}
                </select>
                <button
                  onClick={handleCopiarHorario}
                  disabled={!diaParaCopiar}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  Aplicar
                </button>
              </div>
            )}
          </div>

          {/* Configuração de horários por dia */}
          <div className="space-y-4 mb-8">
            {horarios.map((horario, index) => (
              <div key={horario.diaSemana} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-gray-800">{diasSemana.find(d => d.id === horario.diaSemana)?.nome}</h3>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={`aberto-${horario.diaSemana}`}
                      checked={horario.aberto}
                      onChange={(e) => handleHorarioChange(index, 'aberto', e.target.checked)}
                      className="mr-2"
                    />
                    <label htmlFor={`aberto-${horario.diaSemana}`} className="text-gray-700">
                      {horario.aberto ? 'Aberto' : 'Fechado'}
                    </label>
                  </div>
                </div>

                {horario.aberto && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Horário de Abertura
                      </label>
                      <input
                        type="time"
                        value={horario.horarioAbertura}
                        onChange={(e) => handleHorarioChange(index, 'horarioAbertura', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Horário de Fechamento
                      </label>
                      <input
                        type="time"
                        value={horario.horarioFechamento}
                        onChange={(e) => handleHorarioChange(index, 'horarioFechamento', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Início do Intervalo (opcional)
                      </label>
                      <input
                        type="time"
                        value={horario.intervaloInicio || ''}
                        onChange={(e) => handleHorarioChange(index, 'intervaloInicio', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Fim do Intervalo (opcional)
                      </label>
                      <input
                        type="time"
                        value={horario.intervaloFim || ''}
                        onChange={(e) => handleHorarioChange(index, 'intervaloFim', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="flex justify-between items-center mt-8">
            <Link href="/onboarding/cadastro-servico" className="text-gray-600 hover:text-gray-800 transition duration-300">
              Voltar para cadastro de serviços
            </Link>
            <button
              onClick={handleContinuar}
              disabled={loading}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition duration-300 disabled:bg-gray-400"
            >
              {loading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processando...
                </span>
              ) : 'Continuar para Conclusão'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}