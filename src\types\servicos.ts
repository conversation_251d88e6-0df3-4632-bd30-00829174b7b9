// Tipos para o módulo de serviços

// Interface principal do serviço (baseada no schema do banco)
export interface Servico {
  servico_id: number;
  empresa_id: number;
  nome_servico: string;
  descricao: string;
  duracao_minutos: number;
  preco: number;
  ativo: boolean;
  categoria: string;
  created_at: string;
  updated_at: string;
}

// Dados para criação de um novo serviço
export interface CriarServicoData {
  nome_servico: string;
  descricao: string;
  duracao_minutos: number;
  preco: number;
  categoria: string;
  ativo?: boolean;
}

// Dados para atualização de um serviço
export interface AtualizarServicoData {
  nome_servico?: string;
  descricao?: string;
  duracao_minutos?: number;
  preco?: number;
  categoria?: string;
  ativo?: boolean;
}

// Resposta da API para operações de serviços
export interface ServicoApiResponse {
  success: boolean;
  data?: Servico | Servico[];
  error?: string;
  message?: string;
}

// Filtros para busca de serviços
export interface FiltrosServicos {
  empresa_id?: number;
  ativo?: boolean;
  categoria?: string;
  busca?: string;
}

// Estatísticas de serviços
export interface EstatisticasServicos {
  total: number;
  ativos: number;
  inativos: number;
  categorias: { [categoria: string]: number };
}

// Categorias predefinidas de serviços
export const CATEGORIAS_SERVICOS = [
  'Cabelo',
  'Barba',
  'Estética',
  'Manicure/Pedicure',
  'Massagem',
  'Depilação',
  'Sobrancelha',
  'Maquiagem',
  'Tratamentos Faciais',
  'Outros'
] as const;

export type CategoriaServico = typeof CATEGORIAS_SERVICOS[number];

// Validações
export interface ValidacaoServico {
  nome_servico: string[];
  descricao: string[];
  duracao_minutos: string[];
  preco: string[];
  categoria: string[];
}

// Limites por plano
export const LIMITES_SERVICOS = {
  essencial: 6,
  premium: 12
} as const;
