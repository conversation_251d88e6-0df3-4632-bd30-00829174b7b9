'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { CriarComboData, CriarComboItemData, ComboCompleto, TipoDesconto } from '@/types/combos';
import { useServicos } from '@/hooks/useServicos';

interface FormularioComboProps {
  combo?: ComboCompleto;
  onSalvar: (dados: CriarComboData) => Promise<boolean>;
  onCancelar: () => void;
  loading?: boolean;
}

export function FormularioCombo({ combo, onSalvar, onCancelar, loading = false }: FormularioComboProps) {
  const { servicos, buscarServicos } = useServicos();
  
  // Estados do formulário
  const [nomeCombo, setNomeCombo] = useState(combo?.nome_combo || '');
  const [descricao, setDescricao] = useState(combo?.descricao || '');
  const [tipoDesconto, setTipoDesconto] = useState<TipoDesconto>('percentual');
  const [descontoValorFixo, setDescontoValorFixo] = useState<number>(combo?.desconto_valor_fixo || 0);
  const [descontoPercentual, setDescontoPercentual] = useState<number>(combo?.desconto_percentual || 0);
  const [ativo, setAtivo] = useState(combo?.ativo ?? true);
  const [dataInicio, setDataInicio] = useState(combo?.data_inicio?.split('T')[0] || '');
  const [dataFim, setDataFim] = useState(combo?.data_fim?.split('T')[0] || '');
  const [limiteUsos, setLimiteUsos] = useState<number>(combo?.limite_usos || 0);
  const [itensCombo, setItensCombo] = useState<CriarComboItemData[]>([]);
  
  // Estados de validação
  const [erros, setErros] = useState<Record<string, string>>({});

  // Carregar serviços na inicialização
  useEffect(() => {
    buscarServicos({ ativo: true });
  }, [buscarServicos]);

  // Inicializar itens do combo se estiver editando
  useEffect(() => {
    if (combo?.itens) {
      const itens = combo.itens.map(item => ({
        servico_id: item.servico_id,
        quantidade: item.quantidade,
        ordem_execucao: item.ordem_execucao,
        obrigatorio: item.obrigatorio
      }));
      setItensCombo(itens);
    }

    // Determinar tipo de desconto
    if (combo?.desconto_valor_fixo && combo.desconto_valor_fixo > 0) {
      setTipoDesconto('valor_fixo');
    } else if (combo?.desconto_percentual && combo.desconto_percentual > 0) {
      setTipoDesconto('percentual');
    }
  }, [combo]);

  // Adicionar serviço ao combo
  const adicionarServico = () => {
    const novoItem: CriarComboItemData = {
      servico_id: 0,
      quantidade: 1,
      ordem_execucao: itensCombo.length + 1,
      obrigatorio: true
    };
    setItensCombo([...itensCombo, novoItem]);
  };

  // Remover serviço do combo
  const removerServico = (index: number) => {
    const novosItens = itensCombo.filter((_, i) => i !== index);
    // Reordenar
    const itensReordenados = novosItens.map((item, i) => ({
      ...item,
      ordem_execucao: i + 1
    }));
    setItensCombo(itensReordenados);
  };

  // Atualizar item do combo
  const atualizarItem = (index: number, campo: keyof CriarComboItemData, valor: any) => {
    const novosItens = [...itensCombo];
    novosItens[index] = { ...novosItens[index], [campo]: valor };
    setItensCombo(novosItens);
  };

  // Validar formulário
  const validarFormulario = (): boolean => {
    const novosErros: Record<string, string> = {};

    if (!nomeCombo.trim()) {
      novosErros.nomeCombo = 'Nome do combo é obrigatório';
    } else if (nomeCombo.trim().length < 3) {
      novosErros.nomeCombo = 'Nome deve ter pelo menos 3 caracteres';
    }

    if (!descricao.trim()) {
      novosErros.descricao = 'Descrição é obrigatória';
    } else if (descricao.trim().length < 10) {
      novosErros.descricao = 'Descrição deve ter pelo menos 10 caracteres';
    }

    if (tipoDesconto === 'valor_fixo' && descontoValorFixo <= 0) {
      novosErros.desconto = 'Desconto em valor fixo deve ser maior que zero';
    }

    if (tipoDesconto === 'percentual' && (descontoPercentual <= 0 || descontoPercentual > 90)) {
      novosErros.desconto = 'Desconto percentual deve estar entre 1% e 90%';
    }

    if (itensCombo.length < 2) {
      novosErros.itens = 'Combo deve ter pelo menos 2 serviços';
    }

    // Validar itens
    const servicosInvalidos = itensCombo.some(item => 
      item.servico_id === 0 || item.quantidade <= 0
    );
    if (servicosInvalidos) {
      novosErros.itens = 'Todos os serviços devem ser selecionados com quantidade válida';
    }

    // Verificar serviços duplicados
    const servicosIds = itensCombo.map(item => item.servico_id);
    const servicosDuplicados = servicosIds.length !== new Set(servicosIds).size;
    if (servicosDuplicados) {
      novosErros.itens = 'Não é possível adicionar o mesmo serviço mais de uma vez';
    }

    if (dataInicio && dataFim && new Date(dataInicio) >= new Date(dataFim)) {
      novosErros.datas = 'Data de fim deve ser posterior à data de início';
    }

    setErros(novosErros);
    return Object.keys(novosErros).length === 0;
  };

  // Submeter formulário
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validarFormulario()) {
      return;
    }

    const dadosCombo: CriarComboData = {
      nome_combo: nomeCombo.trim(),
      descricao: descricao.trim(),
      desconto_valor_fixo: tipoDesconto === 'valor_fixo' ? descontoValorFixo : undefined,
      desconto_percentual: tipoDesconto === 'percentual' ? descontoPercentual : undefined,
      ativo,
      data_inicio: dataInicio || undefined,
      data_fim: dataFim || undefined,
      limite_usos: limiteUsos > 0 ? limiteUsos : undefined,
      itens: itensCombo
    };

    const sucesso = await onSalvar(dadosCombo);
    if (sucesso) {
      // Limpar formulário se for criação
      if (!combo) {
        setNomeCombo('');
        setDescricao('');
        setDescontoValorFixo(0);
        setDescontoPercentual(0);
        setDataInicio('');
        setDataFim('');
        setLimiteUsos(0);
        setItensCombo([]);
      }
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>
          {combo ? 'Editar Combo' : 'Novo Combo'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Informações básicas */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                Nome do Combo *
              </label>
              <Input
                type="text"
                value={nomeCombo}
                onChange={(e) => setNomeCombo(e.target.value)}
                placeholder="Ex: Corte + Barba"
                className={erros.nomeCombo ? 'border-red-500' : ''}
              />
              {erros.nomeCombo && (
                <p className="text-red-500 text-sm mt-1">{erros.nomeCombo}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                Status
              </label>
              <select
                value={ativo ? 'ativo' : 'inativo'}
                onChange={(e) => setAtivo(e.target.value === 'ativo')}
                className="w-full px-3 py-2 border border-[var(--border)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary)]"
              >
                <option value="ativo">Ativo</option>
                <option value="inativo">Inativo</option>
              </select>
            </div>
          </div>

          {/* Descrição */}
          <div>
            <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
              Descrição *
            </label>
            <textarea
              value={descricao}
              onChange={(e) => setDescricao(e.target.value)}
              placeholder="Descreva o combo e seus benefícios..."
              rows={3}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary)] ${
                erros.descricao ? 'border-red-500' : 'border-[var(--border)]'
              }`}
            />
            {erros.descricao && (
              <p className="text-red-500 text-sm mt-1">{erros.descricao}</p>
            )}
          </div>

          {/* Desconto */}
          <div className="space-y-4">
            <label className="block text-sm font-medium text-[var(--text-primary)]">
              Tipo de Desconto *
            </label>
            
            <div className="flex gap-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="percentual"
                  checked={tipoDesconto === 'percentual'}
                  onChange={(e) => setTipoDesconto(e.target.value as TipoDesconto)}
                  className="mr-2"
                />
                Percentual
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="valor_fixo"
                  checked={tipoDesconto === 'valor_fixo'}
                  onChange={(e) => setTipoDesconto(e.target.value as TipoDesconto)}
                  className="mr-2"
                />
                Valor Fixo
              </label>
            </div>

            {tipoDesconto === 'percentual' ? (
              <div>
                <Input
                  type="number"
                  value={descontoPercentual}
                  onChange={(e) => setDescontoPercentual(Number(e.target.value))}
                  placeholder="Ex: 15"
                  min="1"
                  max="90"
                  className={erros.desconto ? 'border-red-500' : ''}
                />
                <p className="text-sm text-[var(--text-secondary)] mt-1">
                  Desconto em % (1% a 90%)
                </p>
              </div>
            ) : (
              <div>
                <Input
                  type="number"
                  value={descontoValorFixo}
                  onChange={(e) => setDescontoValorFixo(Number(e.target.value))}
                  placeholder="Ex: 10.00"
                  min="0.01"
                  step="0.01"
                  className={erros.desconto ? 'border-red-500' : ''}
                />
                <p className="text-sm text-[var(--text-secondary)] mt-1">
                  Desconto em R$
                </p>
              </div>
            )}
            
            {erros.desconto && (
              <p className="text-red-500 text-sm">{erros.desconto}</p>
            )}
          </div>

          {/* Período de validade */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                Data de Início
              </label>
              <Input
                type="date"
                value={dataInicio}
                onChange={(e) => setDataInicio(e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                Data de Fim
              </label>
              <Input
                type="date"
                value={dataFim}
                onChange={(e) => setDataFim(e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                Limite de Usos
              </label>
              <Input
                type="number"
                value={limiteUsos}
                onChange={(e) => setLimiteUsos(Number(e.target.value))}
                placeholder="0 = ilimitado"
                min="0"
              />
            </div>
          </div>

          {erros.datas && (
            <p className="text-red-500 text-sm">{erros.datas}</p>
          )}

          {/* Serviços do combo */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <label className="block text-sm font-medium text-[var(--text-primary)]">
                Serviços do Combo *
              </label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={adicionarServico}
              >
                + Adicionar Serviço
              </Button>
            </div>

            {itensCombo.map((item, index) => (
              <div key={index} className="flex gap-4 items-end p-4 border border-[var(--border)] rounded-lg">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                    Serviço
                  </label>
                  <select
                    value={item.servico_id}
                    onChange={(e) => atualizarItem(index, 'servico_id', Number(e.target.value))}
                    className="w-full px-3 py-2 border border-[var(--border)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary)]"
                  >
                    <option value={0}>Selecione um serviço</option>
                    {servicos.map(servico => (
                      <option key={servico.servico_id} value={servico.servico_id}>
                        {servico.nome_servico} - R$ {servico.preco.toFixed(2)}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="w-24">
                  <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                    Qtd
                  </label>
                  <Input
                    type="number"
                    value={item.quantidade}
                    onChange={(e) => atualizarItem(index, 'quantidade', Number(e.target.value))}
                    min="1"
                  />
                </div>

                <div className="flex items-center">
                  <label className="flex items-center text-sm">
                    <input
                      type="checkbox"
                      checked={item.obrigatorio}
                      onChange={(e) => atualizarItem(index, 'obrigatorio', e.target.checked)}
                      className="mr-2"
                    />
                    Obrigatório
                  </label>
                </div>

                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removerServico(index)}
                  className="text-red-600 hover:text-red-700"
                >
                  Remover
                </Button>
              </div>
            ))}

            {erros.itens && (
              <p className="text-red-500 text-sm">{erros.itens}</p>
            )}
          </div>

          {/* Botões */}
          <div className="flex gap-4 pt-4">
            <Button
              type="submit"
              disabled={loading}
              className="flex-1"
            >
              {loading ? 'Salvando...' : (combo ? 'Atualizar' : 'Criar')} Combo
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onCancelar}
              disabled={loading}
            >
              Cancelar
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
