// Utilitário para gerenciar imagens genéricas baseadas no segmento da empresa

// Mapeamento de segmentos para imagens genéricas
// TODO: Substituir por imagens reais quando disponíveis
const IMAGENS_SEGMENTOS: Record<string, string> = {
  'beleza': 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=400&h=300&fit=crop&crop=center',
  'estetica': 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400&h=300&fit=crop&crop=center',
  'barbearia': 'https://images.unsplash.com/photo-1503951914875-452162b0f3f1?w=400&h=300&fit=crop&crop=center',
  'spa': 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400&h=300&fit=crop&crop=center',
  'manicure': 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400&h=300&fit=crop&crop=center',
  'outro': 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop&crop=center'
};

// Imagem padrão quando segmento não está definido
const IMAGEM_PADRAO = 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop&crop=center';

/**
 * Retorna a URL da imagem apropriada para uma empresa
 * @param logoUrl - URL do logo da empresa (pode ser null/undefined)
 * @param segmento - Segmento da empresa (pode ser null/undefined)
 * @returns URL da imagem a ser exibida
 */
export function obterImagemEmpresa(logoUrl: string | null | undefined, segmento: string | null | undefined): string {
  // Se tem logo próprio, usa ele
  if (logoUrl && logoUrl.trim() !== '') {
    return logoUrl;
  }

  // Se não tem logo mas tem segmento, usa imagem genérica do segmento
  if (segmento && segmento.trim() !== '') {
    const segmentoLower = segmento.toLowerCase();
    return IMAGENS_SEGMENTOS[segmentoLower] || IMAGEM_PADRAO;
  }

  // Se não tem nem logo nem segmento, usa imagem padrão
  return IMAGEM_PADRAO;
}

/**
 * Retorna o texto alternativo apropriado para a imagem
 * @param nomeEmpresa - Nome da empresa
 * @param logoUrl - URL do logo da empresa
 * @param segmento - Segmento da empresa
 * @returns Texto alternativo para a imagem
 */
export function obterAltImagemEmpresa(
  nomeEmpresa: string, 
  logoUrl: string | null | undefined, 
  segmento: string | null | undefined
): string {
  // Se tem logo próprio
  if (logoUrl && logoUrl.trim() !== '') {
    return `Logo de ${nomeEmpresa}`;
  }

  // Se não tem logo mas tem segmento
  if (segmento && segmento.trim() !== '') {
    return `${nomeEmpresa} - ${segmento}`;
  }

  // Padrão
  return `${nomeEmpresa} - Estabelecimento`;
}

/**
 * Lista de segmentos disponíveis com suas respectivas imagens
 */
export const SEGMENTOS_DISPONIVEIS = [
  { value: 'beleza', label: 'Salão de Beleza', imagem: IMAGENS_SEGMENTOS.beleza },
  { value: 'estetica', label: 'Clínica de Estética', imagem: IMAGENS_SEGMENTOS.estetica },
  { value: 'barbearia', label: 'Barbearia', imagem: IMAGENS_SEGMENTOS.barbearia },
  { value: 'spa', label: 'SPA', imagem: IMAGENS_SEGMENTOS.spa },
  { value: 'manicure', label: 'Manicure e Pedicure', imagem: IMAGENS_SEGMENTOS.manicure },
  { value: 'outro', label: 'Outro', imagem: IMAGENS_SEGMENTOS.outro }
] as const;
