/**
 * <PERSON>ript para criar usuários de teste no Supabase
 * Cria 4 usuários com diferentes roles para testar o sistema de autenticação
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Variáveis de ambiente do Supabase não encontradas');
  process.exit(1);
}

// Cliente administrativo do Supabase
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Dados dos usuários de teste
const testUsers = [
  {
    email: '<EMAIL>',
    password: '123456',
    userData: {
      name: '<PERSON>',
      full_name: '<PERSON>',
      phone: '(11) 99999-1111',
      role: '<PERSON>ministra<PERSON>'
    },
    description: 'Administrador do sistema - acesso total'
  },
  {
    email: '<EMAIL>',
    password: '123456',
    userData: {
      name: 'Maria Santos',
      full_name: 'Maria Santos',
      phone: '(11) 99999-2222',
      role: 'Proprietario'
    },
    description: 'Proprietário de estabelecimento - pode gerenciar empresa'
  },
  {
    email: '<EMAIL>',
    password: '123456',
    userData: {
      name: 'Pedro Oliveira',
      full_name: 'Pedro Oliveira',
      phone: '(11) 99999-3333',
      role: 'Colaborador'
    },
    description: 'Colaborador/Funcionário - trabalha em estabelecimento'
  },
  {
    email: '<EMAIL>',
    password: '123456',
    userData: {
      name: 'Ana Costa',
      full_name: 'Ana Costa',
      phone: '(11) 99999-4444',
      role: 'Usuario'
    },
    description: 'Cliente/Usuário final - agenda serviços'
  }
];

async function createTestUsers() {
  console.log('🚀 Iniciando criação de usuários de teste...\n');

  const createdUsers = [];

  for (const user of testUsers) {
    try {
      console.log(`📝 Criando usuário: ${user.email} (${user.userData.role})`);
      
      // Criar usuário usando a API administrativa
      const { data, error } = await supabase.auth.admin.createUser({
        email: user.email,
        password: user.password,
        email_confirm: true, // Confirmar email automaticamente
        user_metadata: user.userData
      });

      if (error) {
        console.error(`❌ Erro ao criar ${user.email}:`, error.message);
        continue;
      }

      console.log(`✅ Usuário ${user.email} criado com sucesso!`);
      console.log(`   ID: ${data.user.id}`);
      console.log(`   Nome: ${user.userData.name}`);
      console.log(`   Role: ${user.userData.role}`);
      console.log(`   Telefone: ${user.userData.phone}\n`);

      createdUsers.push({
        id: data.user.id,
        email: user.email,
        password: user.password,
        name: user.userData.name,
        phone: user.userData.phone,
        role: user.userData.role,
        description: user.description
      });

    } catch (error) {
      console.error(`❌ Erro inesperado ao criar ${user.email}:`, error);
    }
  }

  return createdUsers;
}

async function testLogin(email, password) {
  try {
    console.log(`🔐 Testando login para: ${email}`);
    
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      console.error(`❌ Erro no login de ${email}:`, error.message);
      return false;
    }

    console.log(`✅ Login bem-sucedido para ${email}`);
    console.log(`   User ID: ${data.user.id}`);
    console.log(`   Role: ${data.user.user_metadata?.role}`);
    
    // Fazer logout
    await supabase.auth.signOut();
    
    return true;
  } catch (error) {
    console.error(`❌ Erro inesperado no login de ${email}:`, error);
    return false;
  }
}

async function main() {
  try {
    // Criar usuários
    const createdUsers = await createTestUsers();
    
    if (createdUsers.length === 0) {
      console.log('❌ Nenhum usuário foi criado com sucesso.');
      return;
    }

    console.log('=' .repeat(60));
    console.log('📋 RESUMO DOS USUÁRIOS CRIADOS');
    console.log('=' .repeat(60));
    
    createdUsers.forEach((user, index) => {
      console.log(`\n${index + 1}. ${user.description}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Senha: ${user.password}`);
      console.log(`   Nome: ${user.name}`);
      console.log(`   Telefone: ${user.phone}`);
      console.log(`   Role: ${user.role}`);
      console.log(`   ID: ${user.id}`);
    });

    console.log('\n' + '=' .repeat(60));
    console.log('🧪 TESTANDO LOGIN');
    console.log('=' .repeat(60));

    // Testar login do primeiro usuário (admin)
    if (createdUsers.length > 0) {
      const adminUser = createdUsers.find(u => u.role === 'Administrador');
      if (adminUser) {
        await testLogin(adminUser.email, adminUser.password);
      }
    }

    console.log('\n✅ Script executado com sucesso!');
    console.log('\n📝 INSTRUÇÕES PARA USO:');
    console.log('1. Use os emails e senhas listados acima para fazer login');
    console.log('2. Cada usuário tem um role diferente para testar permissões');
    console.log('3. O sistema de Magic Link também funcionará com estes emails');
    console.log('4. Para testar RLS, faça login com diferentes usuários e verifique os acessos');

  } catch (error) {
    console.error('❌ Erro geral:', error);
  }
}

// Executar script
main();
