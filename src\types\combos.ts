// Tipos para o módulo de combos de serviços

// Interface principal do combo (baseada no schema do banco)
export interface Combo {
  combo_id: number;
  empresa_id: number;
  nome_combo: string;
  descricao: string;
  desconto_valor_fixo?: number;
  desconto_percentual?: number;
  ativo: boolean;
  data_inicio?: string;
  data_fim?: string;
  limite_usos?: number;
  usos_realizados: number;
  created_at: string;
  updated_at: string;
}

// Item do combo
export interface ComboItem {
  item_id: number;
  combo_id: number;
  servico_id: number;
  quantidade: number;
  ordem_execucao: number;
  obrigatorio: boolean;
}

// Combo com itens relacionados
export interface ComboCompleto extends Combo {
  itens: ComboItemCompleto[];
  valor_original: number;
  valor_com_desconto: number;
  economia: number;
}

// Item do combo com dados do serviço
export interface ComboItemCompleto extends ComboItem {
  servico: {
    nome_servico: string;
    descricao: string;
    preco: number;
    duracao_minutos: number;
    categoria: string;
  };
}

// Dados para criação de um novo combo
export interface CriarComboData {
  nome_combo: string;
  descricao: string;
  desconto_valor_fixo?: number;
  desconto_percentual?: number;
  ativo?: boolean;
  data_inicio?: string;
  data_fim?: string;
  limite_usos?: number;
  itens: CriarComboItemData[];
}

// Dados para criação de item do combo
export interface CriarComboItemData {
  servico_id: number;
  quantidade: number;
  ordem_execucao: number;
  obrigatorio: boolean;
}

// Dados para atualização de um combo
export interface AtualizarComboData {
  nome_combo?: string;
  descricao?: string;
  desconto_valor_fixo?: number;
  desconto_percentual?: number;
  ativo?: boolean;
  data_inicio?: string;
  data_fim?: string;
  limite_usos?: number;
  itens?: CriarComboItemData[];
}

// Resposta da API para operações de combos
export interface ComboApiResponse {
  success: boolean;
  data?: Combo | Combo[] | ComboCompleto | ComboCompleto[];
  error?: string;
  message?: string;
}

// Filtros para busca de combos
export interface FiltrosCombos {
  ativo?: boolean;
  busca?: string;
  data_inicio?: string;
  data_fim?: string;
}

// Estatísticas de combos
export interface EstatisticasCombos {
  total: number;
  ativos: number;
  inativos: number;
  total_usos: number;
  economia_total: number;
}

// Detecção de combo no agendamento
export interface ComboDetectado {
  combo: ComboCompleto;
  servicos_selecionados: number[];
  servicos_faltantes: number[];
  pode_aplicar: boolean;
  valor_original: number;
  valor_com_desconto: number;
  economia: number;
}

// Validações
export interface ValidacaoCombo {
  nome_combo: string[];
  descricao: string[];
  desconto_valor_fixo: string[];
  desconto_percentual: string[];
  itens: string[];
}

// Tipos de desconto
export type TipoDesconto = 'valor_fixo' | 'percentual';

// Status do combo
export type StatusCombo = 'ativo' | 'inativo' | 'expirado' | 'esgotado';
