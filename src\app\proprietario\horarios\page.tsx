'use client';

import React, { useEffect, useState } from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import { useHorarios } from '@/hooks/useHorarios';
import { HorarioEmpresaComponent } from '@/components/horarios/HorarioEmpresa';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

function GerenciamentoHorarios() {
  const { user } = useAuth();
  const {
    horarios_empresa,
    loading,
    error,
    salvando,
    buscarHorariosEmpresa,
    atualizarHorariosEmpresa,
    limparError
  } = useHorarios();

  const [abaSelecionada, setAbaSelecionada] = useState<'empresa' | 'colaboradores'>('empresa');

  // Carregar horários ao montar o componente
  useEffect(() => {
    buscarHorariosEmpresa();
  }, [buscarHorariosEmpresa]);

  // Limpar erros quando o usuário navegar entre abas
  useEffect(() => {
    if (error) {
      limparError();
    }
  }, [abaSelecionada, limparError]);

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-[var(--background)]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Cabeçalho */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-[var(--text-primary)]">
            Gerenciamento de Horários
          </h1>
          <p className="text-[var(--text-secondary)] mt-2">
            Configure os horários de funcionamento da empresa e dos colaboradores
          </p>
        </div>

        {/* Navegação por abas */}
        <div className="mb-6">
          <nav className="flex space-x-8" aria-label="Tabs">
            <button
              onClick={() => setAbaSelecionada('empresa')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                abaSelecionada === 'empresa'
                  ? 'border-[var(--primary)] text-[var(--primary)]'
                  : 'border-transparent text-[var(--text-secondary)] hover:text-[var(--text-primary)] hover:border-gray-300'
              }`}
            >
              Horários da Empresa
            </button>
            <button
              onClick={() => setAbaSelecionada('colaboradores')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                abaSelecionada === 'colaboradores'
                  ? 'border-[var(--primary)] text-[var(--primary)]'
                  : 'border-transparent text-[var(--text-secondary)] hover:text-[var(--text-primary)] hover:border-gray-300'
              }`}
            >
              Horários dos Colaboradores
            </button>
          </nav>
        </div>

        {/* Mensagem de erro global */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Erro ao carregar horários
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
                <div className="mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      limparError();
                      buscarHorariosEmpresa();
                    }}
                    className="text-red-800 border-red-300 hover:bg-red-50"
                  >
                    Tentar novamente
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Conteúdo das abas */}
        <div className="space-y-6">
          {abaSelecionada === 'empresa' && (
            <div>
              <HorarioEmpresaComponent
                horarios={horarios_empresa}
                onSalvar={atualizarHorariosEmpresa}
                loading={loading}
              />
            </div>
          )}

          {abaSelecionada === 'colaboradores' && (
            <div>
              <Card className="p-8 text-center">
                <div className="max-w-md mx-auto">
                  <div className="mb-4">
                    <svg className="mx-auto h-12 w-12 text-[var(--text-secondary)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-[var(--text-primary)] mb-2">
                    Horários dos Colaboradores
                  </h3>
                  <p className="text-[var(--text-secondary)] mb-6">
                    Esta funcionalidade estará disponível em breve. Por enquanto, os colaboradores herdam os horários da empresa.
                  </p>
                  <div className="space-y-2 text-sm text-[var(--text-secondary)]">
                    <p>• Configuração individual de horários por colaborador</p>
                    <p>• Bloqueios de horários específicos</p>
                    <p>• Bloqueios recorrentes</p>
                    <p>• Verificação de conflitos com agendamentos</p>
                  </div>
                </div>
              </Card>
            </div>
          )}
        </div>

        {/* Informações adicionais */}
        <div className="mt-8">
          <Card className="p-6 bg-blue-50 border-blue-200">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  Dicas importantes
                </h3>
                <div className="mt-2 text-sm text-blue-700">
                  <ul className="list-disc list-inside space-y-1">
                    <li>Configure pelo menos um dia da semana como ativo</li>
                    <li>O horário de fechamento deve ser posterior ao de abertura</li>
                    <li>As pausas devem estar dentro do horário de funcionamento</li>
                    <li>Use o botão "Aplicar para todos" para copiar horários entre dias</li>
                    <li>Os horários configurados afetarão a disponibilidade para agendamentos</li>
                  </ul>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}

export default function HorariosPage() {
  return (
    <ProtectedRoute requiredRole="Proprietario">
      <GerenciamentoHorarios />
    </ProtectedRoute>
  );
}
