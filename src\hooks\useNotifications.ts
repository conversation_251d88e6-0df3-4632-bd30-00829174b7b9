'use client';

import { useState, useCallback } from 'react';
import { 
   
  ProcessarNotificacaoData, 
  ContextoAgendamento, 
  ContextoProprietario 
} from '@/types/notifications';

interface UseNotificationsResult {
  enviando: boolean;
  erro: string | null;
  enviarNotificacao: (data: ProcessarNotificacaoData) => Promise<boolean>;
  reenviarNotificacao: (notificacaoId: number) => Promise<boolean>;
  buscarNotificacoesPendentes: (limite?: number) => Promise<any[]>;
  // Métodos de conveniência para tipos específicos
  notificarNovoAgendamento: (destinatarioId: string, contexto: ContextoAgendamento, agendamentoId: number, empresaId: number) => Promise<boolean>;
  notificarAgendamentoConfirmado: (destinatarioId: string, contexto: ContextoAgendamento, agendamentoId: number) => Promise<boolean>;
  notificarAgendamentoRecusado: (destinatarioId: string, contexto: ContextoAgendamento, agendamentoId: number) => Promise<boolean>;
  notificarAgendamentoCancelado: (destinatarioId: string, contexto: ContextoAgendamento, agendamentoId: number) => Promise<boolean>;
  notificarLembreteConfirmacao: (destinatarioId: string, contexto: ContextoProprietario, agendamentoId: number, empresaId: number) => Promise<boolean>;
  notificarLembreteAgendamento: (destinatarioId: string, contexto: ContextoAgendamento, agendamentoId: number) => Promise<boolean>;
}

export function useNotifications(): UseNotificationsResult {
  const [enviando, setEnviando] = useState(false);
  const [erro, setErro] = useState<string | null>(null);

  const enviarNotificacao = useCallback(async (data: ProcessarNotificacaoData): Promise<boolean> => {
    setEnviando(true);
    setErro(null);

    try {
      console.log(`📧 Hook: Enviando notificação ${data.tipo} para ${data.destinatario_id}`);

      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Erro ao enviar notificação');
      }

      console.log(`✅ Hook: Notificação enviada com sucesso (ID: ${result.notificacao_id})`);
      return true;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      console.error('❌ Hook: Erro ao enviar notificação:', errorMessage);
      setErro(errorMessage);
      return false;

    } finally {
      setEnviando(false);
    }
  }, []);

  const reenviarNotificacao = useCallback(async (notificacaoId: number): Promise<boolean> => {
    setEnviando(true);
    setErro(null);

    try {
      console.log(`🔄 Hook: Reenviando notificação ${notificacaoId}`);

      const response = await fetch(`/api/notifications/${notificacaoId}/resend`, {
        method: 'POST',
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Erro ao reenviar notificação');
      }

      console.log(`✅ Hook: Notificação reenviada com sucesso (tentativas: ${result.tentativas})`);
      return true;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      console.error('❌ Hook: Erro ao reenviar notificação:', errorMessage);
      setErro(errorMessage);
      return false;

    } finally {
      setEnviando(false);
    }
  }, []);

  const buscarNotificacoesPendentes = useCallback(async (limite: number = 10): Promise<any[]> => {
    try {
      const response = await fetch(`/api/notifications?action=pendentes&limite=${limite}`);
      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Erro ao buscar notificações pendentes');
      }

      return result.data || [];

    } catch (error) {
      console.error('❌ Hook: Erro ao buscar notificações pendentes:', error);
      setErro(error instanceof Error ? error.message : 'Erro desconhecido');
      return [];
    }
  }, []);

  // Métodos de conveniência para tipos específicos de notificação

  const notificarNovoAgendamento = useCallback(async (
    destinatarioId: string, 
    contexto: ContextoAgendamento, 
    agendamentoId: number, 
    empresaId: number
  ): Promise<boolean> => {
    return enviarNotificacao({
      tipo: 'novo_agendamento',
      destinatario_id: destinatarioId,
      contexto,
      canal: 'email',
      agendamento_id: agendamentoId,
      empresa_id: empresaId
    });
  }, [enviarNotificacao]);

  const notificarAgendamentoConfirmado = useCallback(async (
    destinatarioId: string, 
    contexto: ContextoAgendamento, 
    agendamentoId: number
  ): Promise<boolean> => {
    return enviarNotificacao({
      tipo: 'agendamento_confirmado',
      destinatario_id: destinatarioId,
      contexto,
      canal: 'email',
      agendamento_id: agendamentoId
    });
  }, [enviarNotificacao]);

  const notificarAgendamentoRecusado = useCallback(async (
    destinatarioId: string, 
    contexto: ContextoAgendamento, 
    agendamentoId: number
  ): Promise<boolean> => {
    return enviarNotificacao({
      tipo: 'agendamento_recusado',
      destinatario_id: destinatarioId,
      contexto,
      canal: 'email',
      agendamento_id: agendamentoId
    });
  }, [enviarNotificacao]);

  const notificarAgendamentoCancelado = useCallback(async (
    destinatarioId: string, 
    contexto: ContextoAgendamento, 
    agendamentoId: number
  ): Promise<boolean> => {
    return enviarNotificacao({
      tipo: 'agendamento_cancelado',
      destinatario_id: destinatarioId,
      contexto,
      canal: 'email',
      agendamento_id: agendamentoId
    });
  }, [enviarNotificacao]);

  const notificarLembreteConfirmacao = useCallback(async (
    destinatarioId: string, 
    contexto: ContextoProprietario, 
    agendamentoId: number, 
    empresaId: number
  ): Promise<boolean> => {
    return enviarNotificacao({
      tipo: 'lembrete_confirmacao',
      destinatario_id: destinatarioId,
      contexto,
      canal: 'email',
      agendamento_id: agendamentoId,
      empresa_id: empresaId
    });
  }, [enviarNotificacao]);

  const notificarLembreteAgendamento = useCallback(async (
    destinatarioId: string, 
    contexto: ContextoAgendamento, 
    agendamentoId: number
  ): Promise<boolean> => {
    return enviarNotificacao({
      tipo: 'lembrete_agendamento',
      destinatario_id: destinatarioId,
      contexto,
      canal: 'email',
      agendamento_id: agendamentoId
    });
  }, [enviarNotificacao]);

  return {
    enviando,
    erro,
    enviarNotificacao,
    reenviarNotificacao,
    buscarNotificacoesPendentes,
    notificarNovoAgendamento,
    notificarAgendamentoConfirmado,
    notificarAgendamentoRecusado,
    notificarAgendamentoCancelado,
    notificarLembreteConfirmacao,
    notificarLembreteAgendamento
  };
}
