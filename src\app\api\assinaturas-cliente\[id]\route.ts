import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/client';
import { StatusAssinatura } from '@/types/planosAssinatura';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-05-28.basil',
});

// GET - Obter assinatura específica
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = createClient();
    const { id } = await params;
    const assinaturaId = parseInt(id);

    if (isNaN(assinaturaId)) {
      return NextResponse.json({ success: false, error: 'ID inválido' }, { status: 400 });
    }

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ success: false, error: 'Não autorizado' }, { status: 401 });
    }

    // Buscar assinatura com verificação de propriedade
    const { data: assinatura, error: assinaturaError } = await supabase
      .from('planos_servico_cliente')
      .select(`
        *,
        servicos(
          nome_servico,
          descricao,
          preco,
          duracao_minutos,
          categoria
        ),
        empresas(
          nome_empresa,
          telefone,
          endereco,
          cidade,
          estado
        )
      `)
      .eq('plano_cliente_id', assinaturaId)
      .eq('cliente_user_id', user.id)
      .single();

    if (assinaturaError || !assinatura) {
      return NextResponse.json({ 
        success: false, 
        error: 'Assinatura não encontrada' 
      }, { status: 404 });
    }

    // Calcular benefícios
    const beneficios = {
      tem_assinatura_ativa: assinatura.status === 'ativa' && assinatura.ativo,
      desconto_percentual: 20,
      usos_restantes: assinatura.limite_usos_mes 
        ? Math.max(0, assinatura.limite_usos_mes - assinatura.usos_consumidos_ciclo_atual)
        : null,
      usos_totais_mes: assinatura.limite_usos_mes,
      data_renovacao: assinatura.data_proxima_cobranca,
      beneficios_extras: [
        '20% de desconto em cada agendamento',
        'Prioridade no agendamento',
        'Cancelamento flexível'
      ]
    };

    return NextResponse.json({
      success: true,
      data: assinatura,
      beneficios
    });

  } catch (error) {
    console.error('Erro ao buscar assinatura:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Erro interno do servidor' 
    }, { status: 500 });
  }
}

// PATCH - Atualizar status da assinatura (cancelar, pausar, reativar)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = createClient();
    const { id } = await params;
    const assinaturaId = parseInt(id);
    const { action, motivo } = await request.json();

    if (isNaN(assinaturaId)) {
      return NextResponse.json({ success: false, error: 'ID inválido' }, { status: 400 });
    }

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ success: false, error: 'Não autorizado' }, { status: 401 });
    }

    // Buscar assinatura
    const { data: assinatura, error: assinaturaError } = await supabase
      .from('planos_servico_cliente')
      .select('*')
      .eq('plano_cliente_id', assinaturaId)
      .eq('cliente_user_id', user.id)
      .single();

    if (assinaturaError || !assinatura) {
      return NextResponse.json({ 
        success: false, 
        error: 'Assinatura não encontrada' 
      }, { status: 404 });
    }

    let novoStatus: StatusAssinatura;
    let dadosAtualizacao: any = {
      updated_at: new Date().toISOString()
    };

    switch (action) {
      case 'cancelar':
        if (assinatura.status === 'cancelada') {
          return NextResponse.json({ 
            success: false, 
            error: 'Assinatura já está cancelada' 
          }, { status: 400 });
        }

        novoStatus = 'cancelada';
        dadosAtualizacao.status = novoStatus;
        dadosAtualizacao.data_cancelamento = new Date().toISOString();
        dadosAtualizacao.motivo_cancelamento = motivo || 'Cancelado pelo cliente';
        dadosAtualizacao.ativo = false;

        // Cancelar no Stripe se existir
        if (assinatura.stripe_subscription_id) {
          try {
            await stripe.subscriptions.cancel(assinatura.stripe_subscription_id);
          } catch (stripeError) {
            console.error('Erro ao cancelar no Stripe:', stripeError);
            // Continua com o cancelamento local mesmo se falhar no Stripe
          }
        }
        break;

      case 'pausar':
        if (assinatura.status !== 'ativa') {
          return NextResponse.json({ 
            success: false, 
            error: 'Apenas assinaturas ativas podem ser pausadas' 
          }, { status: 400 });
        }

        novoStatus = 'pausada';
        dadosAtualizacao.status = novoStatus;

        // Pausar no Stripe se existir
        if (assinatura.stripe_subscription_id) {
          try {
            await stripe.subscriptions.update(assinatura.stripe_subscription_id, {
              pause_collection: {
                behavior: 'keep_as_draft'
              }
            });
          } catch (stripeError) {
            console.error('Erro ao pausar no Stripe:', stripeError);
            return NextResponse.json({ 
              success: false, 
              error: 'Erro ao pausar assinatura no sistema de pagamento' 
            }, { status: 500 });
          }
        }
        break;

      case 'reativar':
        if (assinatura.status !== 'pausada') {
          return NextResponse.json({ 
            success: false, 
            error: 'Apenas assinaturas pausadas podem ser reativadas' 
          }, { status: 400 });
        }

        novoStatus = 'ativa';
        dadosAtualizacao.status = novoStatus;

        // Reativar no Stripe se existir
        if (assinatura.stripe_subscription_id) {
          try {
            await stripe.subscriptions.update(assinatura.stripe_subscription_id, {
              pause_collection: null
            });
          } catch (stripeError) {
            console.error('Erro ao reativar no Stripe:', stripeError);
            return NextResponse.json({ 
              success: false, 
              error: 'Erro ao reativar assinatura no sistema de pagamento' 
            }, { status: 500 });
          }
        }
        break;

      default:
        return NextResponse.json({ 
          success: false, 
          error: 'Ação inválida. Use: cancelar, pausar ou reativar' 
        }, { status: 400 });
    }

    // Atualizar assinatura no banco
    const { data: assinaturaAtualizada, error: atualizarError } = await supabase
      .from('planos_servico_cliente')
      .update(dadosAtualizacao)
      .eq('plano_cliente_id', assinaturaId)
      .select(`
        *,
        servicos(nome_servico, preco),
        empresas(nome_empresa)
      `)
      .single();

    if (atualizarError) {
      console.error('Erro ao atualizar assinatura:', atualizarError);
      return NextResponse.json({ 
        success: false, 
        error: 'Erro ao atualizar assinatura' 
      }, { status: 500 });
    }

    const mensagens = {
      cancelar: 'Assinatura cancelada com sucesso',
      pausar: 'Assinatura pausada com sucesso',
      reativar: 'Assinatura reativada com sucesso'
    };

    return NextResponse.json({
      success: true,
      data: assinaturaAtualizada,
      message: mensagens[action as keyof typeof mensagens]
    });

  } catch (error) {
    console.error('Erro ao atualizar assinatura:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Erro interno do servidor' 
    }, { status: 500 });
  }
}

// DELETE - Excluir assinatura (apenas se cancelada)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = createClient();
    const { id } = await params;
    const assinaturaId = parseInt(id);

    if (isNaN(assinaturaId)) {
      return NextResponse.json({ success: false, error: 'ID inválido' }, { status: 400 });
    }

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ success: false, error: 'Não autorizado' }, { status: 401 });
    }

    // Buscar assinatura
    const { data: assinatura, error: assinaturaError } = await supabase
      .from('planos_servico_cliente')
      .select('*')
      .eq('plano_cliente_id', assinaturaId)
      .eq('cliente_user_id', user.id)
      .single();

    if (assinaturaError || !assinatura) {
      return NextResponse.json({ 
        success: false, 
        error: 'Assinatura não encontrada' 
      }, { status: 404 });
    }

    // Verificar se pode ser excluída
    if (assinatura.status !== 'cancelada') {
      return NextResponse.json({ 
        success: false, 
        error: 'Apenas assinaturas canceladas podem ser excluídas' 
      }, { status: 400 });
    }

    // Verificar se há agendamentos associados
    const { data: agendamentos } = await supabase
      .from('agendamentos')
      .select('agendamento_id')
      .eq('cliente_user_id', user.id)
      .eq('empresa_id', assinatura.empresa_id)
      .eq('servico_id', assinatura.servico_id);

    if (agendamentos && agendamentos.length > 0) {
      return NextResponse.json({ 
        success: false, 
        error: 'Não é possível excluir assinatura com histórico de agendamentos' 
      }, { status: 409 });
    }

    // Excluir assinatura
    const { error: excluirError } = await supabase
      .from('planos_servico_cliente')
      .delete()
      .eq('plano_cliente_id', assinaturaId);

    if (excluirError) {
      console.error('Erro ao excluir assinatura:', excluirError);
      return NextResponse.json({ 
        success: false, 
        error: 'Erro ao excluir assinatura' 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Assinatura excluída com sucesso'
    });

  } catch (error) {
    console.error('Erro ao excluir assinatura:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Erro interno do servidor' 
    }, { status: 500 });
  }
}
