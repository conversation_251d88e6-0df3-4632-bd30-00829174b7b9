'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';

function AuthCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const supabase = createClient();
        
        // Verificar se há um código de autenticação na URL
        const code = searchParams.get('code');
        const next = searchParams.get('next') || '/';
        
        if (code) {
          // Trocar o código por uma sessão
          const { error } = await supabase.auth.exchangeCodeForSession(code);
          
          if (error) {
            console.error('Erro ao trocar código por sessão:', error);
            setError('Erro ao processar autenticação. Tente novamente.');
            setLoading(false);
            return;
          }
        }

        // Obter a sessão atual
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
          console.error('Erro ao obter sessão:', sessionError);
          setError('Erro ao verificar autenticação.');
          setLoading(false);
          return;
        }

        if (session?.user) {
          // Usuário autenticado com sucesso
          const userRole = session.user.user_metadata?.role;
          
          // Redirecionar baseado no papel do usuário ou URL de destino
          let redirectUrl = next;
          
          if (next === '/' || next === '/login' || next === '/cadastro') {
            // Se não há destino específico, redirecionar baseado no papel
            switch (userRole) {
              case 'Administrador':
                redirectUrl = '/admin/dashboard';
                break;
              case 'Proprietario':
                redirectUrl = '/proprietario/dashboard';
                break;
              case 'Colaborador':
                redirectUrl = '/colaborador/agenda';
                break;
              default:
                redirectUrl = '/cliente/dashboard';
                break;
            }
          }
          
          router.push(redirectUrl);
        } else {
          // Não há sessão, redirecionar para login
          router.push('/login');
        }
      } catch (error) {
        console.error('Erro no callback de autenticação:', error);
        setError('Erro inesperado. Tente fazer login novamente.');
        setLoading(false);
      }
    };

    handleAuthCallback();
  }, [router, searchParams]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[var(--background)]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary)] mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-[var(--text-primary)] mb-2">
            Processando autenticação...
          </h2>
          <p className="text-[var(--text-secondary)]">
            Aguarde enquanto verificamos suas credenciais.
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[var(--background)]">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-[var(--text-primary)] mb-2">
            Erro de Autenticação
          </h2>
          <p className="text-[var(--text-secondary)] mb-6">
            {error}
          </p>
          <button
            onClick={() => router.push('/login')}
            className="bg-[var(--primary)] text-white px-6 py-2 rounded-md hover:bg-[var(--primary-hover)] transition-colors"
          >
            Voltar ao Login
          </button>
        </div>
      </div>
    );
  }

  return null;
}

export default function AuthCallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-[var(--background)]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary)] mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-[var(--text-primary)] mb-2">
            Processando autenticação...
          </h2>
          <p className="text-[var(--text-secondary)]">
            Aguarde enquanto verificamos suas credenciais.
          </p>
        </div>
      </div>
    }>
      <AuthCallbackContent />
    </Suspense>
  );
}
