'use client';

import { useState, useEffect } from 'react';
import { useTestesUsuario } from '@/hooks/useTestesUsuario';
import { SessaoTeste, FiltrosSessoes } from '@/types/testesUsuario';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { FormularioSessao } from './FormularioSessao';

export function GerenciamentoSessoes() {
  const {
    sessoes,
    loading,
    error,
    buscarSessoes,
    atualizarSessao
  } = useTestesUsuario();

  const [filtros, setFiltros] = useState<FiltrosSessoes>({});
  const [mostrarFormulario, setMostrarFormulario] = useState(false);
  const [sessaoEditando, setSessaoEditando] = useState<SessaoTeste | null>(null);
  const [sessaoDetalhes, setSessaoDetalhes] = useState<SessaoTeste | null>(null);

  useEffect(() => {
    buscarSessoes(filtros);
  }, [buscarSessoes, filtros]);

  const handleFiltroChange = (campo: keyof FiltrosSessoes, valor: any) => {
    setFiltros(prev => ({
      ...prev,
      [campo]: valor === '' ? undefined : valor
    }));
  };

  const handleEditar = (sessao: SessaoTeste) => {
    setSessaoEditando(sessao);
    setMostrarFormulario(true);
  };

  const handleFecharFormulario = () => {
    setMostrarFormulario(false);
    setSessaoEditando(null);
  };

  const handleVerDetalhes = (sessao: SessaoTeste) => {
    setSessaoDetalhes(sessao);
  };

  const handleFecharDetalhes = () => {
    setSessaoDetalhes(null);
  };

  const handleIniciarSessao = async (sessaoId: number) => {
    try {
      await atualizarSessao(sessaoId, {
        status: 'Em_Andamento',
        data_inicio: new Date().toISOString()
      });
      await buscarSessoes(filtros);
    } catch (error) {
      console.error('Erro ao iniciar sessão:', error);
    }
  };

  const handleFinalizarSessao = async (sessaoId: number) => {
    try {
      await atualizarSessao(sessaoId, {
        status: 'Concluida',
        data_fim: new Date().toISOString()
      });
      await buscarSessoes(filtros);
    } catch (error) {
      console.error('Erro ao finalizar sessão:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Planejada': return 'text-blue-600 bg-blue-100';
      case 'Em_Andamento': return 'text-green-600 bg-green-100';
      case 'Concluida': return 'text-gray-600 bg-gray-100';
      case 'Cancelada': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatarData = (dataString: string) => {
    return new Date(dataString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (mostrarFormulario) {
    return (
      <FormularioSessao
        sessao={sessaoEditando}
        onClose={handleFecharFormulario}
        onSuccess={() => {
          handleFecharFormulario();
          buscarSessoes(filtros);
        }}
      />
    );
  }

  if (sessaoDetalhes) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              Detalhes da Sessão
            </h2>
            <p className="text-gray-600 mt-1">
              {sessaoDetalhes.nome_sessao}
            </p>
          </div>
          <Button
            onClick={handleFecharDetalhes}
            className="bg-gray-300 hover:bg-gray-400 text-gray-700"
          >
            Voltar
          </Button>
        </div>

        {/* Informações da Sessão */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Informações Gerais</h3>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Status</h4>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(sessaoDetalhes.status)}`}>
                  {sessaoDetalhes.status.replace('_', ' ')}
                </span>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Período</h4>
                <p className="text-gray-600">
                  {formatarData(sessaoDetalhes.data_inicio)} - {formatarData(sessaoDetalhes.data_fim)}
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Participantes</h4>
                <p className="text-gray-600">
                  {sessaoDetalhes.participantes_confirmados} / {sessaoDetalhes.participantes_alvo} confirmados
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Cenários</h4>
                <p className="text-gray-600">
                  {sessaoDetalhes.cenarios_incluidos.length} cenários incluídos
                </p>
              </div>
            </div>
            
            <div className="mt-6">
              <h4 className="font-medium text-gray-900 mb-2">Descrição</h4>
              <p className="text-gray-600">{sessaoDetalhes.descricao}</p>
            </div>
            
            {sessaoDetalhes.observacoes_gerais && (
              <div className="mt-4">
                <h4 className="font-medium text-gray-900 mb-2">Observações</h4>
                <p className="text-gray-600">{sessaoDetalhes.observacoes_gerais}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Ações da Sessão */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Ações</h3>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-3">
              {sessaoDetalhes.status === 'Planejada' && (
                <Button
                  onClick={() => handleIniciarSessao(sessaoDetalhes.sessao_id)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Iniciar Sessão
                </Button>
              )}
              
              {sessaoDetalhes.status === 'Em_Andamento' && (
                <Button
                  onClick={() => handleFinalizarSessao(sessaoDetalhes.sessao_id)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Finalizar Sessão
                </Button>
              )}
              
              <Button
                onClick={() => handleEditar(sessaoDetalhes)}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Editar Sessão
              </Button>
              
              <Button
                onClick={() => {/* TODO: Implementar gerenciamento de participantes */}}
                className="bg-purple-600 hover:bg-purple-700"
              >
                Gerenciar Participantes
              </Button>
              
              {sessaoDetalhes.status === 'Concluida' && (
                <Button
                  onClick={() => {/* TODO: Implementar relatório */}}
                  className="bg-indigo-600 hover:bg-indigo-700"
                >
                  Ver Relatório
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Sessões de Teste
          </h2>
          <p className="text-gray-600 mt-1">
            Gerencie sessões de teste de usuário
          </p>
        </div>
        <Button
          onClick={() => setMostrarFormulario(true)}
          className="bg-blue-600 hover:bg-blue-700"
        >
          + Nova Sessão
        </Button>
      </div>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Filtros</h3>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={filtros.status || ''}
                onChange={(e) => handleFiltroChange('status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Todos</option>
                <option value="Planejada">Planejada</option>
                <option value="Em_Andamento">Em Andamento</option>
                <option value="Concluida">Concluída</option>
                <option value="Cancelada">Cancelada</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Data Início (a partir de)
              </label>
              <input
                type="date"
                value={filtros.data_inicio || ''}
                onChange={(e) => handleFiltroChange('data_inicio', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Data Fim (até)
              </label>
              <input
                type="date"
                value={filtros.data_fim || ''}
                onChange={(e) => handleFiltroChange('data_fim', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div className="flex items-end">
              <Button
                onClick={() => setFiltros({})}
                className="w-full bg-gray-300 hover:bg-gray-400 text-gray-700"
              >
                Limpar Filtros
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Sessões */}
      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600 mt-2">Carregando sessões...</p>
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <p className="text-red-600">Erro: {error}</p>
          <Button
            onClick={() => buscarSessoes(filtros)}
            className="mt-2"
          >
            Tentar Novamente
          </Button>
        </div>
      ) : sessoes.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-600">Nenhuma sessão encontrada</p>
          <Button
            onClick={() => setMostrarFormulario(true)}
            className="mt-2 bg-blue-600 hover:bg-blue-700"
          >
            Criar Primeira Sessão
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {sessoes.map((sessao) => (
            <Card key={sessao.sessao_id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {sessao.nome_sessao}
                    </h3>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(sessao.status)}`}>
                      {sessao.status.replace('_', ' ')}
                    </span>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4 line-clamp-2">
                  {sessao.descricao}
                </p>
                
                <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Período:</span>
                    <p className="text-gray-600">
                      {formatarData(sessao.data_inicio)}
                    </p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Participantes:</span>
                    <p className="text-gray-600">
                      {sessao.participantes_confirmados}/{sessao.participantes_alvo}
                    </p>
                  </div>
                </div>

                <div className="flex justify-end space-x-2">
                  <Button
                    onClick={() => handleVerDetalhes(sessao)}
                    className="bg-gray-600 hover:bg-gray-700 text-sm"
                  >
                    Ver Detalhes
                  </Button>
                  <Button
                    onClick={() => handleEditar(sessao)}
                    className="bg-blue-600 hover:bg-blue-700 text-sm"
                  >
                    Editar
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
