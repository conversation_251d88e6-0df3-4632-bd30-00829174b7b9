'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { FiltrosBusca, OPCOES_ORDENACAO, FAIXAS_PRECO, EstatisticasBusca } from '@/types/busca';
import { CATEGORIAS_SERVICOS } from '@/types/servicos';

interface FiltrosBuscaProps {
  filtros: FiltrosBusca;
  estatisticas: EstatisticasBusca | null;
  onAplicarFiltros: (filtros: FiltrosBusca) => void;
  onLimparFiltros: () => void;
  loading?: boolean;
}

export function FiltrosBuscaComponent({
  filtros,
  estatisticas,
  onAplicarFiltros,
  onLimparFiltros,
  loading = false
}: FiltrosBuscaProps) {
  const [filtrosLocais, setFiltrosLocais] = useState<FiltrosBusca>(filtros);
  const [mostrarFiltrosAvancados, setMostrarFiltrosAvancados] = useState(false);

  const handleAplicar = () => {
    onAplicarFiltros(filtrosLocais);
  };

  const handleLimpar = () => {
    const filtrosVazios: FiltrosBusca = {
      termo: '',
      cidade: '',
      estado: '',
      bairro: '',
      categorias_servicos: [],
      preco_minimo: undefined,
      preco_maximo: undefined,
      ordenacao: 'relevancia',
      pagina: 1,
      limite: 12
    };
    setFiltrosLocais(filtrosVazios);
    onLimparFiltros();
  };

  const handleCategoriaChange = (categoria: string, checked: boolean) => {
    const categorias = filtrosLocais.categorias_servicos || [];
    if (checked) {
      setFiltrosLocais({
        ...filtrosLocais,
        categorias_servicos: [...categorias, categoria]
      });
    } else {
      setFiltrosLocais({
        ...filtrosLocais,
        categorias_servicos: categorias.filter(c => c !== categoria)
      });
    }
  };

  const handleFaixaPrecoChange = (faixa: { label: string; min: number; max: number | null }) => {
    setFiltrosLocais({
      ...filtrosLocais,
      preco_minimo: faixa.min,
      preco_maximo: faixa.max || undefined
    });
  };

  const temFiltrosAtivos = !!(
    filtrosLocais.termo ||
    filtrosLocais.cidade ||
    filtrosLocais.estado ||
    filtrosLocais.bairro ||
    (filtrosLocais.categorias_servicos && filtrosLocais.categorias_servicos.length > 0) ||
    filtrosLocais.preco_minimo !== undefined ||
    filtrosLocais.preco_maximo !== undefined
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Filtros de Busca</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setMostrarFiltrosAvancados(!mostrarFiltrosAvancados)}
          >
            {mostrarFiltrosAvancados ? 'Ocultar' : 'Mais Filtros'}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Filtros Básicos */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Termo de busca */}
          <div>
            <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
              Buscar por nome
            </label>
            <Input
              type="text"
              placeholder="Nome do estabelecimento..."
              value={filtrosLocais.termo || ''}
              onChange={(e) => setFiltrosLocais({ ...filtrosLocais, termo: e.target.value })}
            />
          </div>

          {/* Cidade */}
          <div>
            <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
              Cidade
            </label>
            <Input
              type="text"
              placeholder="Digite a cidade..."
              value={filtrosLocais.cidade || ''}
              onChange={(e) => setFiltrosLocais({ ...filtrosLocais, cidade: e.target.value })}
              list="cidades-list"
            />
            {estatisticas && (
              <datalist id="cidades-list">
                {estatisticas.cidades_disponiveis.map(cidade => (
                  <option key={cidade} value={cidade} />
                ))}
              </datalist>
            )}
          </div>

          {/* Ordenação */}
          <div>
            <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
              Ordenar por
            </label>
            <select
              className="w-full px-3 py-2 border border-[var(--border)] rounded-md bg-[var(--surface)] text-[var(--text-primary)]"
              value={filtrosLocais.ordenacao || 'relevancia'}
              onChange={(e) => setFiltrosLocais({ ...filtrosLocais, ordenacao: e.target.value as any })}
            >
              {OPCOES_ORDENACAO.map(opcao => (
                <option key={opcao.value} value={opcao.value}>
                  {opcao.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Filtros Avançados */}
        {mostrarFiltrosAvancados && (
          <div className="space-y-4 pt-4 border-t border-[var(--border)]">
            {/* Estado e Bairro */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                  Estado
                </label>
                <Input
                  type="text"
                  placeholder="Ex: SP, RJ, MG..."
                  value={filtrosLocais.estado || ''}
                  onChange={(e) => setFiltrosLocais({ ...filtrosLocais, estado: e.target.value })}
                  maxLength={2}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                  Bairro
                </label>
                <Input
                  type="text"
                  placeholder="Digite o bairro..."
                  value={filtrosLocais.bairro || ''}
                  onChange={(e) => setFiltrosLocais({ ...filtrosLocais, bairro: e.target.value })}
                />
              </div>
            </div>

            {/* Categorias de Serviços */}
            <div>
              <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                Tipos de Serviços
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                {CATEGORIAS_SERVICOS.map(categoria => (
                  <label key={categoria} className="flex items-center space-x-2 text-sm">
                    <input
                      type="checkbox"
                      checked={filtrosLocais.categorias_servicos?.includes(categoria) || false}
                      onChange={(e) => handleCategoriaChange(categoria, e.target.checked)}
                      className="rounded border-[var(--border)]"
                    />
                    <span>{categoria}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Faixas de Preço Rápidas */}
            <div>
              <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                Faixa de Preço
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
                {FAIXAS_PRECO.map((faixa, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => handleFaixaPrecoChange(faixa)}
                    className={
                      filtrosLocais.preco_minimo === faixa.min && 
                      filtrosLocais.preco_maximo === faixa.max
                        ? 'bg-[var(--primary)] text-white'
                        : ''
                    }
                  >
                    {faixa.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Preços Personalizados */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                  Preço Mínimo (R$)
                </label>
                <Input
                  type="number"
                  placeholder="0"
                  min="0"
                  value={filtrosLocais.preco_minimo || ''}
                  onChange={(e) => setFiltrosLocais({ 
                    ...filtrosLocais, 
                    preco_minimo: e.target.value ? Number(e.target.value) : undefined 
                  })}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                  Preço Máximo (R$)
                </label>
                <Input
                  type="number"
                  placeholder="1000"
                  min="0"
                  value={filtrosLocais.preco_maximo || ''}
                  onChange={(e) => setFiltrosLocais({ 
                    ...filtrosLocais, 
                    preco_maximo: e.target.value ? Number(e.target.value) : undefined 
                  })}
                />
              </div>
            </div>
          </div>
        )}

        {/* Botões de Ação */}
        <div className="flex flex-col sm:flex-row gap-2 pt-4">
          <Button
            onClick={handleAplicar}
            disabled={loading}
            className="flex-1"
          >
            {loading ? 'Buscando...' : 'Aplicar Filtros'}
          </Button>
          {temFiltrosAtivos && (
            <Button
              variant="outline"
              onClick={handleLimpar}
              disabled={loading}
              className="flex-1 sm:flex-none"
            >
              Limpar Filtros
            </Button>
          )}
        </div>

        {/* Estatísticas */}
        {estatisticas && (
          <div className="text-sm text-[var(--text-secondary)] pt-2 border-t border-[var(--border)]">
            <p>{estatisticas.total_empresas} estabelecimentos disponíveis</p>
            {estatisticas.faixa_precos.minimo > 0 && (
              <p>
                Preços de R$ {estatisticas.faixa_precos.minimo.toFixed(2)} a R$ {estatisticas.faixa_precos.maximo.toFixed(2)}
              </p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
