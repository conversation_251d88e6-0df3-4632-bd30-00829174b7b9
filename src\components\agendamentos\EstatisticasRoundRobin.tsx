/**
 * Componente para exibir estatísticas do sistema Round-Robin
 * Implementação da Tarefa #17 - Lógica de Agendamento Round-Robin
 */

'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { useRoundRobinStats } from '@/hooks/useRoundRobinStats';

interface EstatisticasRoundRobinProps {
  empresa_id: number;
  servico_id?: number;
  className?: string;
}

export function EstatisticasRoundRobin({ 
  empresa_id, 
  servico_id,
  className = '' 
}: EstatisticasRoundRobinProps) {
  const [periodo, setPeriodo] = useState(30);
  const [apenasConfirmados, setApenasConfirmados] = useState(false);

  const {
    data,
    loading,
    error,
    atualizarEstatisticas,
    colaboradorMaisUtilizado,
    colaboradorMenosUtilizado,
    distribuicaoEquilibrada,
    recomendacoes,
    temDados,
    ultimaAtualizacao
  } = useRoundRobinStats({
    empresa_id,
    servico_id,
    periodo_dias: periodo,
    apenas_confirmados: apenasConfirmados
  });

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
            <span className="ml-3 text-[var(--text-secondary)]">Carregando estatísticas...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center">
            <div className="text-red-500 mb-2">❌ Erro ao carregar estatísticas</div>
            <p className="text-sm text-[var(--text-secondary)] mb-4">{error}</p>
            <button
              onClick={atualizarEstatisticas}
              className="px-4 py-2 bg-[var(--primary)] text-white rounded-lg hover:bg-[var(--primary-dark)] transition-colors"
            >
              Tentar Novamente
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!temDados) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-[var(--text-secondary)]">
            Nenhum dado disponível para o período selecionado.
          </div>
        </CardContent>
      </Card>
    );
  }

  const { estatisticas, colaboradores } = data!;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Controles */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🎯 Estatísticas Round-Robin
            <button
              onClick={atualizarEstatisticas}
              className="ml-auto p-2 text-[var(--text-secondary)] hover:text-[var(--primary)] transition-colors"
              title="Atualizar estatísticas"
            >
              🔄
            </button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium mb-1">Período (dias)</label>
              <select
                value={periodo}
                onChange={(e) => setPeriodo(Number(e.target.value))}
                className="px-3 py-2 border border-[var(--border)] rounded-lg focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
              >
                <option value={7}>7 dias</option>
                <option value={15}>15 dias</option>
                <option value={30}>30 dias</option>
                <option value={60}>60 dias</option>
                <option value={90}>90 dias</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Filtro</label>
              <select
                value={apenasConfirmados ? 'confirmados' : 'todos'}
                onChange={(e) => setApenasConfirmados(e.target.value === 'confirmados')}
                className="px-3 py-2 border border-[var(--border)] rounded-lg focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
              >
                <option value="todos">Todos os agendamentos</option>
                <option value="confirmados">Apenas confirmados</option>
              </select>
            </div>
          </div>

          {ultimaAtualizacao && (
            <p className="text-xs text-[var(--text-secondary)]">
              Última atualização: {new Date(ultimaAtualizacao).toLocaleString('pt-BR')}
            </p>
          )}
        </CardContent>
      </Card>

      {/* Resumo Geral */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-[var(--primary)]">
              {estatisticas.total_colaboradores}
            </div>
            <div className="text-sm text-[var(--text-secondary)]">Colaboradores</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-[var(--primary)]">
              {estatisticas.total_agendamentos}
            </div>
            <div className="text-sm text-[var(--text-secondary)]">Agendamentos</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-[var(--primary)]">
              {estatisticas.diferenca_maxima}
            </div>
            <div className="text-sm text-[var(--text-secondary)]">Diferença Máxima</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className={`text-2xl font-bold ${distribuicaoEquilibrada ? 'text-green-600' : 'text-orange-600'}`}>
              {distribuicaoEquilibrada ? '✅' : '⚠️'}
            </div>
            <div className="text-sm text-[var(--text-secondary)]">
              {distribuicaoEquilibrada ? 'Equilibrado' : 'Desbalanceado'}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Distribuição por Colaborador */}
      <Card>
        <CardHeader>
          <CardTitle>Distribuição por Colaborador</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {estatisticas.distribuicao.map((item) => {
              const colaborador = colaboradores.find(c => c.colaborador_user_id === item.colaborador_user_id);
              const isMax = item.colaborador_user_id === estatisticas.colaborador_mais_utilizado;
              const isMin = item.colaborador_user_id === estatisticas.colaborador_menos_utilizado;
              
              return (
                <div key={item.colaborador_user_id} className="flex items-center gap-4">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-medium">
                        {item.name}
                        {isMax && <span className="ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded">Mais utilizado</span>}
                        {isMin && <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Menos utilizado</span>}
                      </span>
                      <span className="text-sm text-[var(--text-secondary)]">
                        {item.count} agendamentos ({item.percentual.toFixed(1)}%)
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${isMax ? 'bg-red-500' : isMin ? 'bg-green-500' : 'bg-[var(--primary)]'}`}
                        style={{ width: `${item.percentual}%` }}
                      ></div>
                    </div>
                    {colaborador?.ultimo_agendamento && (
                      <p className="text-xs text-[var(--text-secondary)] mt-1">
                        Último agendamento: {new Date(colaborador.ultimo_agendamento).toLocaleDateString('pt-BR')}
                      </p>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Recomendações */}
      {recomendacoes.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>💡 Recomendações</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {recomendacoes.map((recomendacao, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-[var(--primary)] mt-1">•</span>
                  <span className="text-sm">{recomendacao}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
