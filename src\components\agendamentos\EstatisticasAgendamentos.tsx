'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { EstatisticasAgendamentos as EstatisticasType } from '@/types/agendamentos';

interface EstatisticasAgendamentosProps {
  estatisticas: EstatisticasType;
  loading?: boolean;
  periodo?: string;
}

export function EstatisticasAgendamentos({
  estatisticas,
  loading = false,
  periodo = 'hoje'
}: EstatisticasAgendamentosProps) {
  
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        {[1, 2, 3, 4, 5].map(i => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="text-center space-y-2">
                <div className="h-8 bg-gray-200 rounded w-16 mx-auto"></div>
                <div className="h-4 bg-gray-200 rounded w-20 mx-auto"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const cards = [
    {
      title: 'Total',
      value: estatisticas.total,
      color: 'text-[var(--text-primary)]',
      bgColor: 'bg-gray-50',
      icon: '📊'
    },
    {
      title: 'Pendentes',
      value: estatisticas.pendentes,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      icon: '⏳'
    },
    {
      title: 'Confirmados',
      value: estatisticas.confirmados,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      icon: '✅'
    },
    {
      title: 'Concluídos',
      value: estatisticas.concluidos,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      icon: '✨'
    },
    {
      title: 'Faturamento',
      value: `R$ ${estatisticas.valor_total.toFixed(2)}`,
      color: 'text-[var(--primary)]',
      bgColor: 'bg-purple-50',
      icon: '💰'
    }
  ];

  return (
    <div className="space-y-4">
      {/* Título */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-[var(--text-primary)]">
          Estatísticas {periodo && `- ${periodo}`}
        </h3>
        <div className="text-sm text-[var(--text-secondary)]">
          Atualizado agora
        </div>
      </div>

      {/* Cards de estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        {cards.map((card, index) => (
          <Card key={index} className={`${card.bgColor} border-0`}>
            <CardContent className="p-6">
              <div className="text-center">
                <div className="text-2xl mb-2">{card.icon}</div>
                <div className={`text-2xl font-bold ${card.color}`}>
                  {card.value}
                </div>
                <div className="text-sm text-[var(--text-secondary)] mt-1">
                  {card.title}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detalhes adicionais */}
      {(estatisticas.cancelados > 0 || estatisticas.por_forma_pagamento) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          {/* Cancelados */}
          {estatisticas.cancelados > 0 && (
            <Card className="bg-red-50 border-red-200">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-red-600 font-medium">
                      Cancelados/Recusados
                    </div>
                    <div className="text-2xl font-bold text-red-700">
                      {estatisticas.cancelados}
                    </div>
                  </div>
                  <div className="text-2xl">🚫</div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Formas de pagamento */}
          {estatisticas.por_forma_pagamento && Object.keys(estatisticas.por_forma_pagamento).length > 0 && (
            <Card className="bg-blue-50 border-blue-200">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm text-blue-800">
                  Por Forma de Pagamento
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2">
                  {Object.entries(estatisticas.por_forma_pagamento).map(([forma, quantidade]) => (
                    <div key={forma} className="flex items-center justify-between text-sm">
                      <span className="text-blue-700">
                        {forma === 'Online' ? '💳 Online' : '💵 Local'}
                      </span>
                      <span className="font-semibold text-blue-800">
                        {quantidade}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Taxa de conversão */}
      {estatisticas.total > 0 && (
        <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-0">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-sm text-[var(--text-secondary)]">
                  Taxa de Confirmação
                </div>
                <div className="text-lg font-bold text-green-600">
                  {((estatisticas.confirmados / estatisticas.total) * 100).toFixed(1)}%
                </div>
              </div>
              <div>
                <div className="text-sm text-[var(--text-secondary)]">
                  Taxa de Conclusão
                </div>
                <div className="text-lg font-bold text-blue-600">
                  {((estatisticas.concluidos / estatisticas.total) * 100).toFixed(1)}%
                </div>
              </div>
              <div>
                <div className="text-sm text-[var(--text-secondary)]">
                  Valor Médio
                </div>
                <div className="text-lg font-bold text-[var(--primary)]">
                  R$ {estatisticas.concluidos > 0 ? (estatisticas.valor_total / estatisticas.concluidos).toFixed(2) : '0,00'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
