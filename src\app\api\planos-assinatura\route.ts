import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/client';
import { 
  CriarPlanoAssinaturaData, 
  TemplatePlanoAssinatura,
  LIMITES_PLANOS_ASSINATURA 
} from '@/types/planosAssinatura';

// GET - Listar templates de planos de assinatura da empresa
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ success: false, error: 'Não autorizado' }, { status: 401 });
    }

    // Buscar empresa do usuário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select(`
        empresa_id, 
        nome_empresa,
        planos_saas(nome_plano)
      `)
      .eq('proprietario_user_id', user.id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json({ success: false, error: 'Empresa não encontrada' }, { status: 404 });
    }

    // Verificar se tem plano Premium
    const planoSaas = Array.isArray(empresa.planos_saas) ? empresa.planos_saas[0] : empresa.planos_saas;
    if (!planoSaas || planoSaas.nome_plano !== 'Premium') {
      return NextResponse.json({ 
        success: false, 
        error: 'Funcionalidade disponível apenas para plano Premium' 
      }, { status: 403 });
    }

    // Parâmetros de filtro
    const servicoId = searchParams.get('servico_id');
    const apenasAtivos = searchParams.get('apenas_ativos') === 'true';

    // Buscar serviços da empresa (como templates de planos)
    let query = supabase
      .from('servicos')
      .select(`
        servico_id,
        nome_servico,
        descricao,
        preco,
        duracao_minutos,
        categoria,
        ativo
      `)
      .eq('empresa_id', empresa.empresa_id);

    if (servicoId) {
      query = query.eq('servico_id', parseInt(servicoId));
    }

    if (apenasAtivos) {
      query = query.eq('ativo', true);
    }

    query = query.order('created_at', { ascending: false });

    const { data: templates, error: templatesError } = await query;

    if (templatesError) {
      console.error('Erro ao buscar templates:', templatesError);
      return NextResponse.json({ 
        success: false, 
        error: 'Erro ao buscar planos de assinatura' 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: templates || [],
      total: templates?.length || 0
    });

  } catch (error) {
    console.error('Erro na API de planos de assinatura:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Erro interno do servidor' 
    }, { status: 500 });
  }
}

// POST - Criar novo template de plano de assinatura
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const body: CriarPlanoAssinaturaData = await request.json();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ success: false, error: 'Não autorizado' }, { status: 401 });
    }

    // Validar dados de entrada
    const validacao = validarDadosPlano(body);
    if (!validacao.valido) {
      return NextResponse.json({ 
        success: false, 
        error: 'Dados inválidos',
        detalhes: validacao.erros 
      }, { status: 400 });
    }

    // Buscar empresa do usuário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select(`
        empresa_id,
        planos_saas(nome_plano)
      `)
      .eq('proprietario_user_id', user.id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json({ success: false, error: 'Empresa não encontrada' }, { status: 404 });
    }

    // Verificar se tem plano Premium
    const planoSaas = Array.isArray(empresa.planos_saas) ? empresa.planos_saas[0] : empresa.planos_saas;
    if (!planoSaas || planoSaas.nome_plano !== 'Premium') {
      return NextResponse.json({ 
        success: false, 
        error: 'Funcionalidade disponível apenas para plano Premium' 
      }, { status: 403 });
    }

    // Verificar se o serviço pertence à empresa
    const { data: servico, error: servicoError } = await supabase
      .from('servicos')
      .select('servico_id, nome_servico, preco')
      .eq('servico_id', body.servico_id)
      .eq('empresa_id', empresa.empresa_id)
      .eq('ativo', true)
      .single();

    if (servicoError || !servico) {
      return NextResponse.json({ 
        success: false, 
        error: 'Serviço não encontrado ou não pertence à sua empresa' 
      }, { status: 404 });
    }

    // Verificar se já existe template ativo para este serviço
    const { data: templateExistente } = await supabase
      .from('templates_planos_assinatura')
      .select('template_id')
      .eq('empresa_id', empresa.empresa_id)
      .eq('servico_id', body.servico_id)
      .eq('ativo', true)
      .single();

    if (templateExistente) {
      return NextResponse.json({ 
        success: false, 
        error: 'Já existe um plano de assinatura ativo para este serviço' 
      }, { status: 409 });
    }

    // Calcular desconto padrão (20% do preço original)
    const descontoPercentual = 20;

    // Criar template de plano
    const novoTemplate: Omit<TemplatePlanoAssinatura, 'template_id' | 'created_at' | 'updated_at'> = {
      empresa_id: empresa.empresa_id,
      servico_id: body.servico_id,
      nome_plano: body.nome_plano_empresa,
      descricao: body.descricao_plano ?? '',
      preco_mensal: body.preco_mensal_assinatura,
      limite_usos_mes: body.limite_usos_mes ?? undefined,
      desconto_percentual: descontoPercentual,
      beneficios: [
        `${descontoPercentual}% de desconto em cada agendamento`,
        body.limite_usos_mes ? `Até ${body.limite_usos_mes} usos por mês` : 'Usos ilimitados por mês',
        'Prioridade no agendamento',
        'Cancelamento flexível'
      ],
      ativo: true
    };

    const { data: templateCriado, error: criarError } = await supabase
      .from('templates_planos_assinatura')
      .insert(novoTemplate)
      .select(`
        *,
        servicos(
          nome_servico,
          descricao,
          preco,
          duracao_minutos,
          categoria
        )
      `)
      .single();

    if (criarError) {
      console.error('Erro ao criar template:', criarError);
      return NextResponse.json({ 
        success: false, 
        error: 'Erro ao criar plano de assinatura' 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: templateCriado,
      message: 'Plano de assinatura criado com sucesso'
    }, { status: 201 });

  } catch (error) {
    console.error('Erro ao criar plano de assinatura:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Erro interno do servidor' 
    }, { status: 500 });
  }
}

// Função auxiliar para validação
function validarDadosPlano(data: CriarPlanoAssinaturaData) {
  const erros: string[] = [];

  // Validar nome do plano
  if (!data.nome_plano_empresa || data.nome_plano_empresa.trim().length < LIMITES_PLANOS_ASSINATURA.nome_plano_min) {
    erros.push(`Nome do plano deve ter pelo menos ${LIMITES_PLANOS_ASSINATURA.nome_plano_min} caracteres`);
  }

  if (data.nome_plano_empresa && data.nome_plano_empresa.length > LIMITES_PLANOS_ASSINATURA.nome_plano_max) {
    erros.push(`Nome do plano deve ter no máximo ${LIMITES_PLANOS_ASSINATURA.nome_plano_max} caracteres`);
  }

  // Validar preço
  if (!data.preco_mensal_assinatura || data.preco_mensal_assinatura < LIMITES_PLANOS_ASSINATURA.preco_min) {
    erros.push(`Preço deve ser pelo menos R$ ${LIMITES_PLANOS_ASSINATURA.preco_min}`);
  }

  if (data.preco_mensal_assinatura > LIMITES_PLANOS_ASSINATURA.preco_max) {
    erros.push(`Preço deve ser no máximo R$ ${LIMITES_PLANOS_ASSINATURA.preco_max}`);
  }

  // Validar limite de usos
  if (data.limite_usos_mes !== undefined && data.limite_usos_mes !== null) {
    if (data.limite_usos_mes < LIMITES_PLANOS_ASSINATURA.usos_min) {
      erros.push(`Limite de usos deve ser pelo menos ${LIMITES_PLANOS_ASSINATURA.usos_min}`);
    }

    if (data.limite_usos_mes > LIMITES_PLANOS_ASSINATURA.usos_max) {
      erros.push(`Limite de usos deve ser no máximo ${LIMITES_PLANOS_ASSINATURA.usos_max}`);
    }
  }

  // Validar serviço
  if (!data.servico_id || data.servico_id <= 0) {
    erros.push('Serviço é obrigatório');
  }

  // Validar descrição
  if (data.descricao_plano && data.descricao_plano.length > LIMITES_PLANOS_ASSINATURA.descricao_max) {
    erros.push(`Descrição deve ter no máximo ${LIMITES_PLANOS_ASSINATURA.descricao_max} caracteres`);
  }

  return {
    valido: erros.length === 0,
    erros
  };
}
