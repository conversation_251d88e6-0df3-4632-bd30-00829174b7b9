import { createAdminClient } from '@/utils/supabase/server';
import { ContextoAgendamento, ContextoProprietario } from '@/types/notifications';
import { NotificationService } from '@/services/NotificationService';

/**
 * Extrai contexto completo de um agendamento para notificações
 */
export async function extrairContextoAgendamento(agendamentoId: number): Promise<{
  contextoCliente: ContextoAgendamento;
  contextoProprietario: ContextoProprietario;
  clienteId: string;
  proprietarioId: string;
  colaboradorId: string;
  empresaId: number;
} | null> {
  try {
    const supabase = createAdminClient();

    // Buscar dados completos do agendamento com joins
    const { data: agendamento, error } = await supabase
      .from('agendamentos')
      .select(`
        agendamento_id,
        empresa_id,
        cliente_user_id,
        colaborador_user_id,
        codigo_confirmacao,
        data_hora_inicio,
        data_hora_fim,
        forma_pagamento,
        valor_total,
        observacoes_cliente,
        prazo_confirmacao,
        servico:servicos(nome_servico, preco),
        empresa:empresas(nome_empresa, endereco_completo),
        cliente:auth.users!agendamentos_cliente_user_id_fkey(id, email, raw_user_meta_data),
        colaborador:auth.users!agendamentos_colaborador_user_id_fkey(id, email, raw_user_meta_data)
      `)
      .eq('agendamento_id', agendamentoId)
      .single();

    if (error || !agendamento) {
      console.error('❌ Erro ao buscar agendamento para notificação:', error);
      return null;
    }

    // Buscar proprietário da empresa
    const { data: colaboradorEmpresa, error: colaboradorError } = await supabase
      .from('colaboradores_empresa')
      .select(`
        user_id,
        papel,
        auth.users!colaboradores_empresa_user_id_fkey(id, email, raw_user_meta_data)
      `)
      .eq('empresa_id', (agendamento as any).empresa_id)
      .eq('papel', 'Proprietario')
      .eq('ativo', true)
      .single();

    if (colaboradorError || !colaboradorEmpresa) {
      console.error('❌ Erro ao buscar proprietário da empresa:', colaboradorError);
      return null;
    }

    // Extrair nomes dos metadados
    const clienteNome = (agendamento as any).cliente?.raw_user_meta_data?.name ||
                       (agendamento as any).cliente?.raw_user_meta_data?.full_name ||
                       'Cliente';

    const colaboradorNome = (agendamento as any).colaborador?.raw_user_meta_data?.name ||
                           (agendamento as any).colaborador?.raw_user_meta_data?.full_name ||
                           'Profissional';

    const clienteTelefone = (agendamento as any).cliente?.raw_user_meta_data?.phone || '';

    // Contexto para o cliente
    const contextoCliente: ContextoAgendamento = {
      agendamento_id: (agendamento as any).agendamento_id,
      codigo_confirmacao: (agendamento as any).codigo_confirmacao,
      cliente_nome: clienteNome,
      cliente_email: (agendamento as any).cliente.email,
      empresa_nome: (agendamento as any).empresa.nome_empresa,
      empresa_endereco: (agendamento as any).empresa.endereco_completo,
      servico_nome: (agendamento as any).servico.nome_servico,
      servico_preco: (agendamento as any).servico.preco,
      colaborador_nome: colaboradorNome,
      data_hora_inicio: (agendamento as any).data_hora_inicio,
      data_hora_fim: (agendamento as any).data_hora_fim,
      forma_pagamento: (agendamento as any).forma_pagamento,
      valor_total: (agendamento as any).valor_total,
      observacoes_cliente: (agendamento as any).observacoes_cliente,
      prazo_confirmacao: (agendamento as any).prazo_confirmacao
    };

    // Contexto para o proprietário/colaborador
    const contextoProprietario: ContextoProprietario = {
      agendamento_id: (agendamento as any).agendamento_id,
      codigo_confirmacao: (agendamento as any).codigo_confirmacao,
      cliente_nome: clienteNome,
      cliente_telefone: clienteTelefone,
      servico_nome: (agendamento as any).servico.nome_servico,
      colaborador_nome: colaboradorNome,
      data_hora_inicio: (agendamento as any).data_hora_inicio,
      data_hora_fim: (agendamento as any).data_hora_fim,
      forma_pagamento: (agendamento as any).forma_pagamento,
      valor_total: (agendamento as any).valor_total,
      observacoes_cliente: (agendamento as any).observacoes_cliente,
      prazo_confirmacao: (agendamento as any).prazo_confirmacao
    };

    return {
      contextoCliente,
      contextoProprietario,
      clienteId: (agendamento as any).cliente_user_id,
      proprietarioId: (colaboradorEmpresa as any).user_id,
      colaboradorId: (agendamento as any).colaborador_user_id,
      empresaId: (agendamento as any).empresa_id
    };

  } catch (error) {
    console.error('❌ Erro ao extrair contexto do agendamento:', error);
    return null;
  }
}

/**
 * Envia notificação de novo agendamento para cliente e proprietário
 */
export async function notificarNovoAgendamento(agendamentoId: number): Promise<boolean> {
  try {
    const contexto = await extrairContextoAgendamento(agendamentoId);
    
    if (!contexto) {
      console.error('❌ Não foi possível extrair contexto do agendamento');
      return false;
    }

    // Enviar notificação para o cliente
    const responseCliente = await fetch('/api/notifications', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        tipo: 'novo_agendamento',
        destinatario_id: contexto.clienteId,
        contexto: contexto.contextoCliente,
        canal: 'email',
        agendamento_id: agendamentoId,
        empresa_id: contexto.empresaId
      })
    });

    // Enviar notificação para o proprietário (sobre novo agendamento pendente)
    const responseProprietario = await fetch('/api/notifications', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        tipo: 'lembrete_confirmacao',
        destinatario_id: contexto.proprietarioId,
        contexto: contexto.contextoProprietario,
        canal: 'email',
        agendamento_id: agendamentoId,
        empresa_id: contexto.empresaId
      })
    });

    const resultadoCliente = await responseCliente.json();
    const resultadoProprietario = await responseProprietario.json();

    const sucessoCliente = responseCliente.ok && resultadoCliente.success;
    const sucessoProprietario = responseProprietario.ok && resultadoProprietario.success;

    if (sucessoCliente && sucessoProprietario) {
      console.log('✅ Notificações de novo agendamento enviadas com sucesso');
      return true;
    } else {
      console.error('❌ Erro ao enviar algumas notificações:', {
        cliente: sucessoCliente ? 'OK' : resultadoCliente.error,
        proprietario: sucessoProprietario ? 'OK' : resultadoProprietario.error
      });
      return false;
    }

  } catch (error) {
    console.error('❌ Erro ao notificar novo agendamento:', error);
    return false;
  }
}

/**
 * Envia notificação de confirmação de agendamento
 */
export async function notificarConfirmacaoAgendamento(agendamentoId: number): Promise<boolean> {
  try {
    const contexto = await extrairContextoAgendamento(agendamentoId);
    
    if (!contexto) {
      console.error('❌ Não foi possível extrair contexto do agendamento');
      return false;
    }

    // Enviar notificação para o cliente
    const response = await fetch('/api/notifications', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        tipo: 'agendamento_confirmado',
        destinatario_id: contexto.clienteId,
        contexto: contexto.contextoCliente,
        canal: 'email',
        agendamento_id: agendamentoId
      })
    });

    const resultado = await response.json();

    if (response.ok && resultado.success) {
      console.log('✅ Notificação de confirmação enviada com sucesso');
      return true;
    } else {
      console.error('❌ Erro ao enviar notificação de confirmação:', resultado.error);
      return false;
    }

  } catch (error) {
    console.error('❌ Erro ao notificar confirmação de agendamento:', error);
    return false;
  }
}

/**
 * Envia notificação de recusa de agendamento
 */
export async function notificarRecusaAgendamento(agendamentoId: number): Promise<boolean> {
  try {
    const contexto = await extrairContextoAgendamento(agendamentoId);
    
    if (!contexto) {
      console.error('❌ Não foi possível extrair contexto do agendamento');
      return false;
    }

    // Enviar notificação para o cliente
    const response = await fetch('/api/notifications', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        tipo: 'agendamento_recusado',
        destinatario_id: contexto.clienteId,
        contexto: contexto.contextoCliente,
        canal: 'email',
        agendamento_id: agendamentoId
      })
    });

    const resultado = await response.json();

    if (response.ok && resultado.success) {
      console.log('✅ Notificação de recusa enviada com sucesso');
      return true;
    } else {
      console.error('❌ Erro ao enviar notificação de recusa:', resultado.error);
      return false;
    }

  } catch (error) {
    console.error('❌ Erro ao notificar recusa de agendamento:', error);
    return false;
  }
}

/**
 * Envia notificação de cancelamento de agendamento
 */
export async function notificarCancelamentoAgendamento(agendamentoId: number): Promise<boolean> {
  try {
    const contexto = await extrairContextoAgendamento(agendamentoId);
    
    if (!contexto) {
      console.error('❌ Não foi possível extrair contexto do agendamento');
      return false;
    }

    // Enviar notificação para o cliente
    const response = await fetch('/api/notifications', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        tipo: 'agendamento_cancelado',
        destinatario_id: contexto.clienteId,
        contexto: contexto.contextoCliente,
        canal: 'email',
        agendamento_id: agendamentoId
      })
    });

    const resultado = await response.json();

    if (response.ok && resultado.success) {
      console.log('✅ Notificação de cancelamento enviada com sucesso');
      return true;
    } else {
      console.error('❌ Erro ao enviar notificação de cancelamento:', resultado.error);
      return false;
    }

  } catch (error) {
    console.error('❌ Erro ao notificar cancelamento de agendamento:', error);
    return false;
  }
}

/**
 * Envia notificação de pagamento confirmado
 */
export async function notificarPagamentoConfirmado(agendamentoId: number): Promise<boolean> {
  try {
    const contexto = await extrairContextoAgendamento(agendamentoId);

    if (!contexto) {
      console.error('❌ Não foi possível extrair contexto do agendamento');
      return false;
    }

    const { contextoCliente, contextoProprietario, clienteId, proprietarioId } = contexto;

    // Enviar notificação para o cliente
    const notificationService = new NotificationService();
    const resultadoCliente = await notificationService.processarNotificacao({
      destinatario_id: clienteId,
      tipo: 'pagamento_confirmado',
      contexto: contextoCliente,
      canal: 'email',
      agendamento_id: agendamentoId
    });

    // Enviar notificação para o proprietário
    const resultadoProprietario = await notificationService.processarNotificacao({
      destinatario_id: proprietarioId,
      tipo: 'pagamento_confirmado',
      contexto: contextoProprietario,
      canal: 'email',
      agendamento_id: agendamentoId
    });

    if (resultadoCliente.success && resultadoProprietario.success) {
      console.log(`✅ Notificações de pagamento confirmado enviadas para agendamento ${agendamentoId}`);
      return true;
    } else {
      console.error('❌ Erro ao enviar notificações de pagamento confirmado');
      return false;
    }
  } catch (error) {
    console.error('❌ Erro ao notificar pagamento confirmado:', error);
    return false;
  }
}
