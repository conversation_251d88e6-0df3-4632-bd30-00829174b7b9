'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { usePoliticasCancelamento } from '@/hooks/usePoliticasCancelamento';
import { useAuth } from '@/contexts/AuthContext';
import { Agendamento } from '@/types/agendamentos';

interface BotaoCancelamentoProps {
  agendamento: Agendamento;
  onCancelado?: () => void;
  variant?: 'primary' | 'secondary' | 'accent' | 'outline' | 'ghost' | 'link';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function BotaoCancelamento({
  agendamento,
  onCancelado,
  variant = 'outline',
  size = 'sm',
  className = ''
}: BotaoCancelamentoProps) {
  const { user } = useAuth();
  const { cancelarAgendamento, calcularRegrasCancelamento, formatarTempoRestante } = usePoliticasCancelamento();
  
  const [mostrarModal, setMostrarModal] = useState(false);
  const [motivo, setMotivo] = useState('');
  const [cancelando, setCancelando] = useState(false);
  const [erro, setErro] = useState<string | null>(null);

  // Determinar quem está cancelando
  const isCliente = agendamento.cliente_user_id === user?.id;
  const isProprietario = user?.role === 'Proprietario';
  const isColaborador = agendamento.colaborador_user_id === user?.id;
  const isAdmin = user?.role === 'Administrador';

  const canceladoPor = isCliente ? 'cliente' : 'empresa';

  // Verificar se pode mostrar o botão
  const podeAcessar = isCliente || isProprietario || isColaborador || isAdmin;
  const agendamentoCancelado = agendamento.status_agendamento === 'Cancelado';

  if (!podeAcessar || agendamentoCancelado) {
    return null;
  }

  // Calcular regras de cancelamento (simulação básica)
  const agora = new Date();
  const dataAgendamento = new Date(agendamento.data_hora_inicio);
  const horasAteAgendamento = (dataAgendamento.getTime() - agora.getTime()) / (1000 * 60 * 60);
  const agendamentoConfirmado = agendamento.status_agendamento === 'Confirmado';

  // Regras básicas para exibir o botão
  let podeExibirBotao = true;
  let motivoBloqueio = '';

  if (canceladoPor === 'cliente' && horasAteAgendamento < 2) {
    podeExibirBotao = false;
    motivoBloqueio = 'Prazo mínimo de 2h para cancelamento expirado';
  }

  if (horasAteAgendamento < 0) {
    podeExibirBotao = false;
    motivoBloqueio = 'Agendamento já passou';
  }

  const handleCancelar = async () => {
    try {
      setCancelando(true);
      setErro(null);

      const resultado = await cancelarAgendamento(agendamento.agendamento_id, {
        motivo: motivo.trim() || undefined,
        cancelado_por: canceladoPor,
        usuario_id: user!.id
      });

      if (resultado?.success) {
        setMostrarModal(false);
        setMotivo('');
        onCancelado?.();
      } else {
        setErro('Erro ao cancelar agendamento');
      }

    } catch (err) {
      setErro(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setCancelando(false);
    }
  };

  if (!podeExibirBotao) {
    return (
      <div className="relative group">
        <Button
          variant="outline"
          size={size}
          disabled
          className={`opacity-50 cursor-not-allowed ${className}`}
        >
          Cancelar
        </Button>
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
          {motivoBloqueio}
        </div>
      </div>
    );
  }

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={() => setMostrarModal(true)}
        className={className}
      >
        Cancelar
      </Button>

      {/* Modal de Confirmação */}
      {mostrarModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-2">
                Cancelar Agendamento
              </h3>
              <p className="text-sm text-[var(--text-secondary)]">
                Tem certeza que deseja cancelar este agendamento?
              </p>
            </div>

            {/* Informações do agendamento */}
            <div className="bg-[var(--background)] rounded-lg p-4 mb-4">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-[var(--text-secondary)]">Data/Hora:</span>
                  <span className="text-[var(--text-primary)] font-medium">
                    {new Date(agendamento.data_hora_inicio).toLocaleString('pt-BR')}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[var(--text-secondary)]">Valor:</span>
                  <span className="text-[var(--text-primary)] font-medium">
                    R$ {agendamento.valor_total.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[var(--text-secondary)]">Tempo restante:</span>
                  <span className="text-[var(--text-primary)] font-medium">
                    {formatarTempoRestante(horasAteAgendamento)}
                  </span>
                </div>
              </div>
            </div>

            {/* Informações de reembolso */}
            <div className="bg-[var(--warning-light)] border border-[var(--warning)] rounded-lg p-4 mb-4">
              <h4 className="font-medium text-[var(--text-primary)] mb-2">
                Política de Reembolso
              </h4>
              <div className="text-sm text-[var(--text-secondary)]">
                {canceladoPor === 'empresa' ? (
                  <p>✅ <strong>Reembolso integral (100%)</strong> - Cancelamento pela empresa</p>
                ) : horasAteAgendamento > 24 && !agendamentoConfirmado ? (
                  <p>✅ <strong>Reembolso integral (100%)</strong> - Mais de 24h de antecedência</p>
                ) : agendamentoConfirmado && horasAteAgendamento <= 24 ? (
                  <p>⚠️ <strong>Reembolso parcial</strong> - Conforme política da empresa</p>
                ) : (
                  <p>⚠️ <strong>Reembolso conforme política</strong> - Verificar condições</p>
                )}
              </div>
            </div>

            {/* Campo de motivo */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                Motivo do cancelamento (opcional)
              </label>
              <textarea
                value={motivo}
                onChange={(e) => setMotivo(e.target.value)}
                placeholder="Descreva o motivo do cancelamento..."
                className="w-full px-3 py-2 border border-[var(--border)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent resize-none"
                rows={3}
                maxLength={500}
              />
              <div className="text-xs text-[var(--text-secondary)] mt-1">
                {motivo.length}/500 caracteres
              </div>
            </div>

            {/* Erro */}
            {erro && (
              <div className="mb-4 p-3 bg-[var(--error-light)] border border-[var(--error)] rounded-lg">
                <p className="text-sm text-[var(--error)]">{erro}</p>
              </div>
            )}

            {/* Botões */}
            <div className="flex gap-3 justify-end">
              <Button
                variant="outline"
                onClick={() => {
                  setMostrarModal(false);
                  setMotivo('');
                  setErro(null);
                }}
                disabled={cancelando}
              >
                Voltar
              </Button>
              <Button
                variant="primary"
                onClick={handleCancelar}
                disabled={cancelando}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {cancelando ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Cancelando...
                  </div>
                ) : (
                  'Confirmar Cancelamento'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
