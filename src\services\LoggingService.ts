/**
 * Serviço de Logging Centralizado
 * Integra com Sentry e sistema de auditoria existente
 */

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  CRITICAL = 'critical'
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  context?: Record<string, any>;
  error?: Error;
  tags?: string[];
  fingerprint?: string[];
}

export interface LoggingConfig {
  enableConsole: boolean;
  enableSentry: boolean;
  enableDatabase: boolean;
  minLevel: LogLevel;
  environment: string;
  release?: string;
}

class LoggingService {
  private readonly config: LoggingConfig;
  private isInitialized = false;

  constructor() {
    this.config = {
      enableConsole: process.env.NODE_ENV === 'development',
      enableSentry: process.env.NODE_ENV === 'production',
      enableDatabase: true,
      minLevel: process.env.NODE_ENV === 'production' ? LogLevel.INFO : LogLevel.DEBUG,
      environment: process.env.NODE_ENV ?? 'development',
      release: process.env.NEXT_PUBLIC_APP_VERSION ?? '1.0.0'
    };
  }

  /**
   * Inicializa o serviço de logging
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Inicializar Sentry se habilitado
      if (this.config.enableSentry && typeof window !== 'undefined') {
        // Cliente (browser)
        const { init } = await import('@sentry/react');
        init({
          dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
          environment: this.config.environment,
          release: this.config.release,
          tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
          beforeSend: this.filterSensitiveData,
          integrations: [
            // Integração com React Router se necessário
          ]
        });
      } else if (this.config.enableSentry && typeof window === 'undefined') {
        // Servidor (Node.js)
        const { init } = await import('@sentry/node');
        init({
          dsn: process.env.SENTRY_DSN,
          environment: this.config.environment,
          release: this.config.release,
          tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
          beforeSend: this.filterSensitiveData
        });
      }

      this.isInitialized = true;
      this.info('LoggingService initialized successfully', {
        config: {
          ...this.config,
          enableSentry: this.config.enableSentry && !!process.env.NEXT_PUBLIC_SENTRY_DSN
        }
      });
    } catch (error) {
      console.error('Failed to initialize LoggingService:', error);
    }
  }

  /**
   * Filtra dados sensíveis antes de enviar para Sentry
   */
  private readonly filterSensitiveData = (event: any) => {
    // Lista de campos sensíveis para filtrar
    const sensitiveFields = [
      'password', 'token', 'secret', 'key', 'authorization',
      'cookie', 'session', 'credit_card', 'ssn', 'email'
    ];

    // Função recursiva para filtrar dados
    const filterObject = (obj: any): any => {
      if (!obj || typeof obj !== 'object') return obj;

      const filtered = Array.isArray(obj) ? [] : {};
      
      for (const [key, value] of Object.entries(obj)) {
        const lowerKey = key.toLowerCase();
        const isSensitive = sensitiveFields.some(field => lowerKey.includes(field));
        
        if (isSensitive) {
          (filtered as any)[key] = '[FILTERED]';
        } else if (typeof value === 'object' && value !== null) {
          (filtered as any)[key] = filterObject(value);
        } else {
          (filtered as any)[key] = value;
        }
      }
      
      return filtered;
    };

    // Filtrar dados do evento
    if (event.request?.data) {
      event.request.data = filterObject(event.request.data);
    }
    
    if (event.extra) {
      event.extra = filterObject(event.extra);
    }

    return event;
  };

  /**
   * Verifica se o nível de log deve ser processado
   */
  private shouldLog(level: LogLevel): boolean {
    const levels = [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR, LogLevel.CRITICAL];
    const currentLevelIndex = levels.indexOf(this.config.minLevel);
    const logLevelIndex = levels.indexOf(level);
    
    return logLevelIndex >= currentLevelIndex;
  }

  /**
   * Cria entrada de log estruturada
   */
  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: Record<string, any>,
    error?: Error
  ): LogEntry {
    return {
      level,
      message,
      timestamp: new Date().toISOString(),
      context: context || {},
      error,
      tags: [`level:${level}`, `env:${this.config.environment}`],
      fingerprint: error ? [error.name, error.message] : undefined
    };
  }

  /**
   * Processa e envia log para diferentes destinos
   */
  private async processLog(entry: LogEntry): Promise<void> {
    if (!this.shouldLog(entry.level)) return;

    // Log no console se habilitado
    if (this.config.enableConsole) {
      this.logToConsole(entry);
    }

    // Enviar para Sentry se habilitado
    if (this.config.enableSentry && this.isInitialized) {
      await this.logToSentry(entry);
    }

    // Salvar no banco de dados se habilitado
    if (this.config.enableDatabase) {
      await this.logToDatabase(entry);
    }
  }

  /**
   * Log no console com formatação
   */
  private logToConsole(entry: LogEntry): void {
    const prefix = `[${entry.timestamp}] [${entry.level.toUpperCase()}]`;
    const message = `${prefix} ${entry.message}`;
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(message, entry.context);
        break;
      case LogLevel.INFO:
        console.info(message, entry.context);
        break;
      case LogLevel.WARN:
        console.warn(message, entry.context);
        break;
      case LogLevel.ERROR:
      case LogLevel.CRITICAL:
        console.error(message, entry.context, entry.error);
        break;
    }
  }

  /**
   * Envia log para Sentry
   */
  private async logToSentry(entry: LogEntry): Promise<void> {
    try {
      if (typeof window !== 'undefined') {
        // Cliente
        const { captureMessage, captureException, addBreadcrumb } = await import('@sentry/react');
        
        if (entry.error) {
          captureException(entry.error, {
            tags: entry.tags?.reduce((acc, tag) => {
              const [key, value] = tag.split(':');
              acc[key] = value;
              return acc;
            }, {} as Record<string, string>),
            extra: entry.context,
            fingerprint: entry.fingerprint
          });
        } else {
          captureMessage(entry.message, entry.level as any);
          addBreadcrumb({
            message: entry.message,
            level: entry.level as any,
            data: entry.context
          });
        }
      } else {
        // Servidor
        const { captureMessage, captureException } = await import('@sentry/node');
        
        if (entry.error) {
          captureException(entry.error, {
            tags: entry.tags?.reduce((acc, tag) => {
              const [key, value] = tag.split(':');
              acc[key] = value;
              return acc;
            }, {} as Record<string, string>),
            extra: entry.context,
            fingerprint: entry.fingerprint
          });
        } else {
          captureMessage(entry.message, entry.level as any);
        }
      }
    } catch (error) {
      console.error('Failed to send log to Sentry:', error);
    }
  }

  /**
   * Salva log no banco de dados
   */
  private async logToDatabase(entry: LogEntry): Promise<void> {
    try {
      // Integrar com sistema de auditoria existente
      const { AuditLogger } = await import('@/utils/security/audit');
      
      if (entry.level === LogLevel.ERROR || entry.level === LogLevel.CRITICAL) {
        AuditLogger.securityViolation(
          entry.userId,
          entry.message,
          entry.sessionId,
          {
            ...entry.context,
            error: entry.error?.message,
            stack: entry.error?.stack
          }
        );
      } else {
        AuditLogger.dataAccess(
          entry.userId ?? 'system',
          'application_log',
          undefined,
          true
        );
      }
    } catch (error) {
      console.error('Failed to save log to database:', error);
    }
  }

  // Métodos públicos para diferentes níveis de log
  debug(message: string, context?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.DEBUG, message, context);
    this.processLog(entry);
  }

  info(message: string, context?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.INFO, message, context);
    this.processLog(entry);
  }

  warn(message: string, context?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.WARN, message, context);
    this.processLog(entry);
  }

  error(message: string, error?: Error, context?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.ERROR, message, context, error);
    this.processLog(entry);
  }

  critical(message: string, error?: Error, context?: Record<string, any>): void {
    const entry = this.createLogEntry(LogLevel.CRITICAL, message, context, error);
    this.processLog(entry);
  }

  /**
   * Configura contexto do usuário para logs
   */
  setUserContext(userId: string, email?: string, role?: string): void {
    if (typeof window !== 'undefined') {
      import('@sentry/react').then(({ setUser }) => {
        setUser({
          id: userId,
          email,
          role
        });
      });
    } else {
      import('@sentry/node').then(({ setUser }) => {
        setUser({
          id: userId,
          email,
          role
        });
      });
    }
  }

  /**
   * Limpa contexto do usuário
   */
  clearUserContext(): void {
    if (typeof window !== 'undefined') {
      import('@sentry/react').then(({ setUser }) => {
        setUser(null);
      });
    } else {
      import('@sentry/node').then(({ setUser }) => {
        setUser(null);
      });
    }
  }
}

// Instância singleton
export const logger = new LoggingService();

// Inicializar automaticamente
if (typeof window !== 'undefined') {
  // Cliente - inicializar após carregamento
  window.addEventListener('load', () => {
    logger.initialize();
  });
} else {
  // Servidor - inicializar imediatamente
  logger.initialize();
}

export default logger;
