'use client';

import React, { useEffect, useState } from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import { useGerenciamentoAgendamentos } from '@/hooks/useGerenciamentoAgendamentos';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { EstatisticasAgendamentos } from '@/components/agendamentos/EstatisticasAgendamentos';
import { CardAgendamento } from '@/components/agendamentos/CardAgendamento';
import Link from 'next/link';
import { parseISO, startOfWeek, endOfWeek } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export default function ColaboradorAgendaPage() {
  return (
    <ProtectedRoute requiredRole="Colaborador">
      <ColaboradorAgenda />
    </ProtectedRoute>
  );
}

function ColaboradorAgenda() {
  const { user } = useAuth();
  const [periodoSelecionado, setPeriodoSelecionado] = useState<'hoje' | 'semana' | 'mes'>('hoje');

  const {
    agendamentos,
    estatisticas,
    loading,
    buscarAgendamentos,
    confirmarAgendamento,
    recusarAgendamento,
    cancelarAgendamento,
    concluirAgendamento,
    marcarComoPago
  } = useGerenciamentoAgendamentos();

  // Carregar agendamentos ao montar o componente
  useEffect(() => {
    // Converter período para filtros de data
    const agora = new Date();
    let filtros = {};

    switch (periodoSelecionado) {
      case 'hoje':
        filtros = {
          data_inicio: agora.toISOString().split('T')[0],
          data_fim: agora.toISOString().split('T')[0]
        };
        break;
      case 'semana':
        const inicioSemana = new Date(agora);
        inicioSemana.setDate(agora.getDate() - agora.getDay());
        const fimSemana = new Date(inicioSemana);
        fimSemana.setDate(inicioSemana.getDate() + 6);
        filtros = {
          data_inicio: inicioSemana.toISOString().split('T')[0],
          data_fim: fimSemana.toISOString().split('T')[0]
        };
        break;
      case 'mes':
        const inicioMes = new Date(agora.getFullYear(), agora.getMonth(), 1);
        const fimMes = new Date(agora.getFullYear(), agora.getMonth() + 1, 0);
        filtros = {
          data_inicio: inicioMes.toISOString().split('T')[0],
          data_fim: fimMes.toISOString().split('T')[0]
        };
        break;
    }

    buscarAgendamentos(filtros);
  }, [buscarAgendamentos, periodoSelecionado]);

  // Filtrar agendamentos por período
  const obterAgendamentosPorPeriodo = () => {
    const agora = new Date();

    switch (periodoSelecionado) {
      case 'hoje': {
        return agendamentos.filter(a => {
          const dataAgendamento = parseISO(a.data_hora_inicio);
          return dataAgendamento.toDateString() === agora.toDateString();
        });
      }
      case 'semana': {
        const inicioSemana = startOfWeek(agora, { locale: ptBR });
        const fimSemana = endOfWeek(agora, { locale: ptBR });
        return agendamentos.filter(a => {
          const dataAgendamento = parseISO(a.data_hora_inicio);
          return dataAgendamento >= inicioSemana && dataAgendamento <= fimSemana;
        });
      }
      case 'mes': {
        return agendamentos.filter(a => {
          const dataAgendamento = parseISO(a.data_hora_inicio);
          return dataAgendamento.getMonth() === agora.getMonth() &&
                 dataAgendamento.getFullYear() === agora.getFullYear();
        });
      }
      default:
        return agendamentos;
    }
  };

  const agendamentosPendentes = agendamentos.filter(a => a.status_agendamento === 'Pendente');

  // Agendamentos próximos ao prazo (2 horas)
  const agendamentosProximoPrazo = agendamentosPendentes.filter(a => {
    const prazo = parseISO(a.prazo_confirmacao);
    const agora = new Date();
    const duasHoras = 2 * 60 * 60 * 1000;
    return (prazo.getTime() - agora.getTime()) < duasHoras && (prazo.getTime() - agora.getTime()) > 0;
  });

  // Próximo agendamento do dia
  const proximoAgendamento = (() => {
    const hoje = new Date();
    const agendamentosHoje = agendamentos.filter(a => {
      const dataAgendamento = parseISO(a.data_hora_inicio);
      return dataAgendamento.toDateString() === hoje.toDateString() &&
             ['Pendente', 'Confirmado'].includes(a.status_agendamento);
    }).sort((a, b) => new Date(a.data_hora_inicio).getTime() - new Date(b.data_hora_inicio).getTime());

    return agendamentosHoje.find(a => new Date(a.data_hora_inicio) > hoje);
  })();

  // Função para obter texto do período
  const obterTextoPeriodo = (periodo: string) => {
    switch (periodo) {
      case 'hoje': return 'Hoje';
      case 'semana': return 'Semana';
      case 'mes': return 'Mês';
      default: return 'Hoje';
    }
  };

  return (
    <div className="min-h-screen bg-[var(--background)]">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-[var(--border-color)]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-[var(--text-primary)]">
                Minha Agenda
              </h1>
              <p className="text-[var(--text-secondary)]">
                Bem-vindo, {user?.name || 'Colaborador'}!
              </p>
            </div>
            <div className="flex items-center gap-4">
              {/* Seletor de período */}
              <div className="flex bg-[var(--surface)] rounded-lg p-1">
                {(['hoje', 'semana', 'mes'] as const).map((periodo) => (
                  <button
                    key={periodo}
                    onClick={() => setPeriodoSelecionado(periodo)}
                    className={`px-3 py-1 rounded text-sm transition-colors ${
                      periodoSelecionado === periodo
                        ? 'bg-[var(--primary)] text-white'
                        : 'text-[var(--text-secondary)] hover:text-[var(--text-primary)]'
                    }`}
                  >
                    {obterTextoPeriodo(periodo)}
                  </button>
                ))}
              </div>
              <Link href="/">
                <Button variant="outline">
                  Voltar ao Início
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Alertas de agendamentos próximos ao prazo */}
        {agendamentosProximoPrazo.length > 0 && (
          <Card className="mb-6 border-orange-200 bg-orange-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="text-orange-600">⚠️</div>
                <div>
                  <div className="font-semibold text-orange-800">
                    Atenção: {agendamentosProximoPrazo.length} agendamento(s) próximo(s) ao prazo
                  </div>
                  <div className="text-sm text-orange-700">
                    Você tem agendamentos pendentes que precisam ser confirmados ou recusados em breve.
                  </div>
                </div>
                <div className="ml-auto">
                  <Link href="/colaborador/agendamentos">
                    <Button size="sm" className="bg-orange-600 hover:bg-orange-700">
                      Ver Agendamentos
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Próximo agendamento */}
        {proximoAgendamento && (
          <Card className="mb-6 border-blue-200 bg-blue-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="text-blue-600">🕐</div>
                <div>
                  <div className="font-semibold text-blue-800">
                    Próximo agendamento: {proximoAgendamento.servico.nome_servico}
                  </div>
                  <div className="text-sm text-blue-700">
                    {new Date(proximoAgendamento.data_hora_inicio).toLocaleTimeString('pt-BR', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })} - {proximoAgendamento.cliente?.name || 'Cliente não identificado'}
                  </div>
                </div>
                <div className="ml-auto">
                  <Link href="/colaborador/agendamentos">
                    <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                      Ver Detalhes
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Estatísticas do Período */}
        {estatisticas && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-[var(--text-primary)]">
                Estatísticas - {obterTextoPeriodo(periodoSelecionado)}
              </h2>
            </div>
            <EstatisticasAgendamentos
              estatisticas={estatisticas}
              loading={loading}
              periodo={periodoSelecionado}
            />
          </div>
        )}

        {/* Agendamentos Pendentes */}
        {agendamentosPendentes.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-[var(--text-primary)]">
                Solicitações Pendentes ({agendamentosPendentes.length})
              </h2>
              <Link href="/colaborador/agendamentos">
                <Button variant="outline" size="sm">
                  Ver Todos
                </Button>
              </Link>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {agendamentosPendentes.slice(0, 4).map(agendamento => (
                <CardAgendamento
                  key={agendamento.agendamento_id}
                  agendamento={agendamento}
                  onConfirmar={confirmarAgendamento}
                  onRecusar={recusarAgendamento}
                  loading={loading}
                  userRole="Colaborador"
                  mostrarAcoes={true}
                />
              ))}
            </div>
          </div>
        )}

        {/* Agendamentos do Dia */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-[var(--text-primary)]">
              Meus Agendamentos de Hoje
            </h2>
            <Link href="/colaborador/agendamentos">
              <Button variant="outline" size="sm">
                Ver Agenda Completa
              </Button>
            </Link>
          </div>

          {(() => {
            const agendamentosHoje = obterAgendamentosPorPeriodo().filter(a => {
              const hoje = new Date();
              const dataAgendamento = parseISO(a.data_hora_inicio);
              return dataAgendamento.toDateString() === hoje.toDateString();
            });

            if (agendamentosHoje.length === 0) {
              return (
                <Card>
                  <CardContent className="p-8 text-center">
                    <div className="text-[var(--text-secondary)]">
                      <svg className="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <p className="text-lg font-medium">Nenhum agendamento para hoje</p>
                      <p className="text-sm">Aproveite para descansar ou se preparar para os próximos dias!</p>
                    </div>
                  </CardContent>
                </Card>
              );
            }

            return (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {agendamentosHoje.slice(0, 6).map(agendamento => (
                  <CardAgendamento
                    key={agendamento.agendamento_id}
                    agendamento={agendamento}
                    onConfirmar={confirmarAgendamento}
                    onRecusar={recusarAgendamento}
                    onCancelar={cancelarAgendamento}
                    onConcluir={concluirAgendamento}
                    onMarcarPago={marcarComoPago}
                    loading={loading}
                    userRole="Colaborador"
                    mostrarAcoes={true}
                  />
                ))}
              </div>
            );
          })()}
        </div>

        {/* Ações Principais */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Minha Agenda */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-[var(--primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                Agenda Completa
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-secondary)] mb-4">
                Visualize e gerencie todos os seus agendamentos
              </p>
              <Link href="/colaborador/agendamentos">
                <Button className="w-full">
                  Ver Agenda Completa
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Histórico */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-[var(--primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Histórico
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-secondary)] mb-4">
                Veja seu histórico de atendimentos
              </p>
              <Link href="/colaborador/agendamentos">
                <Button className="w-full">
                  Ver Histórico
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Perfil */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-[var(--primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Meu Perfil
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-secondary)] mb-4">
                Gerencie suas informações pessoais
              </p>
              <Button className="w-full" disabled>
                Em Breve
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
