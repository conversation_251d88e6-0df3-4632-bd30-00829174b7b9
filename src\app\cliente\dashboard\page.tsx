'use client';

import React from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Layout } from '@/components/layout/Layout';
import Link from 'next/link';

export default function ClienteDashboardPage() {
  return (
    <ProtectedRoute requiredRole="Usuario">
      <Layout>
        <ClienteDashboard />
      </Layout>
    </ProtectedRoute>
  );
}

function ClienteDashboard() {
  const { user } = useAuth();

  return (
    <div className="bg-[var(--background)]">
      {/* Page Header */}
      <div className="bg-white shadow-sm border-b border-[var(--border-color)]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-[var(--text-primary)]">
                Dashboard do Cliente
              </h1>
              <p className="text-[var(--text-secondary)]">
                Bem-vindo, {user?.name || 'Cliente'}!
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Meus Agendamentos */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-[var(--primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                Meus Agendamentos
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-secondary)] mb-4">
                Visualize e gerencie seus agendamentos
              </p>
              <Link href="/cliente/agendamentos">
                <Button className="w-full">
                  Ver Agendamentos
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Buscar Estabelecimentos */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-[var(--primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                Buscar Estabelecimentos
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-secondary)] mb-4">
                Encontre estabelecimentos próximos
              </p>
              <Link href="/buscar">
                <Button className="w-full">
                  Buscar
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Meu Perfil */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-[var(--primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Meu Perfil
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-secondary)] mb-4">
                Gerencie suas informações pessoais
              </p>
              <Button className="w-full">
                Editar Perfil
              </Button>
            </CardContent>
          </Card>

          {/* Histórico */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-[var(--primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Histórico
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-secondary)] mb-4">
                Veja seu histórico de agendamentos
              </p>
              <Button className="w-full">
                Ver Histórico
              </Button>
            </CardContent>
          </Card>

          {/* Favoritos */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-[var(--primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                Favoritos
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-secondary)] mb-4">
                Seus estabelecimentos favoritos
              </p>
              <Button className="w-full">
                Ver Favoritos
              </Button>
            </CardContent>
          </Card>

          {/* Avaliações */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-[var(--primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                </svg>
                Minhas Avaliações
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-secondary)] mb-4">
                Avalie os serviços utilizados
              </p>
              <Button className="w-full">
                Ver Avaliações
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Informações do Usuário */}
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>Informações da Conta</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-[var(--text-secondary)] mb-1">
                    Nome
                  </label>
                  <p className="text-[var(--text-primary)]">{user?.name || 'Não informado'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-[var(--text-secondary)] mb-1">
                    E-mail
                  </label>
                  <p className="text-[var(--text-primary)]">{user?.email}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-[var(--text-secondary)] mb-1">
                    Telefone
                  </label>
                  <p className="text-[var(--text-primary)]">{user?.phone || 'Não informado'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-[var(--text-secondary)] mb-1">
                    Tipo de Conta
                  </label>
                  <p className="text-[var(--text-primary)]">{user?.role}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
