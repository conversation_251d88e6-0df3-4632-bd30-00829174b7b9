'use client';

import React from 'react';
import Link from 'next/link';

interface SkipLink {
  href: string;
  label: string;
}

interface SkipLinksProps {
  links?: SkipLink[];
}

const defaultLinks: SkipLink[] = [
  { href: '#main-content', label: 'Pular para o conteúdo principal' },
  { href: '#main-navigation', label: 'Pular para a navegação principal' },
  { href: '#footer', label: 'Pular para o rodapé' },
];

export function SkipLinks({ links = defaultLinks }: SkipLinksProps) {
  return (
    <div className="sr-only focus-within:not-sr-only">
      <nav aria-label="Links de navegação rápida" className="fixed top-0 left-0 z-50">
        <ul className="flex flex-col">
          {links.map((link, index) => (
            <li key={index}>
              <Link
                href={link.href}
                className="
                  block px-4 py-2 bg-[var(--primary)] text-[var(--text-on-primary)] 
                  font-medium text-sm border-b border-[var(--primary-hover)]
                  focus:outline-none focus:ring-2 focus:ring-[var(--accent)] 
                  focus:ring-offset-2 hover:bg-[var(--primary-hover)]
                  transition-colors duration-200
                "
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    const target = document.querySelector(link.href);
                    if (target) {
                      target.scrollIntoView({ behavior: 'smooth' });
                      // Focar no elemento se ele for focável
                      if (target instanceof HTMLElement && target.tabIndex >= 0) {
                        target.focus();
                      }
                    }
                  }
                }}
              >
                {link.label}
              </Link>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
}

// Hook para gerenciar foco em elementos
export function useFocusManagement() {
  const focusElement = (selector: string) => {
    const element = document.querySelector(selector) as HTMLElement;
    if (element) {
      element.focus();
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  const trapFocus = (containerRef: React.RefObject<HTMLElement>) => {
    const container = containerRef.current;
    if (!container) return;

    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      }
      
      if (e.key === 'Escape') {
        const closeButton = container.querySelector('[data-close]') as HTMLElement;
        if (closeButton) {
          closeButton.click();
        }
      }
    };

    container.addEventListener('keydown', handleKeyDown);
    
    // Focar no primeiro elemento focável
    if (firstElement) {
      firstElement.focus();
    }

    return () => {
      container.removeEventListener('keydown', handleKeyDown);
    };
  };

  return { focusElement, trapFocus };
}

// Componente para anunciar mudanças para screen readers
export function LiveRegion({ 
  message, 
  level = 'polite' 
}: { 
  message: string; 
  level?: 'polite' | 'assertive' 
}) {
  return (
    <div
      aria-live={level}
      aria-atomic="true"
      className="sr-only"
      role="status"
    >
      {message}
    </div>
  );
}
