import { useState, useEffect, useCallback } from 'react';
import { 
  Cupom, 
  CriarCupomData, 
  FiltrosCupons, 
  RespostaCupons,
  ResultadoAplicacaoCupom 
} from '@/types/marketing';

interface UseCuponsReturn {
  // Estado
  cupons: Cupom[];
  carregando: boolean;
  erro: string | null;
  total: number;
  paginaAtual: number;
  totalPaginas: number;

  // Ações
  buscarCupons: (filtros?: FiltrosCupons, pagina?: number) => Promise<void>;
  criarCupom: (dados: CriarCupomData) => Promise<boolean>;
  atualizarCupom: (id: number, dados: Partial<CriarCupomData>) => Promise<boolean>;
  excluirCupom: (id: number) => Promise<boolean>;
  validarCupom: (codigo: string, empresaId: number, servicosIds: number[], valorTotal: number) => Promise<ResultadoAplicacaoCupom | null>;
  buscarCupomPorId: (id: number) => Promise<Cupom | null>;
  
  // Utilitários
  limparErro: () => void;
  recarregar: () => Promise<void>;
}

export function useCupons(): UseCuponsReturn {
  const [cupons, setCupons] = useState<Cupom[]>([]);
  const [carregando, setCarregando] = useState(false);
  const [erro, setErro] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [paginaAtual, setPaginaAtual] = useState(1);
  const [totalPaginas, setTotalPaginas] = useState(0);
  const [filtrosAtuais, setFiltrosAtuais] = useState<FiltrosCupons>({});

  // Buscar cupons
  const buscarCupons = useCallback(async (filtros: FiltrosCupons = {}, pagina: number = 1) => {
    try {
      setCarregando(true);
      setErro(null);

      const params = new URLSearchParams();
      
      if (filtros.busca) params.append('busca', filtros.busca);
      if (filtros.status && filtros.status !== 'todos') params.append('status', filtros.status);
      if (filtros.tipo_desconto && filtros.tipo_desconto !== 'todos') params.append('tipo_desconto', filtros.tipo_desconto);
      if (filtros.data_inicio) params.append('data_inicio', filtros.data_inicio);
      if (filtros.data_fim) params.append('data_fim', filtros.data_fim);
      
      params.append('pagina', pagina.toString());
      params.append('limite', '10');

      const response = await fetch(`/api/cupons?${params.toString()}`);
      const resultado: RespostaCupons = await response.json();

      if (!resultado.success) {
        throw new Error(resultado.error || 'Erro ao buscar cupons');
      }

      if (resultado.data) {
        setCupons(resultado.data.cupons);
        setTotal(resultado.data.total);
        setPaginaAtual(resultado.data.pagina);
        setTotalPaginas(resultado.data.total_paginas);
        setFiltrosAtuais(filtros);
      }

    } catch (error) {
      console.error('Erro ao buscar cupons:', error);
      setErro(error instanceof Error ? error.message : 'Erro desconhecido');
      setCupons([]);
    } finally {
      setCarregando(false);
    }
  }, []);

  // Criar cupom
  const criarCupom = useCallback(async (dados: CriarCupomData): Promise<boolean> => {
    try {
      setCarregando(true);
      setErro(null);

      const response = await fetch('/api/cupons', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dados),
      });

      const resultado = await response.json();

      if (!resultado.success) {
        throw new Error(resultado.error || 'Erro ao criar cupom');
      }

      // Recarregar lista
      await buscarCupons(filtrosAtuais, paginaAtual);
      return true;

    } catch (error) {
      console.error('Erro ao criar cupom:', error);
      setErro(error instanceof Error ? error.message : 'Erro desconhecido');
      return false;
    } finally {
      setCarregando(false);
    }
  }, [buscarCupons, filtrosAtuais, paginaAtual]);

  // Atualizar cupom
  const atualizarCupom = useCallback(async (id: number, dados: Partial<CriarCupomData>): Promise<boolean> => {
    try {
      setCarregando(true);
      setErro(null);

      const response = await fetch(`/api/cupons/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dados),
      });

      const resultado = await response.json();

      if (!resultado.success) {
        throw new Error(resultado.error || 'Erro ao atualizar cupom');
      }

      // Atualizar cupom na lista local
      setCupons(prev => prev.map(cupom => 
        cupom.cupom_id === id ? { ...cupom, ...dados } : cupom
      ));

      return true;

    } catch (error) {
      console.error('Erro ao atualizar cupom:', error);
      setErro(error instanceof Error ? error.message : 'Erro desconhecido');
      return false;
    } finally {
      setCarregando(false);
    }
  }, []);

  // Excluir cupom
  const excluirCupom = useCallback(async (id: number): Promise<boolean> => {
    try {
      setCarregando(true);
      setErro(null);

      const response = await fetch(`/api/cupons/${id}`, {
        method: 'DELETE',
      });

      const resultado = await response.json();

      if (!resultado.success) {
        throw new Error(resultado.error || 'Erro ao excluir cupom');
      }

      // Remover cupom da lista local
      setCupons(prev => prev.filter(cupom => cupom.cupom_id !== id));
      setTotal(prev => prev - 1);

      return true;

    } catch (error) {
      console.error('Erro ao excluir cupom:', error);
      setErro(error instanceof Error ? error.message : 'Erro desconhecido');
      return false;
    } finally {
      setCarregando(false);
    }
  }, []);

  // Validar cupom
  const validarCupom = useCallback(async (
    codigo: string, 
    empresaId: number, 
    servicosIds: number[], 
    valorTotal: number
  ): Promise<ResultadoAplicacaoCupom | null> => {
    try {
      const response = await fetch('/api/cupons/validar', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          codigo_cupom: codigo,
          empresa_id: empresaId,
          servicos_ids: servicosIds,
          valor_total: valorTotal
        }),
      });

      const resultado = await response.json();

      if (!resultado.success) {
        throw new Error(resultado.error || 'Erro ao validar cupom');
      }

      return resultado.data;

    } catch (error) {
      console.error('Erro ao validar cupom:', error);
      setErro(error instanceof Error ? error.message : 'Erro desconhecido');
      return null;
    }
  }, []);

  // Buscar cupom por ID
  const buscarCupomPorId = useCallback(async (id: number): Promise<Cupom | null> => {
    try {
      const response = await fetch(`/api/cupons/${id}`);
      const resultado = await response.json();

      if (!resultado.success) {
        throw new Error(resultado.error || 'Erro ao buscar cupom');
      }

      return resultado.data;

    } catch (error) {
      console.error('Erro ao buscar cupom por ID:', error);
      setErro(error instanceof Error ? error.message : 'Erro desconhecido');
      return null;
    }
  }, []);

  // Limpar erro
  const limparErro = useCallback(() => {
    setErro(null);
  }, []);

  // Recarregar
  const recarregar = useCallback(async () => {
    await buscarCupons(filtrosAtuais, paginaAtual);
  }, [buscarCupons, filtrosAtuais, paginaAtual]);

  // Carregar cupons iniciais
  useEffect(() => {
    buscarCupons();
  }, [buscarCupons]);

  return {
    // Estado
    cupons,
    carregando,
    erro,
    total,
    paginaAtual,
    totalPaginas,

    // Ações
    buscarCupons,
    criarCupom,
    atualizarCupom,
    excluirCupom,
    validarCupom,
    buscarCupomPorId,

    // Utilitários
    limparErro,
    recarregar,
  };
}
