# PLANO DE EXECUÇÃO DA MIGRAÇÃO - SERVICETECH

## RESUMO EXECUTIVO

Este documento detalha o plano de migração segura do banco de dados ServiceTech, garantindo:
- **Zero perda de dados** existentes
- **Downtime mínimo** durante a migração
- **Rollback completo** em caso de problemas
- **Validação** de integridade dos dados

## ESTRATÉGIA DE MIGRAÇÃO

### Fase 1: Preparação (Sem Downtime)
1. **Backup completo** do banco de dados
2. **Criação de tabelas auxiliares** para validação
3. **Testes em ambiente de desenvolvimento**

### Fase 2: Migração Estrutural (Downtime: ~5-10 minutos)
1. **Criação de novas tabelas** e índices
2. **Adição de colunas** nas tabelas existentes
3. **Migração de dados** para nova estrutura
4. **Atualização de políticas RLS**

### Fase 3: Validação e Ativação (Sem Downtime)
1. **Validação de integridade** dos dados
2. **Testes funcionais** básicos
3. **Ativação gradual** das novas funcionalidades

## CRONOGRAMA DETALHADO

### PRÉ-MIGRAÇÃO (1-2 dias antes)

#### 1. Backup e Preparação
```sql
-- Criar backup completo
pg_dump servicetech_db > backup_pre_migration_$(date +%Y%m%d_%H%M%S).sql

-- Verificar integridade atual
SELECT COUNT(*) FROM agendamentos;
SELECT COUNT(*) FROM empresas;
SELECT COUNT(*) FROM colaboradores_empresa;
SELECT COUNT(*) FROM servicos;
```

#### 2. Validação de Dados Existentes
```sql
-- Verificar dados inconsistentes
SELECT * FROM agendamentos WHERE empresa_id NOT IN (SELECT empresa_id FROM empresas);
SELECT * FROM colaboradores_empresa WHERE empresa_id NOT IN (SELECT empresa_id FROM empresas);
SELECT * FROM servicos WHERE empresa_id NOT IN (SELECT empresa_id FROM empresas);

-- Verificar usuários sem papel definido
SELECT id, email FROM auth.users WHERE raw_user_meta_data->>'role' IS NULL;
```

#### 3. Preparar Scripts de Migração
- Validar sintaxe SQL
- Testar em ambiente de desenvolvimento
- Preparar scripts de rollback

### EXECUÇÃO DA MIGRAÇÃO

#### JANELA DE MANUTENÇÃO (Estimativa: 10-15 minutos)

##### Passo 1: Início da Manutenção (1 minuto)
```sql
-- Colocar aplicação em modo manutenção
-- Finalizar transações ativas
SELECT pg_terminate_backend(pid) FROM pg_stat_activity 
WHERE datname = 'servicetech_db' AND state = 'active' AND pid != pg_backend_pid();
```

##### Passo 2: Backup Final (2 minutos)
```sql
-- Backup imediatamente antes da migração
pg_dump servicetech_db > backup_final_pre_migration_$(date +%Y%m%d_%H%M%S).sql
```

##### Passo 3: Execução da Migração (5-8 minutos)
```sql
-- Executar script principal de migração
\i database_migration_plan.sql
```

##### Passo 4: Validação Rápida (2 minutos)
```sql
-- Verificar se todas as tabelas foram criadas
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' AND table_name IN ('roles', 'user_roles', 'convites_colaborador');

-- Verificar se dados foram migrados
SELECT COUNT(*) FROM user_roles;
SELECT COUNT(*) FROM roles;

-- Verificar integridade referencial
SELECT COUNT(*) FROM agendamentos a 
LEFT JOIN empresas e ON a.empresa_id = e.empresa_id 
WHERE e.empresa_id IS NULL;
```

##### Passo 5: Ativação (1 minuto)
```sql
-- Remover modo manutenção
-- Reiniciar aplicação
```

## VALIDAÇÃO PÓS-MIGRAÇÃO

### Testes Automáticos (15 minutos)
1. **Verificar contagem de registros**
2. **Testar políticas RLS**
3. **Validar relacionamentos**
4. **Testar funções criadas**

### Testes Funcionais (30 minutos)
1. **Login de usuários** existentes
2. **Criação de agendamentos**
3. **Gestão de colaboradores**
4. **Relatórios básicos**

## ESTRATÉGIA DE ROLLBACK

### Cenários de Rollback

#### Cenário 1: Falha na Migração de Dados
```sql
-- Restaurar backup
psql servicetech_db < backup_final_pre_migration_YYYYMMDD_HHMMSS.sql
```

#### Cenário 2: Problemas de Performance
```sql
-- Reverter apenas estruturas problemáticas
-- Executar seção de rollback do script principal
```

#### Cenário 3: Problemas de Aplicação
- Reverter deploy da aplicação
- Manter estrutura do banco (compatível)
- Investigar e corrigir problemas

### Tempo de Rollback
- **Rollback completo**: 5-10 minutos
- **Rollback parcial**: 2-5 minutos

## MONITORAMENTO PÓS-MIGRAÇÃO

### Primeiras 24 horas
- **Performance** das queries principais
- **Logs de erro** da aplicação
- **Uso de recursos** do banco
- **Feedback** dos usuários

### Primeira semana
- **Relatórios** de integridade
- **Análise** de performance
- **Ajustes** de índices se necessário

## COMUNICAÇÃO

### Antes da Migração
- **Notificar usuários** sobre janela de manutenção
- **Preparar equipe** de suporte
- **Documentar** procedimentos

### Durante a Migração
- **Updates** em tempo real para stakeholders
- **Monitoramento** ativo do progresso

### Após a Migração
- **Confirmação** de sucesso
- **Relatório** de migração
- **Documentação** atualizada

## CRITÉRIOS DE SUCESSO

### Obrigatórios
- ✅ Zero perda de dados
- ✅ Todas as funcionalidades existentes funcionando
- ✅ Performance mantida ou melhorada
- ✅ Políticas RLS funcionando corretamente

### Desejáveis
- ✅ Downtime menor que 15 minutos
- ✅ Sem necessidade de rollback
- ✅ Feedback positivo dos usuários
- ✅ Logs limpos sem erros

## RESPONSABILIDADES

### Equipe de Banco de Dados
- Execução da migração
- Monitoramento de performance
- Backup e rollback

### Equipe de Desenvolvimento
- Testes da aplicação
- Deploy coordenado
- Correção de bugs

### Equipe de Infraestrutura
- Monitoramento de recursos
- Suporte técnico
- Comunicação com usuários
