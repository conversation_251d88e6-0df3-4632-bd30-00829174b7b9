'use client';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function RegistroEmpresaPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [planoSelecionado, setPlanoSelecionado] = useState<string | null>(null);
  
  const [formData, setFormData] = useState({
    nomeEstabelecimento: '',
    cnpj: '',
    telefone: '',
    cep: '',
    endereco: '',
    numero: '',
    complemento: '',
    bairro: '',
    cidade: '',
    estado: '',
    segmento: 'beleza', // Valor padrão
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    // Recuperar o plano selecionado do localStorage
    const plano = localStorage.getItem('planoSelecionado');
    if (!plano) {
      // Se não houver plano selecionado, redirecionar para a seleção de plano
      router.push('/onboarding/selecao-plano');
    } else {
      setPlanoSelecionado(plano);
    }
  }, [router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Limpar erro do campo quando o usuário começa a digitar
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleCepBlur = async () => {
    const cep = formData.cep.replace(/\D/g, '');
    
    if (cep.length === 8) {
      try {
        const response = await fetch(`https://viacep.com.br/ws/${cep}/json/`);
        const data = await response.json();
        
        if (!data.erro) {
          setFormData(prev => ({
            ...prev,
            endereco: data.logradouro,
            bairro: data.bairro,
            cidade: data.localidade,
            estado: data.uf
          }));
        }
      } catch (error) {
        console.error('Erro ao buscar CEP:', error);
      }
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.nomeEstabelecimento.trim()) {
      newErrors.nomeEstabelecimento = 'Nome do estabelecimento é obrigatório';
    }
    
    if (!formData.cnpj.trim()) {
      newErrors.cnpj = 'CNPJ é obrigatório';
    } else if (!/^\d{14}$/.test(formData.cnpj.replace(/\D/g, ''))) {
      newErrors.cnpj = 'CNPJ inválido';
    }
    
    if (!formData.telefone.trim()) {
      newErrors.telefone = 'Telefone é obrigatório';
    }
    
    if (!formData.cep.trim()) {
      newErrors.cep = 'CEP é obrigatório';
    }
    
    if (!formData.endereco.trim()) {
      newErrors.endereco = 'Endereço é obrigatório';
    }
    
    if (!formData.numero.trim()) {
      newErrors.numero = 'Número é obrigatório';
    }
    
    if (!formData.bairro.trim()) {
      newErrors.bairro = 'Bairro é obrigatório';
    }
    
    if (!formData.cidade.trim()) {
      newErrors.cidade = 'Cidade é obrigatória';
    }
    
    if (!formData.estado.trim()) {
      newErrors.estado = 'Estado é obrigatório';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      setLoading(true);
      
      // Armazenar os dados do estabelecimento no localStorage
      localStorage.setItem('dadosEstabelecimento', JSON.stringify(formData));
      
      // Redirecionar para a próxima etapa do onboarding
      router.push('/onboarding/cadastro-servico');
    }
  };

  const segmentos = [
    { value: 'beleza', label: 'Salão de Beleza' },
    { value: 'estetica', label: 'Clínica de Estética' },
    { value: 'barbearia', label: 'Barbearia' },
    { value: 'spa', label: 'SPA' },
    { value: 'manicure', label: 'Manicure e Pedicure' },
    { value: 'outro', label: 'Outro' }
  ];

  return (
    <div className="min-h-screen bg-[var(--background)] py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto bg-[var(--surface)] rounded-lg shadow-xl p-8">
          <h1 className="text-4xl font-extrabold text-center text-[var(--text-primary)] mb-4">
            Registro do Estabelecimento
          </h1>
          <p className="text-lg text-[var(--text-secondary)] text-center mb-10">
            Informe os dados do seu estabelecimento para continuar
          </p>
          
          {planoSelecionado && (
            <div className="mb-8 p-4 bg-blue-100 text-blue-800 rounded-md border border-blue-200">
              <p className="font-semibold">
                Plano selecionado: {planoSelecionado === 'essencial' ? 'Essencial' : 'Premium'}
              </p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Nome do Estabelecimento */}
              <div className="col-span-2">
                <label htmlFor="nomeEstabelecimento" className="block text-sm font-medium text-[var(--text-primary)] mb-1.5">
                  Nome do Estabelecimento *
                </label>
                <input
                  type="text"
                  id="nomeEstabelecimento"
                  name="nomeEstabelecimento"
                  value={formData.nomeEstabelecimento}
                  onChange={handleChange}
                  className={`w-full px-4 py-2.5 border rounded-md focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent placeholder-[var(--text-secondary)] ${errors.nomeEstabelecimento ? 'border-red-500' : 'border-[var(--border-color)]'}`}
                  placeholder="Nome do seu estabelecimento"
                />
                {errors.nomeEstabelecimento && (
                  <p className="mt-1.5 text-sm text-red-600">{errors.nomeEstabelecimento}</p>
                )}
              </div>

              {/* CNPJ */}
              <div>
                <label htmlFor="cnpj" className="block text-sm font-medium text-[var(--text-primary)] mb-1.5">
                  CNPJ *
                </label>
                <input
                  type="text"
                  id="cnpj"
                  name="cnpj"
                  value={formData.cnpj}
                  onChange={handleChange}
                  className={`w-full px-4 py-2.5 border rounded-md focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent placeholder-[var(--text-secondary)] ${errors.cnpj ? 'border-red-500' : 'border-[var(--border-color)]'}`}
                  placeholder="00.000.000/0000-00"
                />
                {errors.cnpj && (
                  <p className="mt-1.5 text-sm text-red-600">{errors.cnpj}</p>
                )}
              </div>

              {/* Telefone */}
              <div>
                <label htmlFor="telefone" className="block text-sm font-medium text-[var(--text-primary)] mb-1.5">
                  Telefone *
                </label>
                <input
                  type="text"
                  id="telefone"
                  name="telefone"
                  value={formData.telefone}
                  onChange={handleChange}
                  className={`w-full px-4 py-2.5 border rounded-md focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent placeholder-[var(--text-secondary)] ${errors.telefone ? 'border-red-500' : 'border-[var(--border-color)]'}`}
                  placeholder="(00) 00000-0000"
                />
                {errors.telefone && (
                  <p className="mt-1.5 text-sm text-red-600">{errors.telefone}</p>
                )}
              </div>

              {/* Segmento */}
              <div className="col-span-2">
                <label htmlFor="segmento" className="block text-sm font-medium text-[var(--text-primary)] mb-1.5">
                  Segmento *
                </label>
                <select
                  id="segmento"
                  name="segmento"
                  value={formData.segmento}
                  onChange={handleChange}
                  className="w-full px-4 py-2.5 border border-[var(--border-color)] rounded-md focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
                >
                  {segmentos.map(segmento => (
                    <option key={segmento.value} value={segmento.value}>
                      {segmento.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* CEP */}
              <div>
                <label htmlFor="cep" className="block text-sm font-medium text-[var(--text-primary)] mb-1.5">
                  CEP *
                </label>
                <input
                  type="text"
                  id="cep"
                  name="cep"
                  value={formData.cep}
                  onChange={handleChange}
                  onBlur={handleCepBlur}
                  className={`w-full px-4 py-2.5 border rounded-md focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent placeholder-[var(--text-secondary)] ${errors.cep ? 'border-red-500' : 'border-[var(--border-color)]'}`}
                  placeholder="00000-000"
                />
                {errors.cep && (
                  <p className="mt-1.5 text-sm text-red-600">{errors.cep}</p>
                )}
              </div>

              {/* Endereço */}
              <div>
                <label htmlFor="endereco" className="block text-sm font-medium text-[var(--text-primary)] mb-1.5">
                  Endereço *
                </label>
                <input
                  type="text"
                  id="endereco"
                  name="endereco"
                  value={formData.endereco}
                  onChange={handleChange}
                  className={`w-full px-4 py-2.5 border rounded-md focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent placeholder-[var(--text-secondary)] ${errors.endereco ? 'border-red-500' : 'border-[var(--border-color)]'}`}
                  placeholder="Rua, Avenida, etc."
                />
                {errors.endereco && (
                  <p className="mt-1.5 text-sm text-red-600">{errors.endereco}</p>
                )}
              </div>

              {/* Número */}
              <div>
                <label htmlFor="numero" className="block text-sm font-medium text-[var(--text-primary)] mb-1.5">
                  Número *
                </label>
                <input
                  type="text"
                  id="numero"
                  name="numero"
                  value={formData.numero}
                  onChange={handleChange}
                  className={`w-full px-4 py-2.5 border rounded-md focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent placeholder-[var(--text-secondary)] ${errors.numero ? 'border-red-500' : 'border-[var(--border-color)]'}`}
                  placeholder="123"
                />
                {errors.numero && (
                  <p className="mt-1.5 text-sm text-red-600">{errors.numero}</p>
                )}
              </div>

              {/* Complemento */}
              <div>
                <label htmlFor="complemento" className="block text-sm font-medium text-[var(--text-primary)] mb-1.5">
                  Complemento
                </label>
                <input
                  type="text"
                  id="complemento"
                  name="complemento"
                  value={formData.complemento}
                  onChange={handleChange}
                  className="w-full px-4 py-2.5 border border-[var(--border-color)] rounded-md focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent placeholder-[var(--text-secondary)]"
                  placeholder="Sala, Andar, etc."
                />
              </div>

              {/* Bairro */}
              <div>
                <label htmlFor="bairro" className="block text-sm font-medium text-[var(--text-primary)] mb-1.5">
                  Bairro *
                </label>
                <input
                  type="text"
                  id="bairro"
                  name="bairro"
                  value={formData.bairro}
                  onChange={handleChange}
                  className={`w-full px-4 py-2.5 border rounded-md focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent placeholder-[var(--text-secondary)] ${errors.bairro ? 'border-red-500' : 'border-[var(--border-color)]'}`}
                  placeholder="Seu bairro"
                />
                {errors.bairro && (
                  <p className="mt-1.5 text-sm text-red-600">{errors.bairro}</p>
                )}
              </div>

              {/* Cidade */}
              <div>
                <label htmlFor="cidade" className="block text-sm font-medium text-[var(--text-primary)] mb-1.5">
                  Cidade *
                </label>
                <input
                  type="text"
                  id="cidade"
                  name="cidade"
                  value={formData.cidade}
                  onChange={handleChange}
                  className={`w-full px-4 py-2.5 border rounded-md focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent placeholder-[var(--text-secondary)] ${errors.cidade ? 'border-red-500' : 'border-[var(--border-color)]'}`}
                  placeholder="Sua cidade"
                />
                {errors.cidade && (
                  <p className="mt-1.5 text-sm text-red-600">{errors.cidade}</p>
                )}
              </div>

              {/* Estado */}
              <div>
                <label htmlFor="estado" className="block text-sm font-medium text-[var(--text-primary)] mb-1.5">
                  Estado *
                </label>
                <input
                  type="text"
                  id="estado"
                  name="estado"
                  value={formData.estado}
                  onChange={handleChange}
                  className={`w-full px-4 py-2.5 border rounded-md focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent placeholder-[var(--text-secondary)] ${errors.estado ? 'border-red-500' : 'border-[var(--border-color)]'}`}
                  placeholder="UF"
                  maxLength={2}
                />
                {errors.estado && (
                  <p className="mt-1.5 text-sm text-red-600">{errors.estado}</p>
                )}
              </div>
            </div>

            <div className="flex justify-between items-center mt-10">
              <Link href="/onboarding/selecao-plano" className="text-[var(--text-secondary)] hover:text-[var(--text-primary)] transition duration-300 font-medium">
                Voltar para seleção de plano
              </Link>
              <button
                type="submit"
                disabled={loading}
                className="px-8 py-3 bg-[var(--primary)] text-white rounded-md font-semibold hover:bg-[var(--primary-hover)] transition duration-300 shadow-md"
              >
                {loading ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processando...
                  </span>
                ) : 'Continuar para pagamento'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
