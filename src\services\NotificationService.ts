import { Resend } from 'resend';
import { createAdminClient } from '@/utils/supabase/server';
import {
  TipoNotificacao,
  CanalNotificacao,
  ProcessarNotificacaoData,
  ResultadoNotificacao,
  EmailResponse,
  EnviarEmailData,
  ContextoAgendamento,
  ContextoProprietario
} from '@/types/notifications';
import { EmailTemplates } from '@/templates/email/EmailTemplates';
import { SMSService } from './SMSService';
import { PushService } from './PushService';

export class NotificationService {
  private readonly resend: Resend;
  private readonly supabase = createAdminClient();
  private readonly fromEmail: string;
  private readonly smsService?: SMSService;
  private readonly pushService?: PushService;

  constructor() {
    const apiKey = process.env.RESEND_API_KEY;
    this.fromEmail = process.env.RESEND_FROM_EMAIL ?? '<EMAIL>';

    if (!apiKey) {
      throw new Error('RESEND_API_KEY não configurada');
    }

    this.resend = new Resend(apiKey);

    // Inicializar serviços SMS e Push se as credenciais estiverem disponíveis
    try {
      this.smsService = new SMSService();
    } catch (error) {
      console.warn('⚠️ SMS Service não inicializado:', error);
    }

    try {
      this.pushService = new PushService();
    } catch (error) {
      console.warn('⚠️ Push Service não inicializado:', error);
    }
  }

  /**
   * Processa uma notificação completa (cria registro + envia)
   */
  async processarNotificacao(data: ProcessarNotificacaoData): Promise<ResultadoNotificacao> {
    try {
      console.log(`📧 Processando notificação: ${data.tipo} para usuário ${data.destinatario_id} via ${data.canal ?? 'email'}`);

      // 1. Obter dados do destinatário e suas preferências
      const { data: { user }, error: userError } = await this.supabase.auth.admin.getUserById(data.destinatario_id);

      if (userError || !user) {
        throw new Error(`Usuário não encontrado: ${data.destinatario_id}`);
      }

      // 2. Obter preferências de notificação do usuário
      const preferencias = await this.obterPreferenciasUsuario(data.destinatario_id);

      // 3. Determinar canais a serem usados
      const canais = this.determinarCanais(data.canal, preferencias, data.tipo);

      if (canais.length === 0) {
        console.log(`⚠️ Nenhum canal habilitado para usuário ${data.destinatario_id}`);
        return { success: true }; // Não é erro, usuário optou por não receber
      }

      // 4. Processar cada canal
      const resultados: ResultadoNotificacao[] = [];

      for (const canal of canais) {
        const resultado = await this.processarCanalNotificacao(
          canal,
          data,
          user,
          preferencias
        );
        resultados.push(resultado);
      }

      // 5. Consolidar resultados
      const sucessos = resultados.filter(r => r.success).length;
      const falhas = resultados.filter(r => !r.success);

      return {
        success: sucessos > 0,
        notificacao_id: resultados.find(r => r.notificacao_id)?.notificacao_id,
        error: falhas.length > 0 ? falhas.map(f => f.error).join('; ') : undefined
      };

    } catch (error) {
      console.error('❌ Erro ao processar notificação:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Obter preferências de notificação do usuário
   */
  private async obterPreferenciasUsuario(userId: string) {
    const { data } = await this.supabase
      .from('preferencias_notificacao')
      .select('*')
      .eq('user_id', userId)
      .single();

    // Retorna preferências padrão se não encontrar
    return data ?? {
      email_enabled: true,
      sms_enabled: true,
      push_enabled: true,
      tipos_habilitados: ['novo_agendamento', 'agendamento_confirmado', 'agendamento_recusado', 'agendamento_cancelado', 'lembrete_confirmacao', 'lembrete_agendamento', 'pagamento_confirmado']
    };
  }

  /**
   * Determinar quais canais usar baseado nas preferências
   */
  private determinarCanais(canalSolicitado?: CanalNotificacao, preferencias?: any, tipo?: TipoNotificacao): CanalNotificacao[] {
    const canais: CanalNotificacao[] = [];

    // Se canal específico foi solicitado, usar apenas ele (se habilitado)
    if (canalSolicitado) {
      if (this.canalHabilitado(canalSolicitado, preferencias, tipo)) {
        canais.push(canalSolicitado);
      }
      return canais;
    }

    // Caso contrário, usar todos os canais habilitados
    if (this.canalHabilitado('email', preferencias, tipo)) canais.push('email');
    if (this.canalHabilitado('sms', preferencias, tipo)) canais.push('sms');
    if (this.canalHabilitado('push', preferencias, tipo)) canais.push('push');

    return canais;
  }

  /**
   * Verificar se um canal está habilitado para o usuário
   */
  private canalHabilitado(canal: CanalNotificacao, preferencias?: any, tipo?: TipoNotificacao): boolean {
    if (!preferencias) return canal === 'email'; // Default apenas email

    // Verificar se o tipo de notificação está habilitado
    if (tipo && preferencias.tipos_habilitados && !preferencias.tipos_habilitados.includes(tipo)) {
      return false;
    }

    // Verificar se o canal está habilitado
    switch (canal) {
      case 'email':
        return preferencias.email_enabled !== false;
      case 'sms':
        return preferencias.sms_enabled === true && !!this.smsService;
      case 'push':
        return preferencias.push_enabled === true && !!this.pushService;
      default:
        return false;
    }
  }

  /**
   * Processar notificação para um canal específico
   */
  private async processarCanalNotificacao(
    canal: CanalNotificacao,
    data: ProcessarNotificacaoData,
    user: any,
    preferencias: any
  ): Promise<ResultadoNotificacao> {
    try {
      // Criar registro de notificação
      const notificacao = await this.criarRegistroNotificacao(data, canal);

      let resultado: { success: boolean; messageId?: string; error?: string };

      // Enviar via canal específico
      switch (canal) {
        case 'email':
          resultado = await this.enviarViaEmail(data, user);
          break;
        case 'sms':
          resultado = await this.enviarViaSMS(data, user);
          break;
        case 'push':
          resultado = await this.enviarViaPush(data, user);
          break;
        default:
          throw new Error(`Canal não suportado: ${canal}`);
      }

      // Atualizar status da notificação
      await this.atualizarStatusNotificacao(
        notificacao.notificacao_id,
        resultado.success,
        resultado.error
      );

      return {
        success: resultado.success,
        notificacao_id: notificacao.notificacao_id,
        error: resultado.error
      };

    } catch (error) {
      console.error(`❌ Erro ao processar canal ${canal}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Envia email usando Resend
   */
  private async enviarEmail(data: EnviarEmailData): Promise<EmailResponse> {
    try {
      const result = await this.resend.emails.send({
        from: data.from ?? this.fromEmail,
        to: data.to,
        subject: data.subject,
        html: data.html,
        text: data.text
      });

      console.log(`✅ Email enviado com sucesso: ${result.data?.id}`);

      return {
        success: true,
        messageId: result.data?.id
      };

    } catch (error) {
      console.error('❌ Erro ao enviar email:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro ao enviar email'
      };
    }
  }

  /**
   * Criar registro de notificação no banco
   */
  private async criarRegistroNotificacao(data: ProcessarNotificacaoData, canal: CanalNotificacao) {
    const template = await this.gerarTemplate(data.tipo, data.contexto);

    const notificacaoData = {
      user_id: data.destinatario_id,
      tipo_notificacao: data.tipo,
      titulo: template.subject,
      mensagem: template.text ?? 'Notificação',
      dados_contexto: data.contexto,
      canal: canal,
      agendamento_id: data.agendamento_id,
      empresa_id: data.empresa_id
    };

    const { data: notificacao, error } = await this.supabase
      .from('notificacoes')
      .insert([notificacaoData])
      .select()
      .single();

    if (error) {
      throw new Error(`Erro ao criar notificação: ${error.message}`);
    }

    return notificacao;
  }

  /**
   * Enviar via email
   */
  private async enviarViaEmail(data: ProcessarNotificacaoData, user: any) {
    if (!user.email) {
      throw new Error('Usuário não possui email');
    }

    const template = await this.gerarTemplate(data.tipo, data.contexto);

    return this.enviarEmail({
      to: user.email,
      subject: template.subject,
      html: template.html,
      text: template.text
    });
  }

  /**
   * Enviar via SMS
   */
  private async enviarViaSMS(data: ProcessarNotificacaoData, user: any) {
    if (!this.smsService) {
      throw new Error('Serviço de SMS não disponível');
    }

    // Obter telefone do usuário (pode estar em user_metadata ou perfil)
    const telefone = user.user_metadata?.telefone ?? user.phone;

    if (!telefone) {
      throw new Error('Usuário não possui telefone');
    }

    if (!this.smsService.validarNumero(telefone)) {
      throw new Error('Número de telefone inválido');
    }

    const mensagem = this.smsService.gerarTemplateSMS(data.tipo, data.contexto);

    return this.smsService.enviarSMS({
      to: telefone,
      message: mensagem
    });
  }

  /**
   * Enviar via Push
   */
  private async enviarViaPush(data: ProcessarNotificacaoData, user: any) {
    if (!this.pushService) {
      throw new Error('Serviço de Push não disponível');
    }

    // Obter tokens de dispositivo do usuário
    const { data: tokens } = await this.supabase
      .from('device_tokens')
      .select('token')
      .eq('user_id', user.id)
      .eq('active', true);

    if (!tokens || tokens.length === 0) {
      throw new Error('Usuário não possui tokens de dispositivo ativos');
    }

    const dadosPush = this.pushService.gerarDadosPush(data.tipo, data.contexto);

    // Enviar para todos os tokens do usuário
    const resultados = await Promise.all(
      tokens.map(({ token }) =>
        this.pushService!.enviarPush({ ...dadosPush, token })
      )
    );

    // Considerar sucesso se pelo menos um envio foi bem-sucedido
    const sucessos = resultados.filter(r => r.success);
    const falhas = resultados.filter(r => !r.success);

    return {
      success: sucessos.length > 0,
      messageId: sucessos[0]?.messageId,
      error: falhas.length > 0 ? falhas.map(f => f.error).join('; ') : undefined
    };
  }

  /**
   * Gera template de email baseado no tipo de notificação
   */
  private async gerarTemplate(tipo: TipoNotificacao, contexto: any) {
    const templates = new EmailTemplates();

    switch (tipo) {
      case 'novo_agendamento':
        return templates.novoAgendamentoCliente(contexto as ContextoAgendamento);

      case 'agendamento_confirmado':
        return templates.agendamentoConfirmado(contexto as ContextoAgendamento);

      case 'agendamento_recusado':
        return templates.agendamentoRecusado(contexto as ContextoAgendamento);

      case 'agendamento_cancelado':
        return templates.agendamentoCancelado(contexto as ContextoAgendamento);

      case 'lembrete_confirmacao':
        return templates.lembreteConfirmacao(contexto as ContextoProprietario);

      case 'lembrete_agendamento':
        return templates.lembreteAgendamento(contexto as ContextoAgendamento);

      case 'pagamento_confirmado':
        return templates.pagamentoConfirmado(contexto as ContextoAgendamento);

      default:
        throw new Error(`Tipo de notificação não suportado: ${tipo}`);
    }
  }

  /**
   * Atualiza status da notificação após tentativa de envio
   */
  private async atualizarStatusNotificacao(
    notificacaoId: number, 
    sucesso: boolean, 
    erro?: string
  ) {
    try {
      const updateData: any = {
        enviada: sucesso,
        data_envio: new Date().toISOString(),
        tentativas_envio: 1
      };

      if (!sucesso && erro) {
        updateData.erro_envio = erro;
      }

      await this.supabase
        .from('notificacoes')
        .update(updateData)
        .eq('notificacao_id', notificacaoId);

    } catch (error) {
      console.error('❌ Erro ao atualizar status da notificação:', error);
    }
  }

  /**
   * Reenviar notificação que falhou
   */
  async reenviarNotificacao(notificacaoId: number): Promise<ResultadoNotificacao> {
    try {
      // Buscar notificação
      const { data: notificacao, error } = await this.supabase
        .from('notificacoes')
        .select('*')
        .eq('notificacao_id', notificacaoId)
        .single();

      if (error || !notificacao) {
        throw new Error('Notificação não encontrada');
      }

      // Obter dados do usuário
      const { data: { user }, error: userError } = await this.supabase.auth.admin.getUserById(notificacao.user_id);
      
      if (userError || !user?.email) {
        throw new Error('Usuário não encontrado ou sem email');
      }

      // Gerar template novamente
      const template = await this.gerarTemplate(
        notificacao.tipo_notificacao as TipoNotificacao, 
        notificacao.dados_contexto
      );

      // Enviar email
      const emailResult = await this.enviarEmail({
        to: user.email,
        subject: template.subject,
        html: template.html,
        text: template.text
      });

      // Atualizar tentativas
      await this.supabase
        .from('notificacoes')
        .update({
          enviada: emailResult.success,
          data_envio: new Date().toISOString(),
          tentativas_envio: notificacao.tentativas_envio + 1,
          erro_envio: emailResult.success ? null : emailResult.error
        })
        .eq('notificacao_id', notificacaoId);

      return {
        success: emailResult.success,
        notificacao_id: notificacaoId,
        error: emailResult.error,
        tentativas: notificacao.tentativas_envio + 1
      };

    } catch (error) {
      console.error('❌ Erro ao reenviar notificação:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Buscar notificações pendentes para reenvio
   */
  async buscarNotificacoesPendentes(limite: number = 10) {
    try {
      const { data, error } = await this.supabase
        .from('notificacoes')
        .select('*')
        .eq('enviada', false)
        .lt('tentativas_envio', 3)
        .order('created_at', { ascending: true })
        .limit(limite);

      if (error) {
        throw new Error(`Erro ao buscar notificações pendentes: ${error.message}`);
      }

      return data || [];

    } catch (error) {
      console.error('❌ Erro ao buscar notificações pendentes:', error);
      return [];
    }
  }
}
