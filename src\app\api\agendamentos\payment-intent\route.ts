import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import Stripe from 'stripe';
import { CriarPaymentIntentAgendamentoData } from '@/types/agendamentos';


// Inicializar o cliente Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: '2025-05-28.basil',
});

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    const body: CriarPaymentIntentAgendamentoData = await request.json();
    const { agendamento_id, valor, descricao } = body;

    // Validar dados obrigatórios
    if (!agendamento_id || !valor || valor <= 0) {
      return NextResponse.json(
        { success: false, error: 'Dados inválidos para criar pagamento' },
        { status: 400 }
      );
    }

    // Buscar agendamento e verificar se pertence ao usuário
    const { data: agendamento, error: agendamentoError } = await supabase
      .from('agendamentos')
      .select(`
        agendamento_id,
        cliente_user_id,
        empresa_id,
        servico_id,
        valor_total,
        status_agendamento,
        status_pagamento,
        forma_pagamento,
        stripe_payment_intent_id,
        servicos!inner (
          nome_servico,
          preco
        ),
        empresas!inner (
          nome_empresa,
          email_contato,
          stripe_account_id,
          stripe_charges_enabled,
          pagamentos_online_habilitados,
          percentual_comissao_plataforma
        )
      `)
      .eq('agendamento_id', agendamento_id)
      .eq('cliente_user_id', user.id)
      .single();

    if (agendamentoError || !agendamento) {
      return NextResponse.json(
        { success: false, error: 'Agendamento não encontrado ou não autorizado' },
        { status: 404 }
      );
    }

    // Verificar se o agendamento pode ser pago
    if (agendamento.forma_pagamento !== 'Online') {
      return NextResponse.json(
        { success: false, error: 'Este agendamento não é para pagamento online' },
        { status: 400 }
      );
    }

    if (agendamento.status_pagamento === 'Pago') {
      return NextResponse.json(
        { success: false, error: 'Este agendamento já foi pago' },
        { status: 400 }
      );
    }

    if (agendamento.status_agendamento === 'Cancelado' || agendamento.status_agendamento === 'Recusado') {
      return NextResponse.json(
        { success: false, error: 'Não é possível pagar um agendamento cancelado ou recusado' },
        { status: 400 }
      );
    }

    // Verificar se a empresa tem pagamentos online habilitados
    const empresa = Array.isArray(agendamento.empresas) ? agendamento.empresas[0] : agendamento.empresas;
    if (!empresa?.pagamentos_online_habilitados) {
      return NextResponse.json(
        { success: false, error: 'Esta empresa não aceita pagamentos online no momento' },
        { status: 400 }
      );
    }

    // Verificar se tem conta Stripe conectada e ativa (OBRIGATÓRIO)
    const temContaConectada = empresa.stripe_account_id &&
                              empresa.stripe_charges_enabled;

    if (!temContaConectada) {
      return NextResponse.json(
        {
          success: false,
          error: 'Esta empresa ainda não configurou sua conta Stripe para receber pagamentos online. Entre em contato para mais informações.'
        },
        { status: 400 }
      );
    }

    const percentualComissao = empresa.percentual_comissao_plataforma || 5.0;

    // Se já existe um Payment Intent, retornar o existente (se ainda válido)
    if (agendamento.stripe_payment_intent_id) {
      try {
        const existingPaymentIntent = await stripe.paymentIntents.retrieve(
          agendamento.stripe_payment_intent_id
        );

        // Se o Payment Intent ainda está válido (não foi cancelado ou bem-sucedido)
        if (existingPaymentIntent.status === 'requires_payment_method' || 
            existingPaymentIntent.status === 'requires_confirmation' ||
            existingPaymentIntent.status === 'requires_action') {
          return NextResponse.json({
            success: true,
            data: {
              client_secret: existingPaymentIntent.client_secret,
              payment_intent_id: existingPaymentIntent.id
            }
          });
        }
      } catch (error) {
        console.log('Payment Intent existente não encontrado ou inválido, criando novo...');
      }
    }

    // Buscar dados do usuário para o cliente Stripe
    const { data: userData, error: userError } = await supabase.auth.admin.getUserById(user.id);
    if (userError) {
      console.error('Erro ao buscar dados do usuário:', userError);
    }

    // Criar ou buscar cliente no Stripe
    let stripeCustomer;
    try {
      // Tentar buscar cliente existente pelo email
      const customers = await stripe.customers.list({
        email: userData?.user?.email || user.email,
        limit: 1
      });

      if (customers.data.length > 0) {
        stripeCustomer = customers.data[0];
      } else {
        // Criar novo cliente
        stripeCustomer = await stripe.customers.create({
          email: userData?.user?.email || user.email,
          name: userData?.user?.user_metadata?.name || 'Cliente',
          metadata: {
            user_id: user.id,
            agendamento_id: agendamento_id.toString()
          }
        });
      }
    } catch (error) {
      console.error('Erro ao criar/buscar cliente Stripe:', error);
      // Continuar sem cliente se houver erro
    }

    // Criar Payment Intent APENAS com conta conectada
    const valorCentavos = Math.round(valor * 100);
    const comissaoCentavos = Math.round((valor * percentualComissao / 100) * 100);

    const paymentIntent = await stripe.paymentIntents.create({
      amount: valorCentavos,
      currency: 'brl',
      customer: stripeCustomer?.id,
      automatic_payment_methods: {
        enabled: true,
      },
      application_fee_amount: comissaoCentavos,
      transfer_data: {
        destination: empresa.stripe_account_id,
      },
      metadata: {
        agendamento_id: agendamento_id.toString(),
        cliente_user_id: user.id,
        empresa_id: agendamento.empresa_id.toString(),
        servico_id: agendamento.servico_id.toString(),
        tipo_pagamento: 'agendamento_conectado',
        conta_conectada_id: empresa.stripe_account_id,
        percentual_comissao: percentualComissao.toString(),
        nome_servico: (Array.isArray(agendamento.servicos) ? agendamento.servicos[0] : agendamento.servicos)?.nome_servico,
        nome_empresa: empresa.nome_empresa
      },
      description: descricao || `Pagamento do agendamento - ${(Array.isArray(agendamento.servicos) ? agendamento.servicos[0] : agendamento.servicos)?.nome_servico} na ${empresa.nome_empresa}`,
      receipt_email: userData?.user?.email || user.email,
    });

    // Atualizar agendamento com o Payment Intent ID
    const { error: updateError } = await supabase
      .from('agendamentos')
      .update({
        stripe_payment_intent_id: paymentIntent.id,
        updated_at: new Date().toISOString()
      })
      .eq('agendamento_id', agendamento_id);

    if (updateError) {
      console.error('Erro ao atualizar agendamento com Payment Intent:', updateError);
      // Não falhar a operação por causa disso
    }

    console.log('✅ Payment Intent criado para agendamento:', {
      agendamento_id,
      payment_intent_id: paymentIntent.id,
      valor: valor,
      cliente: user.id
    });

    return NextResponse.json({
      success: true,
      data: {
        client_secret: paymentIntent.client_secret,
        payment_intent_id: paymentIntent.id
      }
    });

  } catch (error: any) {
    console.error('Erro ao criar Payment Intent para agendamento:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
