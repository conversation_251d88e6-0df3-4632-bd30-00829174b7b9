# 👨‍💻 Guia de Desenvolvimento - ServiceTech

Guia completo para desenvolvimento local e contribuição no projeto.

## ⚙️ Configuração do Ambiente

### Pré-requisitos
```bash
node --version  # 18+
npm --version
git --version
```

### Instalação
```bash
git clone https://github.com/renansbernardo/geremias.git
cd geremias/servicetech
npm install
cp .env.example .env.local
```

### Variáveis de Ambiente
```bash
# Supabase
NEXT_PUBLIC_SUPABASE_URL=sua_url_supabase
NEXT_PUBLIC_SUPABASE_ANON_KEY=sua_chave_anon
SUPABASE_SERVICE_ROLE_KEY=sua_chave_service

# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_... # stripe listen --forward-to localhost:3000/api/webhook/stripe
STRIPE_CONNECT_CLIENT_ID=ca_...

# Notificações
RESEND_API_KEY=re_...
TWILIO_ACCOUNT_SID=AC... # opcional
TWILIO_AUTH_TOKEN=... # opcional

# Firebase (Push) - opcional
FIREBASE_ADMIN_PRIVATE_KEY="..."
FIREBASE_ADMIN_CLIENT_EMAIL=...
FIREBASE_ADMIN_PROJECT_ID=...
```

### Scripts Disponíveis
```bash
npm run dev          # Servidor de desenvolvimento
npm run build        # Build de produção
npm run start        # Servidor de produção
npm run lint         # ESLint
npm run type-check   # TypeScript check
npm run test         # Jest
npm run test:watch   # Jest watch mode
```

## 🏗️ Estrutura do Projeto

```
servicetech/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API Routes
│   │   ├── (auth)/            # Grupo de autenticação
│   │   ├── admin/             # Dashboard admin
│   │   ├── proprietario/      # Dashboard proprietário
│   │   ├── colaborador/       # Dashboard colaborador
│   │   └── cliente/           # Dashboard cliente
│   │
│   ├── components/            # Componentes React
│   │   ├── ui/               # Componentes base
│   │   ├── layout/           # Layout components
│   │   ├── auth/             # Autenticação
│   │   └── [feature]/        # Por funcionalidade
│   │
│   ├── hooks/                # Custom hooks
│   ├── types/                # Definições TypeScript
│   ├── utils/                # Utilitários
│   ├── contexts/             # React contexts
│   └── services/             # Serviços externos
│
├── public/                   # Assets estáticos
└── tests/                    # Testes
```

### Convenções de Nomenclatura
```typescript
// Arquivos
PascalCase.tsx        // Componentes React
camelCase.ts          // Utilitários e hooks
kebab-case.md         // Documentação

// Componentes
export function ComponentName() {}

// Hooks
export function useCustomHook() {}

// Tipos
export interface UserData {}
export type StatusType = 'active' | 'inactive';
```

## 📝 Padrões de Código

### TypeScript
```typescript
// Props de componentes
interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary';
  onClick?: () => void;
  disabled?: boolean;
}

export function Button({ children, variant = 'primary', ...props }: ButtonProps) {
  return (
    <button className={`btn btn-${variant}`} {...props}>
      {children}
    </button>
  );
}

// Retornos de hooks
interface UseAgendamentosReturn {
  agendamentos: Agendamento[];
  loading: boolean;
  error: string | null;
  criarAgendamento: (data: CriarAgendamentoData) => Promise<void>;
}
```

### React Patterns
```typescript
// Componentes funcionais com hooks
export function AgendamentoCard({ agendamento }: { agendamento: Agendamento }) {
  const [loading, setLoading] = useState(false);
  
  const handleConfirmar = useCallback(async () => {
    setLoading(true);
    try {
      await confirmarAgendamento(agendamento.agendamento_id);
    } catch (error) {
      console.error('Erro ao confirmar:', error);
    } finally {
      setLoading(false);
    }
  }, [agendamento.agendamento_id]);

  return <div className="card">{/* JSX */}</div>;
}

// Custom hooks para lógica reutilizável
export function useAgendamentos(empresaId?: number) {
  const [state, setState] = useState<AgendamentosState>({
    agendamentos: [],
    loading: false,
    error: null
  });

  const buscarAgendamentos = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true }));
    try {
      const data = await api.get('/agendamentos', { params: { empresaId } });
      setState(prev => ({ ...prev, agendamentos: data, loading: false }));
    } catch (error) {
      setState(prev => ({ ...prev, error: error.message, loading: false }));
    }
  }, [empresaId]);

  useEffect(() => {
    buscarAgendamentos();
  }, [buscarAgendamentos]);

  return { ...state, buscarAgendamentos };
}
```

### Error Handling
```typescript
// API Routes
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (!body.empresa_id) {
      return NextResponse.json(
        { success: false, error: 'empresa_id é obrigatório' },
        { status: 400 }
      );
    }

    const result = await criarAgendamento(body);
    
    return NextResponse.json({
      success: true,
      data: result
    });
    
  } catch (error) {
    console.error('Erro na API:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
```

## 🔌 APIs e Backend

### API Routes Pattern
```typescript
// app/api/agendamentos/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { withSecurity } from '@/utils/security/apiMiddleware';

export async function GET(request: NextRequest) {
  return withSecurity(request, async () => {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    
    const empresaId = searchParams.get('empresa_id');
    
    let query = supabase
      .from('agendamentos')
      .select(`
        *,
        servicos(nome_servico, preco),
        empresas(nome_empresa)
      `);
    
    if (empresaId) {
      query = query.eq('empresa_id', empresaId);
    }
    
    const { data, error } = await query;
    
    if (error) throw new Error(error.message);
    
    return NextResponse.json({
      success: true,
      data: data || []
    });
  });
}
```

### Middleware de Segurança
```typescript
// utils/security/apiMiddleware.ts
export async function withSecurity(
  request: NextRequest,
  options: SecurityOptions = {},
  handler: () => Promise<NextResponse>
) {
  try {
    // Rate limiting
    await checkRateLimit(request);
    
    // Autenticação
    if (options.requireAuth !== false) {
      const user = await authenticateRequest(request);
      if (!user) {
        return NextResponse.json(
          { success: false, error: 'Não autorizado' },
          { status: 401 }
        );
      }
    }
    
    return await handler();
    
  } catch (error) {
    console.error('Erro na API:', error);
    await logSecurityEvent(request, error);
    
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
```

## 🗄️ Banco de Dados

### Supabase Client
```typescript
// utils/supabase/client.ts
import { createBrowserClient } from '@supabase/ssr';

export function createClient() {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
}

// utils/supabase/server.ts
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

export async function createClient() {
  const cookieStore = await cookies();
  
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            cookieStore.set(name, value, options);
          });
        },
      },
    }
  );
}
```

### Queries Tipadas
```typescript
// types/database.ts
export interface Database {
  public: {
    Tables: {
      agendamentos: {
        Row: {
          agendamento_id: number;
          cliente_user_id: string;
          empresa_id: number;
        };
        Insert: {
          cliente_user_id: string;
          empresa_id: number;
        };
        Update: {
          status_agendamento?: string;
        };
      };
    };
  };
}

// Uso tipado
const supabase = createClient<Database>();
const { data, error } = await supabase
  .from('agendamentos')
  .select('*')
  .eq('empresa_id', 1);
```

## 🧪 Testes

### Configuração Jest
```javascript
// jest.config.js
const nextJest = require('next/jest');

const createJestConfig = nextJest({
  dir: './',
});

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
};

module.exports = createJestConfig(customJestConfig);
```

### Testes de Componentes
```typescript
// __tests__/components/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '@/components/ui/Button';

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button')).toHaveTextContent('Click me');
  });

  it('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

## 🚀 Deploy e CI/CD

### GitHub Actions
```yaml
# .github/workflows/ci.yml
name: CI/CD

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: servicetech/package-lock.json
      
      - name: Install dependencies
        run: |
          cd servicetech
          npm ci
      
      - name: Run tests
        run: |
          cd servicetech
          npm run test
      
      - name: Build
        run: |
          cd servicetech
          npm run build
```

## 🤝 Contribuição

### Fluxo de Desenvolvimento
1. **Fork** o repositório
2. **Clone** seu fork
3. **Crie branch**: `git checkout -b feature/nova-funcionalidade`
4. **Desenvolva** seguindo os padrões
5. **Teste** localmente
6. **Commit** com mensagem clara
7. **Push** para seu fork
8. **Abra PR** para branch main

### Commit Messages
```bash
# Formato
type(scope): description

# Tipos
feat: nova funcionalidade
fix: correção de bug
docs: documentação
style: formatação
refactor: refatoração
test: testes

# Exemplos
feat(agendamento): adicionar verificação de disponibilidade
fix(auth): corrigir redirecionamento após login
docs(api): atualizar documentação de endpoints
```

### Code Review Checklist
- [ ] **Funcionalidade**: Código funciona conforme esperado
- [ ] **Testes**: Cobertura adequada de testes
- [ ] **Performance**: Sem impactos negativos
- [ ] **Segurança**: Sem vulnerabilidades
- [ ] **Padrões**: Segue convenções do projeto
- [ ] **Documentação**: Atualizada quando necessário

---

**ServiceTech Development** - Código limpo e escalável 👨‍💻
