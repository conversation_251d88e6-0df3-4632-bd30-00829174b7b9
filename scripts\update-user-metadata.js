/**
 * Script para atualizar user_metadata dos usuários com empresa_id
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

const MARIA_SANTOS_ID = 'c7c4df3a-00fa-43b8-8d91-7980e9fe0345';
const PEDRO_OLIVEIRA_ID = '22df7916-1c36-4dda-8d0d-6de61a13ad27';
const EMPRESA_ID = 5;

async function updateUserMetadata() {
  try {
    console.log('👤 Atualizando user_metadata...');
    
    // Atualizar <PERSON> (Proprietária)
    const { error: errorMaria } = await supabase.auth.admin.updateUserById(MARIA_SANTOS_ID, {
      user_metadata: {
        name: '<PERSON> <PERSON>',
        full_name: 'Maria Santos',
        phone: '(11) 99999-2222',
        role: 'Proprietario',
        empresa_id: EMPRESA_ID,
        onboarding_concluido: true
      }
    });

    if (errorMaria) {
      console.error('❌ Erro ao atualizar Maria Santos:', errorMaria);
    } else {
      console.log('✅ Maria Santos atualizada com empresa_id:', EMPRESA_ID);
    }

    // Atualizar Pedro Oliveira (Colaborador)
    const { error: errorPedro } = await supabase.auth.admin.updateUserById(PEDRO_OLIVEIRA_ID, {
      user_metadata: {
        name: 'Pedro Oliveira',
        full_name: 'Pedro Oliveira',
        phone: '(11) 99999-3333',
        role: 'Colaborador',
        empresa_id: EMPRESA_ID
      }
    });

    if (errorPedro) {
      console.error('❌ Erro ao atualizar Pedro Oliveira:', errorPedro);
    } else {
      console.log('✅ Pedro Oliveira atualizado com empresa_id:', EMPRESA_ID);
    }

    console.log('\n✅ User metadata atualizado com sucesso!');
  } catch (error) {
    console.error('❌ Erro:', error);
  }
}

updateUserMetadata();
