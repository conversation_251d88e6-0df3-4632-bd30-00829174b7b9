/**
 * Sistema de Rate Limiting para APIs
 * Protege contra ataques de força bruta e spam
 */

interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
  blocked: boolean;
}

// Cache em memória para rate limiting (em produção, usar Redis)
const rateLimitCache = new Map<string, RateLimitEntry>();

// Configurações padrão por endpoint
const DEFAULT_CONFIGS: Record<string, RateLimitConfig> = {
  // Autenticação - mais restritivo
  '/api/auth/login': { maxRequests: 5, windowMs: 15 * 60 * 1000 }, // 5 tentativas por 15 min
  '/api/auth/signup': { maxRequests: 3, windowMs: 60 * 60 * 1000 }, // 3 tentativas por hora
  '/api/auth/reset-password': { maxRequests: 3, windowMs: 60 * 60 * 1000 },
  
  // APIs gerais - moderado
  '/api/agendamentos': { maxRequests: 100, windowMs: 60 * 60 * 1000 }, // 100 por hora
  '/api/servicos': { maxRequests: 200, windowMs: 60 * 60 * 1000 },
  '/api/colaboradores': { maxRequests: 50, windowMs: 60 * 60 * 1000 },
  
  // Webhooks - mais permissivo
  '/api/webhook': { maxRequests: 1000, windowMs: 60 * 60 * 1000 },
  
  // Padrão para outros endpoints
  'default': { maxRequests: 100, windowMs: 60 * 60 * 1000 }
};

/**
 * Gera chave única para rate limiting
 */
function generateKey(identifier: string, endpoint: string): string {
  return `rate_limit:${identifier}:${endpoint}`;
}

/**
 * Limpa entradas expiradas do cache
 */
function cleanupExpiredEntries(): void {
  const now = Date.now();
  for (const [key, entry] of rateLimitCache.entries()) {
    if (now > entry.resetTime) {
      rateLimitCache.delete(key);
    }
  }
}

/**
 * Verifica se uma requisição deve ser limitada
 */
export function checkRateLimit(
  identifier: string, // IP ou user ID
  endpoint: string,
  customConfig?: Partial<RateLimitConfig>
): {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
} {
  // Limpar entradas expiradas periodicamente
  if (Math.random() < 0.01) { // 1% de chance
    cleanupExpiredEntries();
  }

  // Normalizar endpoint para buscar configuração
  const normalizedEndpoint = Object.keys(DEFAULT_CONFIGS).find(pattern => 
    endpoint.startsWith(pattern)
  ) || 'default';

  const config = { ...DEFAULT_CONFIGS[normalizedEndpoint], ...customConfig };
  const key = generateKey(identifier, normalizedEndpoint);
  const now = Date.now();
  const resetTime = now + config.windowMs;

  let entry = rateLimitCache.get(key);

  // Se não existe entrada ou expirou, criar nova
  if (!entry || now > entry.resetTime) {
    entry = {
      count: 1,
      resetTime,
      blocked: false
    };
    rateLimitCache.set(key, entry);
    
    return {
      allowed: true,
      remaining: config.maxRequests - 1,
      resetTime
    };
  }

  // Incrementar contador
  entry.count++;

  // Verificar se excedeu o limite
  if (entry.count > config.maxRequests) {
    entry.blocked = true;
    
    return {
      allowed: false,
      remaining: 0,
      resetTime: entry.resetTime,
      retryAfter: Math.ceil((entry.resetTime - now) / 1000)
    };
  }

  return {
    allowed: true,
    remaining: config.maxRequests - entry.count,
    resetTime: entry.resetTime
  };
}

/**
 * Middleware para aplicar rate limiting em APIs
 */
export function withRateLimit(
  identifier: string,
  endpoint: string,
  customConfig?: Partial<RateLimitConfig>
) {
  const result = checkRateLimit(identifier, endpoint, customConfig);
  
  if (!result.allowed) {
    const error = new Error('Rate limit exceeded');
    (error as any).status = 429;
    (error as any).retryAfter = result.retryAfter;
    (error as any).resetTime = result.resetTime;
    throw error;
  }
  
  return {
    remaining: result.remaining,
    resetTime: result.resetTime
  };
}

/**
 * Extrai identificador da requisição (IP ou user ID)
 */
export function getRequestIdentifier(request: Request, userId?: string): string {
  if (userId) {
    return `user:${userId}`;
  }
  
  // Tentar extrair IP real considerando proxies
  const forwarded = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const cfConnectingIp = request.headers.get('cf-connecting-ip');
  
  const ip = cfConnectingIp || realIp || forwarded?.split(',')[0] || 'unknown';
  return `ip:${ip}`;
}

/**
 * Bloqueia temporariamente um identificador
 */
export function blockIdentifier(
  identifier: string,
  endpoint: string,
  durationMs: number = 60 * 60 * 1000 // 1 hora padrão
): void {
  const key = generateKey(identifier, endpoint);
  const resetTime = Date.now() + durationMs;
  
  rateLimitCache.set(key, {
    count: 999999, // Valor alto para garantir bloqueio
    resetTime,
    blocked: true
  });
}

/**
 * Remove bloqueio de um identificador
 */
export function unblockIdentifier(identifier: string, endpoint: string): void {
  const key = generateKey(identifier, endpoint);
  rateLimitCache.delete(key);
}

/**
 * Obtém estatísticas do rate limiting
 */
export function getRateLimitStats(): {
  totalEntries: number;
  blockedEntries: number;
  cacheSize: number;
} {
  const totalEntries = rateLimitCache.size;
  let blockedEntries = 0;
  
  for (const entry of rateLimitCache.values()) {
    if (entry.blocked) {
      blockedEntries++;
    }
  }
  
  return {
    totalEntries,
    blockedEntries,
    cacheSize: totalEntries
  };
}

/**
 * Configurações específicas para diferentes tipos de usuário
 */
export const USER_RATE_LIMITS = {
  // Usuários não autenticados - mais restritivo
  anonymous: {
    '/api/empresas': { maxRequests: 50, windowMs: 60 * 60 * 1000 },
    '/api/servicos': { maxRequests: 30, windowMs: 60 * 60 * 1000 },
    'default': { maxRequests: 20, windowMs: 60 * 60 * 1000 }
  },
  
  // Usuários autenticados - mais permissivo
  authenticated: {
    '/api/agendamentos': { maxRequests: 200, windowMs: 60 * 60 * 1000 },
    '/api/servicos': { maxRequests: 300, windowMs: 60 * 60 * 1000 },
    'default': { maxRequests: 150, windowMs: 60 * 60 * 1000 }
  },
  
  // Administradores - muito permissivo
  admin: {
    'default': { maxRequests: 1000, windowMs: 60 * 60 * 1000 }
  }
};
