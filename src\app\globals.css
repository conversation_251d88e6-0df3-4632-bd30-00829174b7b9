@import "tailwindcss";

/* Importar estilos de acessibilidade */
@import '../styles/accessibility.css';

/* <PERSON><PERSON> (Padrão) */
:root {
  --primary: #3B82F6; /* Um azul mais vibrante */
  --primary-hover: #2563EB; /* Escurece no hover */
  --secondary: #6B7280; /* Cinza médio */
  --accent: #F59E0B; /* Laranja mais suave */
  --accent-hover: #D97706;
  --background: #F3F4F6; /* Cinza claro para o fundo geral */
  --surface: #FFFFFF; /* Branco puro para cards */
  --text-primary: #1F2937; /* Cinza escuro forte */
  --text-secondary: #6B7280; /* Cinza médio para texto secundário */
  --text-on-primary: #ffffff;
  --text-on-accent: #ffffff;
  --border-color: #D1D5DB;
  --success: #10B981;
  --error: #EF4444;
  --warning: #FBBF24;

  /* Transitions */
  --transition-default: all 0.3s ease;

  /* Cores específicas para tema escuro */
  --surface-hover: #F9FAFB;
  --surface-active: #F3F4F6;
  --input-background: #FFFFFF;
  --input-border: #D1D5DB;
  --shadow-color: rgba(0, 0, 0, 0.1);
}



/* Tema Escuro - Aplicado via classe .dark */
.dark {
  --primary: #60A5FA; /* Azul mais claro para melhor contraste */
  --primary-hover: #3B82F6; /* Azul padrão no hover */
  --secondary: #9CA3AF; /* Cinza mais claro para secundário */
  --accent: #FBBF24; /* Amarelo mais claro para melhor contraste */
  --accent-hover: #F59E0B;
  --background: #0F172A; /* Azul escuro profundo */
  --surface: #1E293B; /* Azul escuro médio para cards */
  --text-primary: #F1F5F9; /* Branco quase puro para texto principal */
  --text-secondary: #94A3B8; /* Cinza azulado para texto secundário */
  --text-on-primary: #0F172A;
  --text-on-accent: #0F172A;
  --border-color: #334155; /* Cinza azulado para bordas */
  --success: #22C55E; /* Verde mais claro */
  --error: #F87171; /* Vermelho mais claro */
  --warning: #FBBF24; /* Amarelo mais claro */

  /* Cores específicas para tema escuro */
  --surface-hover: #334155;
  --surface-active: #475569;
  --input-background: #1E293B;
  --input-border: #334155;
  --shadow-color: rgba(0, 0, 0, 0.3);

  /* Color scheme */
  color-scheme: dark;
}

/* Fallback para preferência do sistema */
@media (prefers-color-scheme: dark) {
  :root:not(.light) {
    --primary: #60A5FA;
    --primary-hover: #3B82F6;
    --secondary: #9CA3AF;
    --accent: #FBBF24;
    --accent-hover: #F59E0B;
    --background: #0F172A;
    --surface: #1E293B;
    --text-primary: #F1F5F9;
    --text-secondary: #94A3B8;
    --text-on-primary: #0F172A;
    --text-on-accent: #0F172A;
    --border-color: #334155;
    --success: #22C55E;
    --error: #F87171;
    --warning: #FBBF24;
    --surface-hover: #334155;
    --surface-active: #475569;
    --input-background: #1E293B;
    --input-border: #334155;
    --shadow-color: rgba(0, 0, 0, 0.3);
  }
}

body {
  background-color: var(--background);
  color: var(--text-primary);
  font-family: var(--font-geist-sans), system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
  /* antialiased is handled in layout.tsx, CSS should support it */
}

h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
  font-weight: 600; /* Semi-bold by default for Geist */
  margin-bottom: 0.5em;
  line-height: 1.3;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
/* h4, h5, h6 will inherit remaining styles and can be customized further with Tailwind */

/* Utility classes */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Estilos específicos para tema claro */
.light {
  color-scheme: light;
}

/* Melhorias para inputs no tema escuro */
.dark input,
.dark textarea,
.dark select {
  background-color: var(--input-background);
  border-color: var(--input-border);
  color: var(--text-primary);
}

.dark input:focus,
.dark textarea:focus,
.dark select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 1px var(--primary);
}

/* Melhorias para cards e superfícies */
.dark .card,
.dark [class*="bg-white"],
.dark [class*="bg-gray-50"] {
  background-color: var(--surface) !important;
  border-color: var(--border-color) !important;
}

/* Melhorias para texto */
.dark .text-gray-600 {
  color: var(--text-secondary) !important;
}

.dark .text-gray-900 {
  color: var(--text-primary) !important;
}

/* Melhorias para bordas */
.dark .border-gray-200,
.dark .border-gray-300 {
  border-color: var(--border-color) !important;
}

/* Melhorias para hover states */
.dark .hover\:bg-gray-50:hover {
  background-color: var(--surface-hover) !important;
}

.dark .hover\:bg-gray-100:hover {
  background-color: var(--surface-active) !important;
}

/* Scrollbar styling para tema escuro */
.dark ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.dark ::-webkit-scrollbar-track {
  background: var(--surface);
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--secondary);
}
