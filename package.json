{"name": "servicetech", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit", "analyze:performance": "node scripts/analyze-performance.js", "analyze:bundle": "ANALYZE=true npm run build", "clean": "rm -rf .next out dist", "prepare": "husky install || true"}, "dependencies": {"@radix-ui/react-icons": "^1.3.2", "@sentry/nextjs": "^9.27.0", "@sentry/node": "^9.27.0", "@sentry/react": "^9.27.0", "@stripe/react-stripe-js": "^3.7.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@types/jsonwebtoken": "^9.0.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "firebase-admin": "^12.7.0", "framer-motion": "^12.18.1", "isomorphic-dompurify": "^2.25.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.513.0", "next": "15.3.3", "node-fetch": "^3.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "resend": "^4.0.1", "stripe": "^18.2.0", "tailwind-merge": "^3.3.0", "twilio": "^5.4.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0-beta.3", "tailwindcss": "^4", "typescript": "^5"}}