'use client';

import { useState, useEffect } from 'react';
import { useTestesUsuario } from '@/hooks/useTestesUsuario';
import { CenarioTeste, PassoTeste, MetricasAlvo } from '@/types/testesUsuario';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';

interface FormularioCenarioProps {
  cenario?: CenarioTeste | null;
  onClose: () => void;
  onSuccess: () => void;
}

export function FormularioCenario({ cenario, onClose, onSuccess }: FormularioCenarioProps) {
  const { criarCenario, atualizarCenario, loading, error } = useTestesUsuario();

  const [formData, setFormData] = useState({
    nome_cenario: '',
    descricao: '',
    papel_usuario: 'Cliente' as 'Administrador' | 'Proprietario' | 'Colaborador' | 'Cliente',
    categoria: 'Agendamento' as 'Onboarding' | 'Agendamento' | 'Gestao' | 'Pagamento' | 'Navegacao',
    dificuldade: 'Medio' as 'Facil' | 'Medio' | 'Dificil',
    tempo_estimado_minutos: 30,
    ativo: true
  });

  const [passos, setPassos] = useState<Omit<PassoTeste, 'passo_numero'>[]>([
    {
      descricao: '',
      acao_esperada: '',
      resultado_esperado: '',
      url_inicial: '',
      elementos_interacao: [],
      observacoes: ''
    }
  ]);

  const [criteriosSucesso, setCriteriosSucesso] = useState<string[]>(['']);
  const [metricasAlvo, setMetricasAlvo] = useState<MetricasAlvo>({
    tempo_maximo_minutos: 45,
    taxa_sucesso_minima: 80,
    pontuacao_satisfacao_minima: 7,
    taxa_erro_maxima: 20
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (cenario) {
      setFormData({
        nome_cenario: cenario.nome_cenario,
        descricao: cenario.descricao,
        papel_usuario: cenario.papel_usuario,
        categoria: cenario.categoria,
        dificuldade: cenario.dificuldade,
        tempo_estimado_minutos: cenario.tempo_estimado_minutos,
        ativo: cenario.ativo
      });
      setPassos(cenario.passos.map(({ passo_numero, ...resto }) => resto));
      setCriteriosSucesso(cenario.criterios_sucesso);
      setMetricasAlvo(cenario.metricas_alvo);
    }
  }, [cenario]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handlePassoChange = (index: number, field: string, value: any) => {
    setPassos(prev => prev.map((passo, i) => 
      i === index ? { ...passo, [field]: value } : passo
    ));
  };

  const adicionarPasso = () => {
    setPassos(prev => [...prev, {
      descricao: '',
      acao_esperada: '',
      resultado_esperado: '',
      url_inicial: '',
      elementos_interacao: [],
      observacoes: ''
    }]);
  };

  const removerPasso = (index: number) => {
    if (passos.length > 1) {
      setPassos(prev => prev.filter((_, i) => i !== index));
    }
  };

  const handleCriterioChange = (index: number, value: string) => {
    setCriteriosSucesso(prev => prev.map((criterio, i) => 
      i === index ? value : criterio
    ));
  };

  const adicionarCriterio = () => {
    setCriteriosSucesso(prev => [...prev, '']);
  };

  const removerCriterio = (index: number) => {
    if (criteriosSucesso.length > 1) {
      setCriteriosSucesso(prev => prev.filter((_, i) => i !== index));
    }
  };

  const handleMetricaChange = (field: keyof MetricasAlvo, value: number) => {
    setMetricasAlvo(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.nome_cenario.trim()) {
      errors.nome_cenario = 'Nome do cenário é obrigatório';
    }

    if (!formData.descricao.trim()) {
      errors.descricao = 'Descrição é obrigatória';
    }

    if (formData.tempo_estimado_minutos < 5 || formData.tempo_estimado_minutos > 120) {
      errors.tempo_estimado_minutos = 'Tempo deve estar entre 5 e 120 minutos';
    }

    const passosValidos = passos.filter(passo => 
      passo.descricao.trim() && passo.acao_esperada.trim() && passo.resultado_esperado.trim()
    );

    if (passosValidos.length === 0) {
      errors.passos = 'Pelo menos um passo completo é obrigatório';
    }

    const criteriosValidos = criteriosSucesso.filter(criterio => criterio.trim());
    if (criteriosValidos.length === 0) {
      errors.criterios = 'Pelo menos um critério de sucesso é obrigatório';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const dadosCenario = {
        ...formData,
        passos: passos
          .filter(passo =>
            passo.descricao.trim() && passo.acao_esperada.trim() && passo.resultado_esperado.trim()
          )
          .map((passo, index) => ({
            ...passo,
            passo_numero: index + 1
          })),
        criterios_sucesso: criteriosSucesso.filter(criterio => criterio.trim()),
        metricas_alvo: metricasAlvo
      };

      if (cenario) {
        await atualizarCenario(cenario.cenario_id, dadosCenario);
      } else {
        await criarCenario(dadosCenario);
      }

      onSuccess();
    } catch (error) {
      console.error('Erro ao salvar cenário:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">
          {cenario ? 'Editar Cenário' : 'Novo Cenário'}
        </h2>
        <Button
          onClick={onClose}
          className="bg-gray-300 hover:bg-gray-400 text-gray-700"
        >
          Cancelar
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Informações Básicas */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Informações Básicas</h3>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nome do Cenário *
              </label>
              <input
                type="text"
                value={formData.nome_cenario}
                onChange={(e) => handleInputChange('nome_cenario', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  validationErrors.nome_cenario ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Ex: Realizar agendamento completo"
              />
              {validationErrors.nome_cenario && (
                <p className="text-red-500 text-sm mt-1">{validationErrors.nome_cenario}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Descrição *
              </label>
              <textarea
                value={formData.descricao}
                onChange={(e) => handleInputChange('descricao', e.target.value)}
                rows={3}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  validationErrors.descricao ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Descreva o objetivo e contexto do cenário de teste"
              />
              {validationErrors.descricao && (
                <p className="text-red-500 text-sm mt-1">{validationErrors.descricao}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Papel do Usuário
                </label>
                <select
                  value={formData.papel_usuario}
                  onChange={(e) => handleInputChange('papel_usuario', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Administrador">Administrador</option>
                  <option value="Proprietario">Proprietário</option>
                  <option value="Colaborador">Colaborador</option>
                  <option value="Cliente">Cliente</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Categoria
                </label>
                <select
                  value={formData.categoria}
                  onChange={(e) => handleInputChange('categoria', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Onboarding">Onboarding</option>
                  <option value="Agendamento">Agendamento</option>
                  <option value="Gestao">Gestão</option>
                  <option value="Pagamento">Pagamento</option>
                  <option value="Navegacao">Navegação</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Dificuldade
                </label>
                <select
                  value={formData.dificuldade}
                  onChange={(e) => handleInputChange('dificuldade', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Facil">Fácil</option>
                  <option value="Medio">Médio</option>
                  <option value="Dificil">Difícil</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tempo Estimado (min)
                </label>
                <input
                  type="number"
                  min="5"
                  max="120"
                  value={formData.tempo_estimado_minutos}
                  onChange={(e) => handleInputChange('tempo_estimado_minutos', parseInt(e.target.value))}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    validationErrors.tempo_estimado_minutos ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {validationErrors.tempo_estimado_minutos && (
                  <p className="text-red-500 text-sm mt-1">{validationErrors.tempo_estimado_minutos}</p>
                )}
              </div>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="ativo"
                checked={formData.ativo}
                onChange={(e) => handleInputChange('ativo', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="ativo" className="ml-2 block text-sm text-gray-700">
                Cenário ativo
              </label>
            </div>
          </CardContent>
        </Card>

        {/* Passos do Teste */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Passos do Teste</h3>
              <Button
                type="button"
                onClick={adicionarPasso}
                className="bg-green-600 hover:bg-green-700 text-sm"
              >
                + Adicionar Passo
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {validationErrors.passos && (
              <p className="text-red-500 text-sm">{validationErrors.passos}</p>
            )}
            
            {passos.map((passo, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-center mb-3">
                  <h4 className="font-medium text-gray-900">Passo {index + 1}</h4>
                  {passos.length > 1 && (
                    <Button
                      type="button"
                      onClick={() => removerPasso(index)}
                      className="bg-red-600 hover:bg-red-700 text-sm"
                    >
                      Remover
                    </Button>
                  )}
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Descrição do Passo *
                    </label>
                    <textarea
                      value={passo.descricao}
                      onChange={(e) => handlePassoChange(index, 'descricao', e.target.value)}
                      rows={2}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Descreva o que o usuário deve fazer"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Ação Esperada *
                    </label>
                    <textarea
                      value={passo.acao_esperada}
                      onChange={(e) => handlePassoChange(index, 'acao_esperada', e.target.value)}
                      rows={2}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Qual ação específica o usuário deve realizar"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Resultado Esperado *
                    </label>
                    <textarea
                      value={passo.resultado_esperado}
                      onChange={(e) => handlePassoChange(index, 'resultado_esperado', e.target.value)}
                      rows={2}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="O que deve acontecer após a ação"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      URL Inicial (opcional)
                    </label>
                    <input
                      type="url"
                      value={passo.url_inicial || ''}
                      onChange={(e) => handlePassoChange(index, 'url_inicial', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="https://..."
                    />
                  </div>
                </div>
                
                <div className="mt-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Observações (opcional)
                  </label>
                  <textarea
                    value={passo.observacoes || ''}
                    onChange={(e) => handlePassoChange(index, 'observacoes', e.target.value)}
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Observações adicionais para este passo"
                  />
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Critérios de Sucesso */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Critérios de Sucesso</h3>
              <Button
                type="button"
                onClick={adicionarCriterio}
                className="bg-green-600 hover:bg-green-700 text-sm"
              >
                + Adicionar Critério
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            {validationErrors.criterios && (
              <p className="text-red-500 text-sm">{validationErrors.criterios}</p>
            )}
            
            {criteriosSucesso.map((criterio, index) => (
              <div key={index} className="flex gap-2">
                <input
                  type="text"
                  value={criterio}
                  onChange={(e) => handleCriterioChange(index, e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: Usuário consegue completar o agendamento sem erros"
                />
                {criteriosSucesso.length > 1 && (
                  <Button
                    type="button"
                    onClick={() => removerCriterio(index)}
                    className="bg-red-600 hover:bg-red-700 text-sm"
                  >
                    Remover
                  </Button>
                )}
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Métricas Alvo */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Métricas Alvo</h3>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tempo Máximo (min)
                </label>
                <input
                  type="number"
                  min="5"
                  max="180"
                  value={metricasAlvo.tempo_maximo_minutos}
                  onChange={(e) => handleMetricaChange('tempo_maximo_minutos', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Taxa de Sucesso Mínima (%)
                </label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={metricasAlvo.taxa_sucesso_minima}
                  onChange={(e) => handleMetricaChange('taxa_sucesso_minima', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Satisfação Mínima (1-10)
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={metricasAlvo.pontuacao_satisfacao_minima}
                  onChange={(e) => handleMetricaChange('pontuacao_satisfacao_minima', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Taxa de Erro Máxima (%)
                </label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={metricasAlvo.taxa_erro_maxima}
                  onChange={(e) => handleMetricaChange('taxa_erro_maxima', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Botões de Ação */}
        <div className="flex justify-end space-x-3 pt-6">
          <Button
            type="button"
            onClick={onClose}
            className="bg-gray-300 hover:bg-gray-400 text-gray-700"
          >
            Cancelar
          </Button>
          <Button
            type="submit"
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {loading ? 'Salvando...' : (cenario ? 'Atualizar' : 'Criar')} Cenário
          </Button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mt-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Erro ao salvar cenário
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  {error}
                </div>
              </div>
            </div>
          </div>
        )}
      </form>
    </div>
  );
}
