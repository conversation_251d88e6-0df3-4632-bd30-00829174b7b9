import { NextResponse } from 'next/server';
import { NotificationService } from '@/services/NotificationService';

export async function POST(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const notificacaoId = parseInt(resolvedParams.id);

    if (isNaN(notificacaoId)) {
      return NextResponse.json({
        success: false,
        error: 'ID da notificação inválido'
      }, { status: 400 });
    }

    console.log(`🔄 API: Reenviando notificação ${notificacaoId}`);

    const notificationService = new NotificationService();
    const resultado = await notificationService.reenviarNotificacao(notificacaoId);

    if (resultado.success) {
      return NextResponse.json({
        success: true,
        message: 'Notificação reenviada com sucesso',
        tentativas: resultado.tentativas
      });
    } else {
      return NextResponse.json({
        success: false,
        error: resultado.error
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ Erro ao reenviar notificação:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 });
  }
}
