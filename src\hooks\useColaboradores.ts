'use client';

import { useState, useCallback } from 'react';
import { 
  Colaborador, 
  CriarConviteData, 
  AtualizarColaboradorData, 
  FiltrosColaboradores, 
  ColaboradorApiResponse,
  AceitarConviteData,
  DadosConvite
} from '@/types/colaboradores';

export function useColaboradores() {
  const [colaboradores, setColaboradores] = useState<Colaborador[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [empresaId, setEmpresaId] = useState<number | null>(null);
  const [empresaNome, setEmpresaNome] = useState<string | null>(null);

  // Buscar colaboradores
  const buscarColaboradores = useCallback(async (filtros?: FiltrosColaboradores) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      
      if (filtros?.ativo !== undefined) {
        params.append('ativo', filtros.ativo.toString());
      }
      if (filtros?.ativo_como_prestador !== undefined) {
        params.append('ativo_como_prestador', filtros.ativo_como_prestador.toString());
      }
      if (filtros?.convite_aceito !== undefined) {
        params.append('convite_aceito', filtros.convite_aceito.toString());
      }

      const url = `/api/colaboradores${params.toString() ? `?${params.toString()}` : ''}`;
      const response = await fetch(url);
      const data: ColaboradorApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao buscar colaboradores');
      }

      if (data.success && Array.isArray(data.data)) {
        setColaboradores(data.data);
        if ('empresa_id' in data) {
          setEmpresaId(data.empresa_id as number);
        }
        if ('empresa_nome' in data) {
          setEmpresaNome(data.empresa_nome as string);
        }
      } else {
        throw new Error('Formato de resposta inválido');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao buscar colaboradores:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Criar convite para colaborador
  const criarConvite = useCallback(async (dadosConvite: CriarConviteData): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/colaboradores', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dadosConvite),
      });

      const data: ColaboradorApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao criar convite');
      }

      if (data.success && data.data) {
        // Adicionar o novo convite à lista
        setColaboradores(prev => [data.data as Colaborador, ...prev]);
        return true;
      } else {
        throw new Error('Formato de resposta inválido');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao criar convite:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // Atualizar colaborador
  const atualizarColaborador = useCallback(async (colaboradorId: number, dadosAtualizacao: AtualizarColaboradorData): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/colaboradores/${colaboradorId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dadosAtualizacao),
      });

      const data: ColaboradorApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao atualizar colaborador');
      }

      if (data.success && data.data && !Array.isArray(data.data)) {
        // Atualizar o colaborador na lista
        setColaboradores(prev => 
          prev.map(colaborador => 
            colaborador.associacao_id === colaboradorId ? data.data as Colaborador : colaborador
          )
        );
        return true;
      } else {
        throw new Error('Formato de resposta inválido');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao atualizar colaborador:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // Remover colaborador
  const removerColaborador = useCallback(async (colaboradorId: number): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/colaboradores/${colaboradorId}`, {
        method: 'DELETE',
      });

      const data: ColaboradorApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao remover colaborador');
      }

      if (data.success) {
        // Remover o colaborador da lista
        setColaboradores(prev => prev.filter(colaborador => colaborador.associacao_id !== colaboradorId));
        return true;
      } else {
        throw new Error('Erro ao remover colaborador');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao remover colaborador:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // Buscar colaborador específico
  const buscarColaborador = useCallback(async (colaboradorId: number): Promise<Colaborador | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/colaboradores/${colaboradorId}`);
      const data: ColaboradorApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao buscar colaborador');
      }

      if (data.success && data.data && !Array.isArray(data.data)) {
        return data.data as Colaborador;
      } else {
        throw new Error('Colaborador não encontrado');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao buscar colaborador:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Alternar status ativo/inativo
  const alternarStatusColaborador = useCallback(async (colaboradorId: number, ativo: boolean): Promise<boolean> => {
    return await atualizarColaborador(colaboradorId, { ativo });
  }, [atualizarColaborador]);

  // Alternar status como prestador
  const alternarStatusPrestador = useCallback(async (colaboradorId: number, ativo_como_prestador: boolean): Promise<boolean> => {
    return await atualizarColaborador(colaboradorId, { ativo_como_prestador });
  }, [atualizarColaborador]);

  // Aceitar convite
  const aceitarConvite = useCallback(async (dadosAceite: AceitarConviteData): Promise<any> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/colaboradores/aceitar-convite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dadosAceite),
      });

      const data: ColaboradorApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao aceitar convite');
      }

      if (data.success) {
        return data.data;
      } else {
        throw new Error('Erro ao aceitar convite');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao aceitar convite:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Verificar dados do convite
  const verificarConvite = useCallback(async (token: string): Promise<DadosConvite | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/colaboradores/aceitar-convite?token=${token}`);
      const data: ColaboradorApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao verificar convite');
      }

      if (data.success && data.data) {
        return data.data as DadosConvite;
      } else {
        throw new Error('Convite não encontrado');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao verificar convite:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Limpar erro
  const limparError = useCallback(() => {
    setError(null);
  }, []);

  // Estatísticas dos colaboradores
  const estatisticas = {
    total: colaboradores.length,
    ativos: colaboradores.filter(c => c.ativo).length,
    inativos: colaboradores.filter(c => !c.ativo).length,
    prestadores_ativos: colaboradores.filter(c => c.ativo_como_prestador && c.ativo).length,
    convites_pendentes: colaboradores.filter(c => !c.convite_aceito && c.ativo).length,
    convites_expirados: colaboradores.filter(c => 
      !c.convite_aceito && 
      c.convite_expira_em && 
      new Date() > new Date(c.convite_expira_em)
    ).length
  };

  return {
    colaboradores,
    loading,
    error,
    empresaId,
    empresaNome,
    estatisticas,
    buscarColaboradores,
    criarConvite,
    atualizarColaborador,
    removerColaborador,
    buscarColaborador,
    alternarStatusColaborador,
    alternarStatusPrestador,
    aceitarConvite,
    verificarConvite,
    limparError
  };
}
