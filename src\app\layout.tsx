import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "@/app/globals.css";
import { AuthProvider } from "@/contexts/AuthContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { BrandingProvider } from "@/contexts/BrandingContext";
import { OnboardingProvider } from "@/contexts/OnboardingContext";
import { SkipLinks } from "@/components/accessibility/SkipLinks";
import { PWAPrompt } from "@/components/ui/PWAPrompt";
import { OnboardingTour } from "@/components/onboarding/OnboardingTour";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ServiceTech",
  description: "Plataforma de Agendamento e Gestão para Estabelecimentos de Serviços Pessoais",
  keywords: "agendamento, serviços pessoais, barbearia, salão, estética",
  authors: [{ name: "ServiceTech Team" }],
  viewport: "width=device-width, initial-scale=1",
  robots: "index, follow",
  openGraph: {
    title: "ServiceTech",
    description: "Plataforma de Agendamento e Gestão para Estabelecimentos de Serviços Pessoais",
    type: "website",
  },
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'ServiceTech',
  },
  other: {
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': 'ServiceTech',
    'application-name': 'ServiceTech',
    'msapplication-TileColor': '#3B82F6',
    'theme-color': '#3B82F6',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt-BR">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning={true}
      >
        <SkipLinks />
        <ThemeProvider>
          <BrandingProvider>
            <AuthProvider>
              <OnboardingProvider>
                {children}
                <PWAPrompt />
                <OnboardingTour />
              </OnboardingProvider>
            </AuthProvider>
          </BrandingProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
