'use client';

import React, { useState } from 'react';
import { usePWA } from '@/hooks/usePWA';
import { Button } from './Button';

interface PWAPromptProps {
  className?: string;
}

export function PWAPrompt({ className = '' }: PWAPromptProps) {
  const { isInstallable, isInstalled, isOffline, installApp, updateAvailable, updateApp } = usePWA();
  const [isInstalling, setIsInstalling] = useState(false);
  const [showPrompt, setShowPrompt] = useState(true);

  const handleInstall = async () => {
    if (!isInstallable) return;

    setIsInstalling(true);
    try {
      await installApp();
      setShowPrompt(false);
    } catch (error) {
      console.error('Erro ao instalar PWA:', error);
    } finally {
      setIsInstalling(false);
    }
  };

  const handleUpdate = () => {
    updateApp();
  };

  const handleDismiss = () => {
    setShowPrompt(false);
    // Salvar no localStorage para não mostrar novamente por um tempo
    localStorage.setItem('pwa-prompt-dismissed', Date.now().toString());
  };

  // Verificar se o prompt foi dispensado recentemente (24 horas)
  React.useEffect(() => {
    const dismissed = localStorage.getItem('pwa-prompt-dismissed');
    if (dismissed) {
      const dismissedTime = parseInt(dismissed);
      const now = Date.now();
      const dayInMs = 24 * 60 * 60 * 1000;
      
      if (now - dismissedTime < dayInMs) {
        setShowPrompt(false);
      }
    }
  }, []);

  // Não mostrar se já está instalado ou não é instalável
  if (isInstalled || (!isInstallable && !updateAvailable) || !showPrompt) {
    return null;
  }

  return (
    <div className={`fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm z-50 ${className}`}>
      <div className="bg-[var(--surface)] border border-[var(--border-color)] rounded-lg shadow-lg p-4">
        {/* Status offline */}
        {isOffline && (
          <div className="mb-3 p-2 bg-[var(--warning)] bg-opacity-10 border border-[var(--warning)] rounded-md">
            <div className="flex items-center space-x-2">
              <svg 
                className="w-4 h-4 text-[var(--warning)]" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" 
                />
              </svg>
              <span className="text-sm text-[var(--warning)]">
                Você está offline. Algumas funcionalidades podem estar limitadas.
              </span>
            </div>
          </div>
        )}

        {/* Prompt de atualização */}
        {updateAvailable && (
          <div className="mb-3">
            <h3 className="text-sm font-semibold text-[var(--text-primary)] mb-2">
              Atualização Disponível
            </h3>
            <p className="text-xs text-[var(--text-secondary)] mb-3">
              Uma nova versão do ServiceTech está disponível. Atualize para ter acesso às últimas funcionalidades.
            </p>
            <div className="flex space-x-2">
              <Button
                onClick={handleUpdate}
                size="sm"
                variant="primary"
                className="flex-1"
              >
                Atualizar
              </Button>
              <Button
                onClick={handleDismiss}
                size="sm"
                variant="ghost"
              >
                Depois
              </Button>
            </div>
          </div>
        )}

        {/* Prompt de instalação */}
        {isInstallable && !updateAvailable && (
          <div>
            <div className="flex items-start space-x-3 mb-3">
              <div className="flex-shrink-0">
                <svg 
                  className="w-8 h-8 text-[var(--primary)]" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" 
                  />
                </svg>
              </div>
              <div className="flex-1">
                <h3 className="text-sm font-semibold text-[var(--text-primary)] mb-1">
                  Instalar ServiceTech
                </h3>
                <p className="text-xs text-[var(--text-secondary)]">
                  Instale o app para acesso rápido, notificações e uso offline.
                </p>
              </div>
            </div>

            <div className="flex space-x-2">
              <Button
                onClick={handleInstall}
                disabled={isInstalling}
                size="sm"
                variant="primary"
                className="flex-1"
              >
                {isInstalling ? (
                  <div className="flex items-center space-x-2">
                    <svg 
                      className="w-4 h-4 animate-spin" 
                      fill="none" 
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <circle 
                        className="opacity-25" 
                        cx="12" 
                        cy="12" 
                        r="10" 
                        stroke="currentColor" 
                        strokeWidth="4"
                      />
                      <path 
                        className="opacity-75" 
                        fill="currentColor" 
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      />
                    </svg>
                    <span>Instalando...</span>
                  </div>
                ) : (
                  'Instalar'
                )}
              </Button>
              <Button
                onClick={handleDismiss}
                size="sm"
                variant="ghost"
              >
                Não agora
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Componente para mostrar status PWA no header
export function PWAStatus() {
  const { isInstalled, isOffline } = usePWA();

  if (!isInstalled && !isOffline) {
    return null;
  }

  return (
    <div className="flex items-center space-x-2">
      {isInstalled && (
        <div 
          className="flex items-center space-x-1 px-2 py-1 bg-[var(--success)] bg-opacity-10 text-[var(--success)] rounded-md"
          title="App instalado"
        >
          <svg 
            className="w-3 h-3" 
            fill="currentColor" 
            viewBox="0 0 20 20"
            aria-hidden="true"
          >
            <path 
              fillRule="evenodd" 
              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
              clipRule="evenodd" 
            />
          </svg>
          <span className="text-xs font-medium">PWA</span>
        </div>
      )}
      
      {isOffline && (
        <div 
          className="flex items-center space-x-1 px-2 py-1 bg-[var(--warning)] bg-opacity-10 text-[var(--warning)] rounded-md"
          title="Modo offline"
        >
          <svg 
            className="w-3 h-3" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M18.364 5.636l-12.728 12.728m0-12.728l12.728 12.728" 
            />
          </svg>
          <span className="text-xs font-medium">Offline</span>
        </div>
      )}
    </div>
  );
}
