'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useAuth } from '@/contexts/AuthContext';

function PagamentoErroContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);

  const agendamentoId = searchParams.get('agendamento_id');
  const erro = searchParams.get('error') || 'Erro desconhecido no pagamento';

  useEffect(() => {
    if (authLoading) return;

    if (!user) {
      router.push('/login');
      return;
    }

    setLoading(false);
  }, [user, authLoading, router]);

  const handleTentarNovamente = () => {
    if (agendamentoId) {
      router.push(`/agendamento/pagamento/${agendamentoId}`);
    } else {
      router.push('/buscar');
    }
  };

  const handleIrParaDashboard = () => {
    router.push('/cliente/dashboard');
  };

  const handleNovoAgendamento = () => {
    router.push('/buscar');
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center">
        <div className="flex items-center gap-3">
          <div className="w-6 h-6 border-2 border-[var(--primary)] border-t-transparent rounded-full animate-spin"></div>
          <span className="text-[var(--text-secondary)]">Carregando...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[var(--background)]">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header de erro */}
          <div className="text-center mb-8">
            <div className="w-20 h-20 bg-[var(--error)] rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-10 h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-[var(--text-primary)] mb-2">
              Falha no Pagamento
            </h1>
            <p className="text-[var(--text-secondary)]">
              Não foi possível processar seu pagamento
            </p>
          </div>

          {/* Detalhes do erro */}
          <Card className="mb-6 bg-[var(--error-light)] border-[var(--error)]">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-[var(--error)] rounded-full flex items-center justify-center mt-1">
                  <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-[var(--text-primary)] mb-2">
                    O que aconteceu?
                  </h3>
                  <p className="text-[var(--text-secondary)] text-sm mb-4">
                    {erro}
                  </p>
                  <div className="text-sm text-[var(--text-secondary)]">
                    <p className="mb-2"><strong>Possíveis causas:</strong></p>
                    <ul className="list-disc list-inside space-y-1">
                      <li>Dados do cartão incorretos ou expirados</li>
                      <li>Limite insuficiente no cartão</li>
                      <li>Problemas temporários com o banco</li>
                      <li>Conexão instável com a internet</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Status do agendamento */}
          <Card className="mb-6 bg-[var(--warning-light)] border-[var(--warning)]">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-[var(--warning)] rounded-full flex items-center justify-center mt-0.5">
                  <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-[var(--text-primary)] mb-1">
                    Seu Agendamento
                  </h4>
                  <p className="text-sm text-[var(--text-secondary)]">
                    Seu agendamento foi criado mas ainda não foi pago. Você pode tentar o pagamento novamente 
                    ou escolher pagar no local durante o atendimento.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Opções de ação */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-4">
                O que você gostaria de fazer?
              </h3>
              
              <div className="space-y-4">
                {agendamentoId && (
                  <div className="p-4 border border-[var(--border)] rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-[var(--text-primary)]">
                          Tentar Pagamento Novamente
                        </h4>
                        <p className="text-sm text-[var(--text-secondary)]">
                          Voltar à página de pagamento e tentar com outro cartão ou método
                        </p>
                      </div>
                      <Button onClick={handleTentarNovamente}>
                        Tentar Novamente
                      </Button>
                    </div>
                  </div>
                )}
                
                <div className="p-4 border border-[var(--border)] rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-[var(--text-primary)]">
                        Ver Meus Agendamentos
                      </h4>
                      <p className="text-sm text-[var(--text-secondary)]">
                        Acessar seu dashboard para gerenciar agendamentos
                      </p>
                    </div>
                    <Button variant="outline" onClick={handleIrParaDashboard}>
                      Dashboard
                    </Button>
                  </div>
                </div>
                
                <div className="p-4 border border-[var(--border)] rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-[var(--text-primary)]">
                        Fazer Novo Agendamento
                      </h4>
                      <p className="text-sm text-[var(--text-secondary)]">
                        Buscar outros estabelecimentos e serviços
                      </p>
                    </div>
                    <Button variant="outline" onClick={handleNovoAgendamento}>
                      Buscar Serviços
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Informações de suporte */}
          <Card className="bg-[var(--info-light)] border-[var(--info)]">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-[var(--info)] rounded-full flex items-center justify-center mt-0.5">
                  <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-[var(--text-primary)] mb-1">
                    Precisa de Ajuda?
                  </h4>
                  <p className="text-sm text-[var(--text-secondary)]">
                    Se o problema persistir, entre em contato conosco ou com o estabelecimento. 
                    Nenhuma cobrança foi realizada em seu cartão.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

export default function PagamentoErroPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center">
        <div className="flex items-center gap-3">
          <div className="w-6 h-6 border-2 border-[var(--primary)] border-t-transparent rounded-full animate-spin"></div>
          <span className="text-[var(--text-secondary)]">Carregando...</span>
        </div>
      </div>
    }>
      <PagamentoErroContent />
    </Suspense>
  );
}
