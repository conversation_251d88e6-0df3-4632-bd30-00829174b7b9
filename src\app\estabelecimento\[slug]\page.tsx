'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Layout } from '@/components/layout/Layout';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Breadcrumbs } from '@/components/ui/Breadcrumbs';
import { LogoEmpresaHeader } from '@/components/ui/LogoEmpresa';

interface DadosEmpresa {
  empresa: {
    empresa_id: number;
    nome_empresa: string;
    telefone?: string;
    endereco: string;
    numero: string;
    complemento?: string;
    bairro: string;
    cidade: string;
    estado: string;
    cep: string;
    descricao?: string;
    logo_url?: string;
    imagem_capa_url?: string;
    fotos_portfolio_urls?: string[];
    horario_funcionamento?: any;
    segmento?: string;
    slug: string;
  };
  servicos: Array<{
    servico_id: number;
    nome_servico: string;
    descricao: string;
    duracao_minutos: number;
    preco: number;
    categoria: string;
  }>;
  servicos_por_categoria: Record<string, any[]>;
  colaboradores: Array<{
    colaborador_user_id: string;
    name: string;
    ativo_como_prestador: boolean;
  }>;
  estatisticas: {
    total_servicos: number;
    total_colaboradores: number;
    categorias_servicos: number;
  };
}

export default function CompanyPublicPage() {
  const params = useParams();
  const router = useRouter();
  const [dadosEmpresa, setDadosEmpresa] = useState<DadosEmpresa | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const buscarDadosEmpresa = async () => {
      try {
        setLoading(true);
        setError(null);

        // Usar o identificador diretamente (pode ser ID ou slug)
        const identificador = params.slug as string;
        console.log('🔍 Buscando empresa com identificador:', identificador);

        if (!identificador) {
          throw new Error('Identificador da empresa não fornecido');
        }

        const url = `/api/empresas/${identificador}`;
        console.log('📡 Fazendo fetch para:', url);
        console.log('📡 URL completa:', window.location.origin + url);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          cache: 'no-cache'
        });
        console.log('📊 Response status:', response.status, response.statusText);
        console.log('📊 Response headers:', Object.fromEntries(response.headers.entries()));

        const responseText = await response.text();
        console.log('📊 Response text (raw):', responseText);

        let result;
        try {
          result = JSON.parse(responseText);
          console.log('📊 Response data (parsed):', result);
        } catch (parseError) {
          console.error('❌ Erro ao fazer parse do JSON:', parseError);
          console.error('❌ Response text que causou erro:', responseText);
          throw new Error('Resposta da API não é um JSON válido');
        }

        if (!response.ok) {
          console.error('❌ Response não OK:', result.error);
          throw new Error(result.error ?? 'Erro ao buscar dados da empresa');
        }

        console.log('✅ Dados recebidos:', result.data);
        console.log('📊 Tipo de result.data:', typeof result.data);
        console.log('📊 result.data é null:', result.data === null);
        console.log('📊 result.data é undefined:', result.data === undefined);

        if (result.data && result.data.empresa) {
          console.log('✅ Empresa encontrada:', result.data.empresa.nome_empresa);
        } else {
          console.error('❌ Dados da empresa não encontrados na resposta');
        }

        setDadosEmpresa(result.data);
      } catch (error: any) {
        console.error('❌ Erro capturado:', error.message);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    if (params.slug) {
      buscarDadosEmpresa();
    }
  }, [params.slug]);

  const formatarPreco = (preco: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(preco);
  };

  const formatarDuracao = (minutos: number) => {
    if (minutos < 60) {
      return `${minutos}min`;
    }
    const horas = Math.floor(minutos / 60);
    const minutosRestantes = minutos % 60;
    return minutosRestantes > 0 ? `${horas}h ${minutosRestantes}min` : `${horas}h`;
  };

  const formatarHorarioFuncionamento = (horario: any) => {
    if (!horario) return 'Horário não informado';

    const diasSemana = [
      { key: 'domingo', nome: 'Domingo' },
      { key: 'segunda', nome: 'Segunda' },
      { key: 'terca', nome: 'Terça' },
      { key: 'quarta', nome: 'Quarta' },
      { key: 'quinta', nome: 'Quinta' },
      { key: 'sexta', nome: 'Sexta' },
      { key: 'sabado', nome: 'Sábado' }
    ];

    return diasSemana.map(dia => {
      const horarioDia = horario[dia.key];
      if (!horarioDia?.ativo) {
        return `${dia.nome}: Fechado`;
      }
      return `${dia.nome}: ${horarioDia.abertura} - ${horarioDia.fechamento}`;
    });
  };

  const handleAgendar = () => {
    if (dadosEmpresa) {
      router.push(`/agendamento/${dadosEmpresa.empresa.empresa_id}`);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="bg-[var(--background)] py-8">
          <div className="container mx-auto px-4">
            <div className="text-center py-12">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
              <p className="text-[var(--text-secondary)] mt-4">Carregando informações do estabelecimento...</p>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  // Debug: Log do estado atual
  console.log('🔍 Estado atual:');
  console.log('   loading:', loading);
  console.log('   error:', error);
  console.log('   dadosEmpresa:', dadosEmpresa);
  console.log('   !dadosEmpresa:', !dadosEmpresa);

  if (error || !dadosEmpresa) {
    console.log('❌ Exibindo tela de erro');
    console.log('   Motivo - error:', error);
    console.log('   Motivo - !dadosEmpresa:', !dadosEmpresa);

    return (
      <Layout>
        <div className="bg-[var(--background)] py-8">
          <div className="container mx-auto px-4">
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-[var(--text-secondary)] mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <p className="text-[var(--error)] text-lg">
                {error ?? 'Estabelecimento não encontrado'}
              </p>
              <p className="text-[var(--text-secondary)] text-sm mt-2">
                Debug: error={error ? 'true' : 'false'}, dadosEmpresa={dadosEmpresa ? 'exists' : 'null/undefined'}
              </p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => router.push('/buscar')}
              >
                Buscar Outros Estabelecimentos
              </Button>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  const { empresa, servicos, servicos_por_categoria, colaboradores, estatisticas } = dadosEmpresa;
  const enderecoCompleto = `${empresa.endereco}, ${empresa.numero}${empresa.complemento ? `, ${empresa.complemento}` : ''} - ${empresa.bairro}, ${empresa.cidade}/${empresa.estado}`;

  return (
    <Layout>
      <div className="bg-[var(--background)] py-8">
        <div className="container mx-auto px-4">
          {/* Breadcrumbs */}
          <div className="mb-8">
            <Breadcrumbs
              items={[
                { label: 'Início', href: '/' },
                { label: 'Buscar', href: '/buscar' },
                { label: empresa.nome_empresa }
              ]}
            />
          </div>

          {/* Imagem de Capa */}
          {empresa.imagem_capa_url && (
            <div className="mb-8 rounded-lg overflow-hidden shadow-lg">
              <div className="relative h-64 md:h-80 lg:h-96">
                <img
                  src={empresa.imagem_capa_url}
                  alt={`Capa de ${empresa.nome_empresa}`}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                <div className="absolute bottom-6 left-6 text-white">
                  <h1 className="text-3xl md:text-4xl font-bold mb-2">{empresa.nome_empresa}</h1>
                  <p className="text-white/90 text-lg">{empresa.segmento}</p>
                </div>
              </div>
            </div>
          )}

          {/* Header da empresa */}
          <Card className="mb-8 overflow-hidden">
            <div className="bg-gradient-to-r from-[var(--primary)] to-[var(--primary-dark)] text-white p-8">
              <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
                <LogoEmpresaHeader
                  nomeEmpresa={empresa.nome_empresa}
                  logoUrl={empresa.logo_url}
                  segmento={empresa.segmento}
                  className="bg-white/20"
                />

                <div className="flex-1">
                  <h1 className="text-3xl md:text-4xl font-bold mb-2">{empresa.nome_empresa}</h1>
                  <p className="text-white/80 mb-2">{enderecoCompleto}</p>
                  {empresa.telefone && (
                    <p className="text-white/80">📞 {empresa.telefone}</p>
                  )}
                  {empresa.segmento && (
                    <span className="inline-block bg-white/20 px-3 py-1 rounded-full text-sm mt-2">
                      {empresa.segmento}
                    </span>
                  )}
                </div>

                <div className="text-center">
                  <Button
                    onClick={handleAgendar}
                    className="bg-white text-[var(--primary)] hover:bg-white/90 font-semibold px-8 py-3 text-lg"
                  >
                    Agendar Agora
                  </Button>
                  <p className="text-white/80 text-sm mt-2">
                    {estatisticas.total_servicos} serviços • {estatisticas.total_colaboradores} profissionais
                  </p>
                </div>
              </div>
            </div>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Coluna principal */}
            <div className="lg:col-span-2 space-y-8">
              {/* Descrição */}
              {empresa.descricao && (
                <Card>
                  <CardContent className="p-6">
                    <h2 className="text-2xl font-semibold text-[var(--text-primary)] mb-4">
                      Sobre o Estabelecimento
                    </h2>
                    <p className="text-[var(--text-secondary)] leading-relaxed">
                      {empresa.descricao}
                    </p>
                  </CardContent>
                </Card>
              )}

              {/* Serviços */}
              <Card>
                <CardContent className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-2xl font-semibold text-[var(--text-primary)]">
                      Nossos Serviços
                    </h2>
                    <Button onClick={handleAgendar} variant="outline">
                      Agendar Serviço
                    </Button>
                  </div>

                  {Object.keys(servicos_por_categoria).length > 0 ? (
                    <div className="space-y-6">
                      {Object.entries(servicos_por_categoria).map(([categoria, servicosCategoria]) => (
                        <div key={categoria}>
                          <h3 className="text-lg font-medium text-[var(--text-primary)] mb-3 pb-2 border-b border-[var(--border)]">
                            {categoria} ({servicosCategoria.length})
                          </h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {servicosCategoria.map((servico: any) => (
                              <div
                                key={servico.servico_id}
                                className="p-4 border border-[var(--border)] rounded-lg hover:shadow-md transition-shadow"
                              >
                                <div className="flex justify-between items-start mb-2">
                                  <h4 className="font-medium text-[var(--text-primary)]">
                                    {servico.nome_servico}
                                  </h4>
                                  <span className="font-semibold text-[var(--primary)]">
                                    {formatarPreco(servico.preco)}
                                  </span>
                                </div>
                                {servico.descricao && (
                                  <p className="text-sm text-[var(--text-secondary)] mb-2">
                                    {servico.descricao}
                                  </p>
                                )}
                                <div className="flex items-center text-sm text-[var(--text-secondary)]">
                                  <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                  </svg>
                                  {formatarDuracao(servico.duracao_minutos)}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <svg className="mx-auto h-12 w-12 text-[var(--text-secondary)] mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <p className="text-[var(--text-secondary)]">Nenhum serviço disponível no momento</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Portfólio de Fotos */}
              {empresa.fotos_portfolio_urls && empresa.fotos_portfolio_urls.length > 0 && (
                <Card>
                  <CardContent className="p-6">
                    <h2 className="text-2xl font-semibold text-[var(--text-primary)] mb-6">
                      Nosso Portfólio
                    </h2>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                      {empresa.fotos_portfolio_urls.map((foto, index) => (
                        <div
                          key={`portfolio-${index}`}
                          className="relative aspect-square rounded-lg overflow-hidden group cursor-pointer"
                        >
                          <img
                            src={foto}
                            alt={`Portfólio ${index + 1} - ${empresa.nome_empresa}`}
                            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                          />
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>
                          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Profissionais */}
              {colaboradores.length > 0 && (
                <Card>
                  <CardContent className="p-6">
                    <h2 className="text-2xl font-semibold text-[var(--text-primary)] mb-6">
                      Nossa Equipe
                    </h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                      {colaboradores.filter(c => c.ativo_como_prestador).map((colaborador) => (
                        <div
                          key={colaborador.colaborador_user_id}
                          className="text-center p-4 border border-[var(--border)] rounded-lg"
                        >
                          <div className="w-16 h-16 bg-gradient-to-br from-[var(--secondary)] to-[var(--secondary-dark)] rounded-full flex items-center justify-center mx-auto mb-3">
                            <span className="text-white font-medium text-xl">
                              {colaborador.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <h4 className="font-medium text-[var(--text-primary)]">
                            {colaborador.name}
                          </h4>
                          <span className="text-xs bg-[var(--success-light)] text-[var(--success)] px-2 py-1 rounded mt-2 inline-block">
                            Disponível
                          </span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* CTA Principal */}
              <Card className="bg-gradient-to-br from-[var(--primary-light)] to-[var(--primary)] text-white">
                <CardContent className="p-6 text-center">
                  <svg className="w-12 h-12 mx-auto mb-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V6a2 2 0 012-2h2a2 2 0 012 2v1m-6 0h6m-6 0l-.5 6.5a2 2 0 002 2.2h3a2 2 0 002-2.2L16 7m-6 0h6" />
                  </svg>
                  <h3 className="text-xl font-semibold mb-2">Agende Agora</h3>
                  <p className="text-white/80 mb-4 text-sm">
                    Escolha o melhor horário para você e agende online
                  </p>
                  <Button
                    onClick={handleAgendar}
                    className="w-full bg-white text-[var(--primary)] hover:bg-white/90 font-semibold"
                  >
                    Fazer Agendamento
                  </Button>
                </CardContent>
              </Card>

              {/* Horários de Funcionamento */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-4">
                    Horários de Funcionamento
                  </h3>
                  <div className="space-y-2">
                    {typeof empresa.horario_funcionamento === 'string' || !empresa.horario_funcionamento ? (
                      <div className="text-sm text-[var(--text-primary)]">
                        Horário não informado
                      </div>
                    ) : (
                      (formatarHorarioFuncionamento(empresa.horario_funcionamento) as string[]).map((horario: string, index: number) => (
                        <div key={index} className="flex justify-between text-sm">
                          <span className="text-[var(--text-secondary)]">
                            {horario.split(':')[0]}:
                          </span>
                          <span className="text-[var(--text-primary)]">
                            {horario.split(':').slice(1).join(':')}
                          </span>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Informações de Contato */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-4">
                    Informações de Contato
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <svg className="w-5 h-5 text-[var(--text-secondary)] mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <div className="text-sm">
                        <p className="text-[var(--text-primary)] font-medium">Endereço</p>
                        <p className="text-[var(--text-secondary)]">{enderecoCompleto}</p>
                        {empresa.cep && (
                          <p className="text-[var(--text-secondary)]">CEP: {empresa.cep}</p>
                        )}
                      </div>
                    </div>

                    {empresa.telefone && (
                      <div className="flex items-start gap-3">
                        <svg className="w-5 h-5 text-[var(--text-secondary)] mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        <div className="text-sm">
                          <p className="text-[var(--text-primary)] font-medium">Telefone</p>
                          <p className="text-[var(--text-secondary)]">{empresa.telefone}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Estatísticas */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-4">
                    Sobre o Estabelecimento
                  </h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-[var(--text-secondary)] text-sm">Serviços</span>
                      <span className="font-medium text-[var(--text-primary)]">
                        {estatisticas.total_servicos}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-[var(--text-secondary)] text-sm">Profissionais</span>
                      <span className="font-medium text-[var(--text-primary)]">
                        {estatisticas.total_colaboradores}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-[var(--text-secondary)] text-sm">Categorias</span>
                      <span className="font-medium text-[var(--text-primary)]">
                        {estatisticas.categorias_servicos}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}