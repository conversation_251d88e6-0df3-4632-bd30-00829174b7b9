'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useAuth } from '@/contexts/AuthContext';

interface DadosAgendamento {
  agendamento_id: number;
  valor_total: number;
  status_agendamento: string;
  status_pagamento: string;
  data_hora_inicio: string;
  codigo_confirmacao: string;
  servicos: {
    nome_servico: string;
    duracao_minutos: number;
  };
  empresas: {
    nome_empresa: string;
    endereco: string;
    numero: string;
    bairro: string;
    cidade: string;
    estado: string;
    telefone_contato: string;
  };
  colaboradores: {
    name: string;
  };
}

function PagamentoSucessoContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, loading: authLoading } = useAuth();
  const [agendamento, setAgendamento] = useState<DadosAgendamento | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const agendamentoId = searchParams.get('agendamento_id');

  useEffect(() => {
    if (authLoading) return;

    if (!user) {
      router.push('/login');
      return;
    }

    if (!agendamentoId) {
      setError('ID do agendamento não fornecido');
      setLoading(false);
      return;
    }

    const verificarPagamento = async () => {
      try {
        // Aguardar um pouco para o webhook processar
        await new Promise(resolve => setTimeout(resolve, 2000));

        const response = await fetch(`/api/agendamentos/${agendamentoId}`);
        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Erro ao verificar pagamento');
        }

        const dadosAgendamento = result.data;

        // Verificar se o agendamento pertence ao usuário
        if (dadosAgendamento.cliente_user_id !== user.id) {
          setError('Agendamento não encontrado ou não autorizado');
          return;
        }

        setAgendamento(dadosAgendamento);
      } catch (error: any) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    verificarPagamento();
  }, [agendamentoId, user, authLoading, router]);

  const formatarPreco = (preco: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(preco);
  };

  const formatarDataHora = (dataHoraStr: string) => {
    const data = new Date(dataHoraStr);
    return {
      data: data.toLocaleDateString('pt-BR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      }),
      horario: data.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
      })
    };
  };

  const formatarDuracao = (minutos: number) => {
    if (minutos < 60) {
      return `${minutos}min`;
    }
    const horas = Math.floor(minutos / 60);
    const minutosRestantes = minutos % 60;
    return minutosRestantes > 0 ? `${horas}h ${minutosRestantes}min` : `${horas}h`;
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <div className="w-8 h-8 border-2 border-[var(--primary)] border-t-transparent rounded-full animate-spin"></div>
          <span className="text-[var(--text-secondary)]">Verificando pagamento...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <div className="w-16 h-16 bg-[var(--error-light)] rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-[var(--error)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-[var(--text-primary)] mb-2">
              Erro na Verificação
            </h2>
            <p className="text-[var(--text-secondary)] mb-6">
              {error}
            </p>
            <Button onClick={() => router.push('/cliente/dashboard')} className="w-full">
              Ir para Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!agendamento) {
    return (
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center">
        <span className="text-[var(--text-secondary)]">Agendamento não encontrado</span>
      </div>
    );
  }

  const { data, horario } = formatarDataHora(agendamento.data_hora_inicio);
  const enderecoCompleto = `${agendamento.empresas.endereco}, ${agendamento.empresas.numero} - ${agendamento.empresas.bairro}, ${agendamento.empresas.cidade} - ${agendamento.empresas.estado}`;

  return (
    <div className="min-h-screen bg-[var(--background)]">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header de sucesso */}
          <div className="text-center mb-8">
            <div className="w-20 h-20 bg-[var(--success)] rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-10 h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-[var(--text-primary)] mb-2">
              Pagamento Realizado com Sucesso!
            </h1>
            <p className="text-[var(--text-secondary)]">
              Seu agendamento foi confirmado e o pagamento processado
            </p>
          </div>

          {/* Status do pagamento */}
          <Card className="mb-6 bg-[var(--success-light)] border-[var(--success)]">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-[var(--success)] rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-[var(--text-primary)]">
                    Pagamento Confirmado
                  </h3>
                  <p className="text-sm text-[var(--text-secondary)]">
                    Valor pago: {formatarPreco(agendamento.valor_total)}
                  </p>
                </div>
                <div className="text-sm font-medium text-[var(--success)]">
                  {agendamento.status_pagamento}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Detalhes do agendamento */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-4">
                Detalhes do Agendamento
              </h3>
              
              <div className="space-y-4">
                <div className="flex justify-between items-start">
                  <span className="text-[var(--text-secondary)]">Código:</span>
                  <span className="font-mono font-medium text-[var(--primary)]">
                    {agendamento.codigo_confirmacao}
                  </span>
                </div>
                
                <div className="flex justify-between items-start">
                  <span className="text-[var(--text-secondary)]">Serviço:</span>
                  <span className="font-medium text-right">
                    {agendamento.servicos.nome_servico}
                    <br />
                    <span className="text-sm text-[var(--text-secondary)]">
                      {formatarDuracao(agendamento.servicos.duracao_minutos)}
                    </span>
                  </span>
                </div>
                
                <div className="flex justify-between items-start">
                  <span className="text-[var(--text-secondary)]">Profissional:</span>
                  <span className="font-medium">{agendamento.colaboradores.name}</span>
                </div>
                
                <div className="flex justify-between items-start">
                  <span className="text-[var(--text-secondary)]">Data e Horário:</span>
                  <span className="font-medium text-right">
                    {data}
                    <br />
                    <span className="text-[var(--primary)]">{horario}</span>
                  </span>
                </div>
                
                <div className="flex justify-between items-start">
                  <span className="text-[var(--text-secondary)]">Local:</span>
                  <span className="font-medium text-right max-w-xs">
                    {agendamento.empresas.nome_empresa}
                    <br />
                    <span className="text-sm text-[var(--text-secondary)]">
                      {enderecoCompleto}
                    </span>
                  </span>
                </div>
                
                <div className="flex justify-between items-start">
                  <span className="text-[var(--text-secondary)]">Contato:</span>
                  <span className="font-medium">{agendamento.empresas.telefone_contato}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Próximos passos */}
          <Card className="mb-6 bg-[var(--info-light)] border-[var(--info)]">
            <CardContent className="p-4">
              <h4 className="font-medium text-[var(--text-primary)] mb-2">
                Próximos Passos
              </h4>
              <ul className="text-sm text-[var(--text-secondary)] space-y-1">
                <li>• Você receberá uma confirmação por email</li>
                <li>• O estabelecimento confirmará seu agendamento em até 24 horas</li>
                <li>• Chegue com 10 minutos de antecedência</li>
                <li>• Apresente o código de confirmação no local</li>
              </ul>
            </CardContent>
          </Card>

          {/* Botões de ação */}
          <div className="flex flex-col sm:flex-row gap-4">
            <Button 
              variant="outline" 
              onClick={() => router.push('/cliente/dashboard')}
              className="flex-1"
            >
              Meus Agendamentos
            </Button>
            <Button 
              onClick={() => router.push('/buscar')}
              className="flex-1"
            >
              Fazer Novo Agendamento
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function PagamentoSucessoPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <div className="w-8 h-8 border-2 border-[var(--primary)] border-t-transparent rounded-full animate-spin"></div>
          <span className="text-[var(--text-secondary)]">Verificando pagamento...</span>
        </div>
      </div>
    }>
      <PagamentoSucessoContent />
    </Suspense>
  );
}
