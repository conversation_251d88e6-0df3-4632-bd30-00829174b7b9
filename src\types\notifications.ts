// Tipos para o sistema de notificações

// Tipos de notificação disponíveis
export type TipoNotificacao =
  | 'novo_agendamento'
  | 'agendamento_confirmado'
  | 'agendamento_recusado'
  | 'agendamento_cancelado'
  | 'lembrete_confirmacao'
  | 'lembrete_agendamento'
  | 'pagamento_confirmado';

// Canais de notificação
export type CanalNotificacao = 'email' | 'sms' | 'push' | 'in_app';

// Status da notificação
export type StatusNotificacao = 'pendente' | 'enviada' | 'falhou' | 'lida';

// Interface principal da notificação (baseada no schema do banco)
export interface Notificacao {
  notificacao_id: number;
  user_id: string;
  tipo_notificacao: TipoNotificacao;
  titulo: string;
  mensagem: string;
  dados_contexto: Record<string, any>;
  canal: CanalNotificacao;
  lida: boolean;
  enviada: boolean;
  data_envio?: string;
  erro_envio?: string;
  tentativas_envio: number;
  agendamento_id?: number;
  empresa_id?: number;
  created_at: string;
  updated_at: string;
}

// Dados para criação de uma nova notificação
export interface CriarNotificacaoData {
  user_id: string;
  tipo_notificacao: TipoNotificacao;
  titulo: string;
  mensagem: string;
  dados_contexto?: Record<string, any>;
  canal: CanalNotificacao;
  agendamento_id?: number;
  empresa_id?: number;
}

// Contexto específico para notificações de agendamento
export interface ContextoAgendamento {
  agendamento_id: number;
  codigo_confirmacao: string;
  cliente_nome: string;
  cliente_email: string;
  empresa_nome: string;
  empresa_endereco: string;
  servico_nome: string;
  servico_preco: number;
  colaborador_nome: string;
  data_hora_inicio: string;
  data_hora_fim: string;
  forma_pagamento: string;
  valor_total: number;
  observacoes_cliente?: string;
  prazo_confirmacao?: string;
}

// Contexto para notificações de proprietário/colaborador
export interface ContextoProprietario {
  agendamento_id: number;
  codigo_confirmacao: string;
  cliente_nome: string;
  cliente_telefone?: string;
  servico_nome: string;
  colaborador_nome: string;
  data_hora_inicio: string;
  data_hora_fim: string;
  forma_pagamento: string;
  valor_total: number;
  observacoes_cliente?: string;
  prazo_confirmacao: string;
}

// Template de email
export interface EmailTemplate {
  subject: string;
  html: string;
  text?: string;
}

// Dados para envio de email
export interface EnviarEmailData {
  to: string;
  subject: string;
  html: string;
  text?: string;
  from?: string;
}

// Resposta do serviço de email
export interface EmailResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

// Configurações de notificação do usuário
export interface PreferenciasNotificacao {
  user_id: string;
  email_enabled: boolean;
  sms_enabled: boolean;
  push_enabled: boolean;
  tipos_habilitados: TipoNotificacao[];
}

// Dados para processamento de notificação
export interface ProcessarNotificacaoData {
  tipo: TipoNotificacao;
  destinatario_id: string;
  contexto: ContextoAgendamento | ContextoProprietario;
  canal?: CanalNotificacao;
  agendamento_id?: number;
  empresa_id?: number;
}

// Resultado do processamento de notificação
export interface ResultadoNotificacao {
  success: boolean;
  notificacao_id?: number;
  error?: string;
  tentativas?: number;
}

// Dados para envio de SMS
export interface EnviarSMSData {
  to: string;
  message: string;
  from?: string;
}

// Resposta do envio de SMS
export interface SMSResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

// Dados para envio de Push
export interface EnviarPushData {
  token: string;
  title: string;
  body: string;
  data?: Record<string, string>;
  imageUrl?: string;
}

// Resposta do envio de Push
export interface PushResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

// Token de dispositivo para notificações push
export interface DeviceToken {
  id: number;
  user_id: string;
  token: string;
  platform: 'web' | 'android' | 'ios';
  created_at: string;
  updated_at: string;
  active: boolean;
}

// Preferências de notificação do usuário
export interface PreferenciasNotificacao {
  id: number;
  user_id: string;
  email_enabled: boolean;
  sms_enabled: boolean;
  push_enabled: boolean;
  tipos_habilitados: TipoNotificacao[];
  created_at: string;
  updated_at: string;
}
