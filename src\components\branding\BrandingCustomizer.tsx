'use client';

import React, { useState } from 'react';
import { useBranding, colorUtils } from '@/contexts/BrandingContext';
import { Button } from '@/components/ui/Button';

interface ColorInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  onPreview?: (value: string) => void;
  description?: string;
}

function ColorInput({ label, value, onChange, onPreview, description }: ColorInputProps) {
  const [inputValue, setInputValue] = useState(value);
  const [isValid, setIsValid] = useState(true);

  const handleChange = (newValue: string) => {
    setInputValue(newValue);
    const isValidColor = colorUtils.isValidHex(newValue);
    setIsValid(isValidColor);
    
    if (isValidColor) {
      onChange(newValue);
      onPreview?.(newValue);
    }
  };

  const handleBlur = () => {
    if (!isValid) {
      setInputValue(value);
      setIsValid(true);
    }
  };

  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-[var(--text-primary)]">
        {label}
      </label>
      <div className="flex items-center space-x-3">
        <div 
          className="w-10 h-10 rounded-lg border-2 border-[var(--border-color)] flex-shrink-0"
          style={{ backgroundColor: isValid ? inputValue : '#cccccc' }}
        />
        <div className="flex-1">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => handleChange(e.target.value)}
            onBlur={handleBlur}
            placeholder="#3B82F6"
            className={`w-full px-3 py-2 border rounded-md text-sm font-mono ${
              isValid 
                ? 'border-[var(--border-color)] focus:border-[var(--primary)] focus:ring-1 focus:ring-[var(--primary)]' 
                : 'border-[var(--error)] focus:border-[var(--error)] focus:ring-1 focus:ring-[var(--error)]'
            } bg-[var(--input-background)] text-[var(--text-primary)]`}
          />
          {!isValid && (
            <p className="text-xs text-[var(--error)] mt-1">
              Cor inválida. Use formato #RRGGBB
            </p>
          )}
        </div>
        <input
          type="color"
          value={isValid ? inputValue : '#cccccc'}
          onChange={(e) => handleChange(e.target.value)}
          className="w-10 h-10 rounded-lg border border-[var(--border-color)] cursor-pointer"
        />
      </div>
      {description && (
        <p className="text-xs text-[var(--text-secondary)]">{description}</p>
      )}
    </div>
  );
}

interface BrandingCustomizerProps {
  className?: string;
  showPreview?: boolean;
}

export function BrandingCustomizer({ className = '', showPreview = true }: BrandingCustomizerProps) {
  const { 
    branding, 
    applyBranding, 
    resetBranding, 
    previewBranding, 
    clearPreview, 
    isPreviewMode 
  } = useBranding();

  const [tempColors, setTempColors] = useState(branding.colors);
  const [hasChanges, setHasChanges] = useState(false);

  const handleColorChange = (colorKey: keyof typeof tempColors, value: string) => {
    const newColors = { ...tempColors, [colorKey]: value };
    
    // Gerar cor de hover automaticamente se for cor primária ou accent
    if (colorKey === 'primary') {
      newColors.primaryHover = colorUtils.generateHoverColor(value);
    } else if (colorKey === 'accent') {
      newColors.accentHover = colorUtils.generateHoverColor(value);
    }
    
    setTempColors(newColors);
    setHasChanges(true);
    
    if (showPreview) {
      previewBranding(newColors);
    }
  };

  const handleApply = () => {
    applyBranding(tempColors);
    setHasChanges(false);
  };

  const handleReset = () => {
    resetBranding();
    setTempColors(branding.colors);
    setHasChanges(false);
  };

  const handleCancel = () => {
    setTempColors(branding.colors);
    setHasChanges(false);
    if (isPreviewMode) {
      clearPreview();
    }
  };

  return (
    <div className={`bg-[var(--surface)] border border-[var(--border-color)] rounded-lg p-6 ${className}`}>
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-2">
          Personalização da Marca
        </h3>
        <p className="text-sm text-[var(--text-secondary)]">
          Customize as cores da sua empresa para criar uma identidade visual única.
        </p>
      </div>

      <div className="space-y-6">
        {/* Cor Primária */}
        <ColorInput
          label="Cor Primária"
          value={tempColors.primary}
          onChange={(value) => handleColorChange('primary', value)}
          onPreview={showPreview ? (value) => previewBranding({ primary: value, primaryHover: colorUtils.generateHoverColor(value) }) : undefined}
          description="Cor principal da sua marca (botões, links, destaques)"
        />

        {/* Cor Secundária */}
        <ColorInput
          label="Cor Secundária"
          value={tempColors.secondary}
          onChange={(value) => handleColorChange('secondary', value)}
          onPreview={showPreview ? (value) => previewBranding({ secondary: value }) : undefined}
          description="Cor para textos secundários e elementos de apoio"
        />

        {/* Cor de Destaque */}
        <ColorInput
          label="Cor de Destaque"
          value={tempColors.accent}
          onChange={(value) => handleColorChange('accent', value)}
          onPreview={showPreview ? (value) => previewBranding({ accent: value, accentHover: colorUtils.generateHoverColor(value) }) : undefined}
          description="Cor para promoções, ofertas especiais e chamadas de ação"
        />

        {/* Preview das cores de hover (somente leitura) */}
        <div className="grid grid-cols-2 gap-4 p-4 bg-[var(--surface-hover)] rounded-lg">
          <div>
            <label className="block text-xs font-medium text-[var(--text-secondary)] mb-2">
              Cor Primária (Hover)
            </label>
            <div className="flex items-center space-x-2">
              <div 
                className="w-6 h-6 rounded border border-[var(--border-color)]"
                style={{ backgroundColor: tempColors.primaryHover }}
              />
              <span className="text-xs font-mono text-[var(--text-secondary)]">
                {tempColors.primaryHover}
              </span>
            </div>
          </div>
          <div>
            <label className="block text-xs font-medium text-[var(--text-secondary)] mb-2">
              Cor de Destaque (Hover)
            </label>
            <div className="flex items-center space-x-2">
              <div 
                className="w-6 h-6 rounded border border-[var(--border-color)]"
                style={{ backgroundColor: tempColors.accentHover }}
              />
              <span className="text-xs font-mono text-[var(--text-secondary)]">
                {tempColors.accentHover}
              </span>
            </div>
          </div>
        </div>

        {/* Preview da aplicação */}
        {showPreview && (
          <div className="p-4 border border-[var(--border-color)] rounded-lg">
            <h4 className="text-sm font-medium text-[var(--text-primary)] mb-3">
              Preview da Aplicação
            </h4>
            <div className="space-y-3">
              <Button variant="primary" size="sm" disabled>
                Botão Primário
              </Button>
              <Button variant="outline" size="sm" disabled>
                Botão Secundário
              </Button>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-[var(--text-secondary)]">Link de exemplo:</span>
                <a 
                  href="#" 
                  className="text-sm text-[var(--primary)] hover:text-[var(--primary-hover)] transition-colors"
                  onClick={(e) => e.preventDefault()}
                >
                  Clique aqui
                </a>
              </div>
              <div 
                className="p-3 rounded-md text-sm"
                style={{ 
                  backgroundColor: `${tempColors.accent}20`,
                  color: tempColors.accent,
                  border: `1px solid ${tempColors.accent}40`
                }}
              >
                Exemplo de destaque com cor de accent
              </div>
            </div>
          </div>
        )}

        {/* Ações */}
        <div className="flex items-center justify-between pt-4 border-t border-[var(--border-color)]">
          <div className="flex space-x-2">
            <Button
              onClick={handleReset}
              variant="ghost"
              size="sm"
              className="text-[var(--text-secondary)]"
            >
              Restaurar Padrão
            </Button>
          </div>
          
          <div className="flex space-x-2">
            {hasChanges && (
              <Button
                onClick={handleCancel}
                variant="ghost"
                size="sm"
              >
                Cancelar
              </Button>
            )}
            <Button
              onClick={handleApply}
              variant="primary"
              size="sm"
              disabled={!hasChanges}
            >
              {isPreviewMode ? 'Aplicar Mudanças' : 'Salvar'}
            </Button>
          </div>
        </div>

        {/* Status */}
        {branding.isCustomized && (
          <div className="flex items-center space-x-2 text-sm text-[var(--success)]">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path 
                fillRule="evenodd" 
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" 
                clipRule="evenodd" 
              />
            </svg>
            <span>Personalização ativa</span>
          </div>
        )}
      </div>
    </div>
  );
}
