/**
 * Helpers centralizados para APIs
 * Padroniza validação, tratamento de erros e respostas
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { logger } from '@/services/LoggingService';

// Tipos para padronização de respostas
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  validationErrors?: Record<string, string>;
  timestamp?: string;
}

export interface ApiError extends Error {
  status?: number;
  validationErrors?: Record<string, string>;
}

// Configuração de validação para campos comuns
export const commonValidations = {
  email: (value: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      return 'Email inválido';
    }
  },
  
  phone: (value: string) => {
    const phoneRegex = /^\(\d{2}\)\s\d{4,5}-\d{4}$/;
    if (!phoneRegex.test(value)) {
      return 'Telefone deve estar no formato (11) 99999-9999';
    }
  },
  
  required: (value: any, fieldName: string) => {
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return `${fieldName} é obrigatório`;
    }
  },
  
  minLength: (value: string, min: number, fieldName: string) => {
    if (value && value.length < min) {
      return `${fieldName} deve ter pelo menos ${min} caracteres`;
    }
  },
  
  maxLength: (value: string, max: number, fieldName: string) => {
    if (value && value.length > max) {
      return `${fieldName} deve ter no máximo ${max} caracteres`;
    }
  },
  
  positiveNumber: (value: number, fieldName: string) => {
    if (value <= 0) {
      return `${fieldName} deve ser um número positivo`;
    }
  },
  
  range: (value: number, min: number, max: number, fieldName: string) => {
    if (value < min || value > max) {
      return `${fieldName} deve estar entre ${min} e ${max}`;
    }
  }
};

// Schema de validação
export interface ValidationRule {
  required?: boolean;
  type?: 'string' | 'number' | 'email' | 'phone';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  custom?: (value: any) => string | undefined;
}

export interface ValidationSchema {
  [fieldName: string]: ValidationRule;
}

// Função de validação centralizada
export function validateData(data: any, schema: ValidationSchema): {
  isValid: boolean;
  errors: Record<string, string>;
} {
  const errors: Record<string, string> = {};

  Object.entries(schema).forEach(([fieldName, rules]) => {
    const value = data[fieldName];

    // Validação de campo obrigatório
    if (rules.required) {
      const requiredError = commonValidations.required(value, fieldName);
      if (requiredError) {
        errors[fieldName] = requiredError;
        return;
      }
    }

    // Se campo não é obrigatório e está vazio, pular outras validações
    if (!value && !rules.required) {
      return;
    }

    // Validação de tipo
    if (rules.type) {
      switch (rules.type) {
        case 'email':
          const emailError = commonValidations.email(value);
          if (emailError) errors[fieldName] = emailError;
          break;
        case 'phone':
          const phoneError = commonValidations.phone(value);
          if (phoneError) errors[fieldName] = phoneError;
          break;
        case 'number':
          if (isNaN(Number(value))) {
            errors[fieldName] = `${fieldName} deve ser um número`;
          }
          break;
      }
    }

    // Validação de comprimento
    if (rules.minLength && typeof value === 'string') {
      const minError = commonValidations.minLength(value, rules.minLength, fieldName);
      if (minError) errors[fieldName] = minError;
    }

    if (rules.maxLength && typeof value === 'string') {
      const maxError = commonValidations.maxLength(value, rules.maxLength, fieldName);
      if (maxError) errors[fieldName] = maxError;
    }

    // Validação de range numérico
    if (rules.min !== undefined || rules.max !== undefined) {
      const numValue = Number(value);
      if (rules.min !== undefined && numValue < rules.min) {
        errors[fieldName] = `${fieldName} deve ser pelo menos ${rules.min}`;
      }
      if (rules.max !== undefined && numValue > rules.max) {
        errors[fieldName] = `${fieldName} deve ser no máximo ${rules.max}`;
      }
    }

    // Validação customizada
    if (rules.custom) {
      const customError = rules.custom(value);
      if (customError) {
        errors[fieldName] = customError;
      }
    }
  });

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

// Wrapper para APIs com validação automática
export function withValidation(
  schema: ValidationSchema,
  handler: (request: NextRequest, validatedData: any) => Promise<NextResponse>
) {
  return async (request: NextRequest) => {
    try {
      // Verificar se é método que precisa de validação
      if (!['POST', 'PUT', 'PATCH'].includes(request.method)) {
        return handler(request, null);
      }

      // Extrair dados do body
      let body;
      try {
        body = await request.json();
      } catch (error) {
        return NextResponse.json(
          {
            success: false,
            error: 'Formato de dados inválido',
            timestamp: new Date().toISOString()
          } as ApiResponse,
          { status: 400 }
        );
      }

      // Validar dados
      const validation = validateData(body, schema);
      if (!validation.isValid) {
        return NextResponse.json(
          {
            success: false,
            error: 'Dados de entrada inválidos',
            validationErrors: validation.errors,
            timestamp: new Date().toISOString()
          } as ApiResponse,
          { status: 400 }
        );
      }

      // Chamar handler com dados validados
      return handler(request, body);

    } catch (error) {
      logger.error('Erro no middleware de validação', error as Error);
      return NextResponse.json(
        {
          success: false,
          error: 'Erro interno do servidor',
          timestamp: new Date().toISOString()
        } as ApiResponse,
        { status: 500 }
      );
    }
  };
}

// Wrapper para APIs com autenticação
export function withAuth(
  handler: (request: NextRequest, user: any) => Promise<NextResponse>,
  requiredRoles?: string[]
) {
  return async (request: NextRequest) => {
    try {
      const supabase = await createClient();
      const { data: { user }, error } = await supabase.auth.getUser();

      if (error || !user) {
        return NextResponse.json(
          {
            success: false,
            error: 'Não autorizado',
            timestamp: new Date().toISOString()
          } as ApiResponse,
          { status: 401 }
        );
      }

      // Verificar papéis se especificado
      if (requiredRoles && requiredRoles.length > 0) {
        const userRole = user.user_metadata?.role;
        if (!requiredRoles.includes(userRole)) {
          return NextResponse.json(
            {
              success: false,
              error: 'Permissões insuficientes',
              timestamp: new Date().toISOString()
            } as ApiResponse,
            { status: 403 }
          );
        }
      }

      return handler(request, user);

    } catch (error) {
      logger.error('Erro no middleware de autenticação', error as Error);
      return NextResponse.json(
        {
          success: false,
          error: 'Erro interno do servidor',
          timestamp: new Date().toISOString()
        } as ApiResponse,
        { status: 500 }
      );
    }
  };
}

// Função para criar respostas padronizadas
export function createApiResponse<T>(
  success: boolean,
  data?: T,
  error?: string,
  validationErrors?: Record<string, string>
): ApiResponse<T> {
  return {
    success,
    data,
    error,
    validationErrors,
    timestamp: new Date().toISOString()
  };
}

// Função para tratar erros de API
export function handleApiError(error: any): NextResponse {
  logger.error('Erro na API', error);

  if (error.status) {
    return NextResponse.json(
      createApiResponse(false, undefined, error.message, error.validationErrors),
      { status: error.status }
    );
  }

  return NextResponse.json(
    createApiResponse(false, undefined, 'Erro interno do servidor'),
    { status: 500 }
  );
}

// Schemas de validação comuns
export const commonSchemas = {
  createUser: {
    name: { required: true, type: 'string' as const, minLength: 2, maxLength: 100 },
    email: { required: true, type: 'email' as const },
    phone: { required: true, type: 'phone' as const }
  },
  
  createService: {
    nome_servico: { required: true, type: 'string' as const, minLength: 2, maxLength: 100 },
    descricao: { type: 'string' as const, maxLength: 500 },
    duracao_minutos: { required: true, type: 'number' as const, min: 15, max: 480 },
    preco: { required: true, type: 'number' as const, min: 0, max: 10000 }
  },
  
  createAgendamento: {
    empresa_id: { required: true, type: 'number' as const },
    servicos_ids: { required: true },
    data_hora_inicio: { required: true, type: 'string' as const },
    forma_pagamento: { required: true, type: 'string' as const }
  }
};
