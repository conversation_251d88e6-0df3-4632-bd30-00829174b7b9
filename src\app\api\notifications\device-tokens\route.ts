import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

/**
 * GET - Listar tokens de dispositivo do usuário
 */
export async function GET() {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'Usuário não autenticado'
      }, { status: 401 });
    }

    // Buscar tokens do usuário
    const { data: tokens, error } = await supabase
      .from('device_tokens')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erro ao buscar tokens:', error);
      return NextResponse.json({
        success: false,
        error: 'Erro ao buscar tokens'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: tokens
    });

  } catch (error) {
    console.error('Erro na API de tokens:', error);
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 });
  }
}

/**
 * POST - Registrar novo token de dispositivo
 */
export async function POST(request: Request) {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'Usuário não autenticado'
      }, { status: 401 });
    }

    const body = await request.json();
    const { token, platform } = body;

    // Validações
    if (!token || typeof token !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Token é obrigatório e deve ser uma string'
      }, { status: 400 });
    }

    if (!platform || !['web', 'android', 'ios'].includes(platform)) {
      return NextResponse.json({
        success: false,
        error: 'Platform deve ser web, android ou ios'
      }, { status: 400 });
    }

    // Validar formato do token (básico)
    if (token.length < 50) {
      return NextResponse.json({
        success: false,
        error: 'Token inválido'
      }, { status: 400 });
    }

    // Inserir ou atualizar token (upsert)
    const { data: deviceToken, error } = await supabase
      .from('device_tokens')
      .upsert({
        user_id: user.id,
        token,
        platform,
        active: true
      })
      .select()
      .single();

    if (error) {
      console.error('Erro ao registrar token:', error);
      return NextResponse.json({
        success: false,
        error: 'Erro ao registrar token'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: deviceToken,
      message: 'Token registrado com sucesso'
    });

  } catch (error) {
    console.error('Erro na API de tokens:', error);
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 });
  }
}

/**
 * DELETE - Remover token de dispositivo
 */
export async function DELETE(request: Request) {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'Usuário não autenticado'
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'Token é obrigatório'
      }, { status: 400 });
    }

    // Marcar token como inativo (soft delete)
    const { error } = await supabase
      .from('device_tokens')
      .update({ active: false })
      .eq('user_id', user.id)
      .eq('token', token);

    if (error) {
      console.error('Erro ao remover token:', error);
      return NextResponse.json({
        success: false,
        error: 'Erro ao remover token'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Token removido com sucesso'
    });

  } catch (error) {
    console.error('Erro na API de tokens:', error);
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 });
  }
}

/**
 * PATCH - Atualizar status de token
 */
export async function PATCH(request: Request) {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'Usuário não autenticado'
      }, { status: 401 });
    }

    const body = await request.json();
    const { token, active } = body;

    // Validações
    if (!token || typeof token !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Token é obrigatório'
      }, { status: 400 });
    }

    if (typeof active !== 'boolean') {
      return NextResponse.json({
        success: false,
        error: 'Active deve ser um boolean'
      }, { status: 400 });
    }

    // Atualizar status do token
    const { data: deviceToken, error } = await supabase
      .from('device_tokens')
      .update({ active })
      .eq('user_id', user.id)
      .eq('token', token)
      .select()
      .single();

    if (error) {
      console.error('Erro ao atualizar token:', error);
      return NextResponse.json({
        success: false,
        error: 'Erro ao atualizar token'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: deviceToken,
      message: `Token ${active ? 'ativado' : 'desativado'} com sucesso`
    });

  } catch (error) {
    console.error('Erro na API de tokens:', error);
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 });
  }
}
