import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { CriarCupomData } from '@/types/marketing';

// GET - Buscar cupom específico
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient();
    const { id } = await params;
    const cupomId = parseInt(id);

    if (isNaN(cupomId)) {
      return NextResponse.json({ success: false, error: 'ID inválido' }, { status: 400 });
    }

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ success: false, error: 'Não autorizado' }, { status: 401 });
    }

    // Buscar empresa do usuário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select('empresa_id')
      .eq('proprietario_user_id', user.id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json({ success: false, error: 'Empresa não encontrada' }, { status: 404 });
    }

    // Buscar cupom
    const { data: cupom, error: cupomError } = await supabase
      .from('cupons')
      .select('*')
      .eq('cupom_id', cupomId)
      .eq('empresa_id', empresa.empresa_id)
      .single();

    if (cupomError || !cupom) {
      return NextResponse.json({ success: false, error: 'Cupom não encontrado' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: cupom
    });

  } catch (error) {
    console.error('Erro ao buscar cupom:', error);
    return NextResponse.json({ success: false, error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// PATCH - Atualizar cupom
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient();
    const { id } = await params;
    const cupomId = parseInt(id);
    const body: Partial<CriarCupomData> = await request.json();

    if (isNaN(cupomId)) {
      return NextResponse.json({ success: false, error: 'ID inválido' }, { status: 400 });
    }

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ success: false, error: 'Não autorizado' }, { status: 401 });
    }

    // Buscar empresa do usuário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select('empresa_id, plano_saas_id, planos_saas(nome_plano)')
      .eq('proprietario_user_id', user.id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json({ success: false, error: 'Empresa não encontrada' }, { status: 404 });
    }

    // Verificar se é plano Premium
    const planoSaas = Array.isArray(empresa.planos_saas)
      ? empresa.planos_saas[0]
      : empresa.planos_saas;
    const planoNome = planoSaas?.nome_plano;
    
    if (planoNome?.toLowerCase() !== 'premium') {
      return NextResponse.json({ 
        success: false, 
        error: 'Módulo de marketing disponível apenas no plano Premium' 
      }, { status: 403 });
    }

    // Verificar se cupom existe e pertence à empresa
    const { data: cupomExistente, error: cupomExistenteError } = await supabase
      .from('cupons')
      .select('*')
      .eq('cupom_id', cupomId)
      .eq('empresa_id', empresa.empresa_id)
      .single();

    if (cupomExistenteError || !cupomExistente) {
      return NextResponse.json({ success: false, error: 'Cupom não encontrado' }, { status: 404 });
    }

    // Validações parciais
    const erros = validarDadosCupomParcial(body);
    if (Object.keys(erros).length > 0) {
      return NextResponse.json({ success: false, error: 'Dados inválidos', erros }, { status: 400 });
    }

    // Verificar se código do cupom já existe (se está sendo alterado)
    if (body.codigo_cupom && body.codigo_cupom !== cupomExistente.codigo_cupom) {
      const { data: codigoExistente } = await supabase
        .from('cupons')
        .select('cupom_id')
        .eq('codigo_cupom', body.codigo_cupom)
        .neq('cupom_id', cupomId)
        .single();

      if (codigoExistente) {
        return NextResponse.json({ 
          success: false, 
          error: 'Código do cupom já existe. Escolha outro código.' 
        }, { status: 400 });
      }
    }

    // Atualizar cupom
    const { data: cupomAtualizado, error: updateError } = await supabase
      .from('cupons')
      .update(body)
      .eq('cupom_id', cupomId)
      .eq('empresa_id', empresa.empresa_id)
      .select()
      .single();

    if (updateError) {
      console.error('Erro ao atualizar cupom:', updateError);
      return NextResponse.json({ success: false, error: 'Erro ao atualizar cupom' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: cupomAtualizado,
      message: 'Cupom atualizado com sucesso'
    });

  } catch (error) {
    console.error('Erro na atualização de cupom:', error);
    return NextResponse.json({ success: false, error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// DELETE - Excluir cupom
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient();
    const { id } = await params;
    const cupomId = parseInt(id);

    if (isNaN(cupomId)) {
      return NextResponse.json({ success: false, error: 'ID inválido' }, { status: 400 });
    }

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ success: false, error: 'Não autorizado' }, { status: 401 });
    }

    // Buscar empresa do usuário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select('empresa_id')
      .eq('proprietario_user_id', user.id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json({ success: false, error: 'Empresa não encontrada' }, { status: 404 });
    }

    // Verificar se cupom existe e pertence à empresa
    const { data: cupom, error: cupomError } = await supabase
      .from('cupons')
      .select('*')
      .eq('cupom_id', cupomId)
      .eq('empresa_id', empresa.empresa_id)
      .single();

    if (cupomError || !cupom) {
      return NextResponse.json({ success: false, error: 'Cupom não encontrado' }, { status: 404 });
    }

    // Verificar se cupom está sendo usado em campanhas ativas
    const { data: campanhasAtivas, error: campanhasError } = await supabase
      .from('campanhas_marketing')
      .select('campanha_id, nome_campanha')
      .eq('cupom_id', cupomId)
      .in('status', ['rascunho', 'agendada', 'enviando']);

    if (campanhasError) {
      console.error('Erro ao verificar campanhas:', campanhasError);
      return NextResponse.json({ success: false, error: 'Erro ao verificar dependências' }, { status: 500 });
    }

    if (campanhasAtivas && campanhasAtivas.length > 0) {
      return NextResponse.json({ 
        success: false, 
        error: `Cupom não pode ser excluído pois está sendo usado em ${campanhasAtivas.length} campanha(s) ativa(s)` 
      }, { status: 400 });
    }

    // Excluir cupom
    const { error: deleteError } = await supabase
      .from('cupons')
      .delete()
      .eq('cupom_id', cupomId)
      .eq('empresa_id', empresa.empresa_id);

    if (deleteError) {
      console.error('Erro ao excluir cupom:', deleteError);
      return NextResponse.json({ success: false, error: 'Erro ao excluir cupom' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Cupom excluído com sucesso'
    });

  } catch (error) {
    console.error('Erro na exclusão de cupom:', error);
    return NextResponse.json({ success: false, error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// Função de validação parcial
function validarDadosCupomParcial(dados: Partial<CriarCupomData>): Record<string, string> {
  const erros: Record<string, string> = {};

  if (dados.codigo_cupom !== undefined && (!dados.codigo_cupom || dados.codigo_cupom.length < 3)) {
    erros.codigo_cupom = 'Código deve ter pelo menos 3 caracteres';
  }

  if (dados.nome_cupom !== undefined && (!dados.nome_cupom || dados.nome_cupom.length < 3)) {
    erros.nome_cupom = 'Nome deve ter pelo menos 3 caracteres';
  }

  if (dados.descricao !== undefined && (!dados.descricao || dados.descricao.length < 10)) {
    erros.descricao = 'Descrição deve ter pelo menos 10 caracteres';
  }

  if (dados.tipo_desconto !== undefined && !['valor_fixo', 'percentual'].includes(dados.tipo_desconto)) {
    erros.tipo_desconto = 'Tipo de desconto inválido';
  }

  if (dados.valor_desconto !== undefined && dados.valor_desconto <= 0) {
    erros.valor_desconto = 'Valor do desconto deve ser maior que zero';
  }

  if (dados.tipo_desconto === 'percentual' && dados.valor_desconto !== undefined && dados.valor_desconto > 90) {
    erros.valor_desconto = 'Desconto percentual não pode ser maior que 90%';
  }

  if (dados.valor_minimo_pedido !== undefined && dados.valor_minimo_pedido < 0) {
    erros.valor_minimo_pedido = 'Valor mínimo não pode ser negativo';
  }

  if (dados.data_inicio && dados.data_fim && new Date(dados.data_fim) <= new Date(dados.data_inicio)) {
    erros.data_fim = 'Data de fim deve ser posterior à data de início';
  }

  if (dados.limite_usos_total !== undefined && dados.limite_usos_total <= 0) {
    erros.limite_usos_total = 'Limite de usos deve ser maior que zero';
  }

  if (dados.limite_usos_por_cliente !== undefined && dados.limite_usos_por_cliente <= 0) {
    erros.limite_usos_por_cliente = 'Limite por cliente deve ser maior que zero';
  }

  return erros;
}
