'use client';
import React from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';

interface Step {
  name: string;
  path: string;
  status: 'complete' | 'current' | 'upcoming';
}

export default function OnboardingProgress() {
  const pathname = usePathname();

  // Definir as etapas do onboarding
  const steps: Step[] = [
    {
      name: '<PERSON><PERSON> da Empresa',
      path: '/onboarding/registro-empresa',
      status: pathname === '/onboarding/registro-empresa' ? 'current' :
              pathname === '/onboarding/cadastro-servico' ||
              pathname === '/onboarding/definir-horario-comercial' ||
              pathname === '/onboarding/conclusao' ? 'complete' : 'upcoming'
    },
    {
      name: '<PERSON>adastro de Serviços',
      path: '/onboarding/cadastro-servico',
      status: pathname === '/onboarding/cadastro-servico' ? 'current' :
              pathname === '/onboarding/definir-horario-comercial' ||
              pathname === '/onboarding/conclus<PERSON>' ? 'complete' : 'upcoming'
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      path: '/onboarding/definir-horario-comercial',
      status: pathname === '/onboarding/definir-horario-comercial' ? 'current' :
              pathname === '/onboarding/conclusao' ? 'complete' : 'upcoming'
    },
    {
      name: 'Conclusão',
      path: '/onboarding/conclusao',
      status: pathname === '/onboarding/conclusao' ? 'current' : 'upcoming'
    },
  ];

  return (
    <nav aria-label="Progress" className="mb-8">
      <ol role="list" className="flex items-center">
        {steps.map((step, stepIdx) => (
          <li key={step.name} className={`relative ${stepIdx !== steps.length - 1 ? 'pr-8 sm:pr-20' : ''} ${stepIdx !== 0 ? 'pl-8 sm:pl-20' : ''}`}>
            {stepIdx !== steps.length - 1 ? (
              <div className="absolute inset-0 flex items-center" aria-hidden="true">
                <div className={`h-0.5 w-full ${step.status === 'complete' ? 'bg-[var(--primary)]' : 'bg-[var(--border-color)]'}`} />
              </div>
            ) : null}
            <div className="relative flex items-center justify-center">
              {step.status === 'complete' ? (
                <Link
                  href={step.path}
                  className="h-8 w-8 rounded-full bg-[var(--primary)] flex items-center justify-center"
                >
                  <svg className="h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="sr-only">{step.name}</span>
                </Link>
              ) : step.status === 'current' ? (
                <div className="h-8 w-8 rounded-full border-2 border-[var(--primary)] bg-surface flex items-center justify-center transition-[border-color] duration-300">
                  <span className="h-2.5 w-2.5 rounded-full bg-[var(--primary)]" aria-hidden="true" />
                  <span className="sr-only">{step.name}</span>
                </div>
              ) : (
                <div className="h-8 w-8 rounded-full border-2 border-[var(--border-color)] bg-surface hover:border-[var(--primary)] transition-[border-color] duration-300">
                  <span className="sr-only">{step.name}</span>
                </div>
              )}
            </div>
            <div className="absolute top-10 left-1/2 transform -translate-x-1/2">
              <span className={`text-xs sm:text-sm font-medium ${step.status === 'complete' || step.status === 'current' ? 'text-[var(--primary)]' : 'text-[var(--text-secondary)]'}`}>
                {step.name}
              </span>
            </div>
          </li>
        ))}
      </ol>
    </nav>
  );
}
