'use client';

import { useState } from 'react';
import { useCampanhas } from '@/hooks/useCampanhas';
import { FiltrosCampanhas } from '@/types/marketing';

export function GerenciamentoCampanhas() {
  const {
    campanhas,
    carregando,
    erro,
    total,
    paginaAtual,
    totalPaginas,
    buscarCampanhas,
    excluirCampanha,
    enviarCampanha,
    cancelarCampanha,
    limparErro
  } = useCampanhas();

  const [filtros, setFiltros] = useState<FiltrosCampanhas>({
    busca: '',
    status: 'todos',
    tipo_campanha: 'todos'
  });

  // Aplicar filtros
  const aplicarFiltros = () => {
    buscarCampanhas(filtros, 1);
  };

  // Limpar filtros
  const limparFiltros = () => {
    const filtrosLimpos: FiltrosCampanhas = {
      busca: '',
      status: 'todos',
      tipo_campanha: 'todos'
    };
    setFiltros(filtrosLimpos);
    buscarCampanhas(filtrosLimpos, 1);
  };

  // Excluir campanha
  const handleExcluirCampanha = async (id: number) => {
    if (window.confirm('Tem certeza que deseja excluir esta campanha?')) {
      await excluirCampanha(id);
    }
  };

  // Enviar campanha
  const handleEnviarCampanha = async (id: number) => {
    if (window.confirm('Tem certeza que deseja enviar esta campanha? Esta ação não pode ser desfeita.')) {
      await enviarCampanha(id);
    }
  };

  // Cancelar campanha
  const handleCancelarCampanha = async (id: number) => {
    if (window.confirm('Tem certeza que deseja cancelar esta campanha?')) {
      await cancelarCampanha(id);
    }
  };

  // Cor do status
  const getCorStatus = (status: string) => {
    switch (status) {
      case 'rascunho': return 'text-gray-600 bg-gray-100';
      case 'agendada': return 'text-blue-600 bg-blue-100';
      case 'enviando': return 'text-yellow-600 bg-yellow-100';
      case 'enviada': return 'text-green-600 bg-green-100';
      case 'cancelada': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Traduzir status
  const traduzirStatus = (status: string) => {
    switch (status) {
      case 'rascunho': return 'Rascunho';
      case 'agendada': return 'Agendada';
      case 'enviando': return 'Enviando';
      case 'enviada': return 'Enviada';
      case 'cancelada': return 'Cancelada';
      default: return status;
    }
  };

  // Traduzir tipo
  const traduzirTipo = (tipo: string) => {
    switch (tipo) {
      case 'email': return 'E-mail';
      case 'sms': return 'SMS';
      case 'ambos': return 'E-mail + SMS';
      default: return tipo;
    }
  };

  return (
    <div className="space-y-6">
      {/* Cabeçalho */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Campanhas de Marketing</h2>
          <p className="text-gray-600">Gerencie suas campanhas de e-mail e SMS</p>
        </div>
        <button
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          onClick={() => alert('Funcionalidade em desenvolvimento')}
        >
          Nova Campanha
        </button>
      </div>

      {/* Filtros */}
      <div className="bg-white p-4 rounded-lg shadow border">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Buscar
            </label>
            <input
              type="text"
              value={filtros.busca}
              onChange={(e) => setFiltros(prev => ({ ...prev, busca: e.target.value }))}
              placeholder="Nome da campanha"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={filtros.status}
              onChange={(e) => setFiltros(prev => ({ ...prev, status: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="todos">Todos</option>
              <option value="rascunho">Rascunho</option>
              <option value="agendada">Agendada</option>
              <option value="enviando">Enviando</option>
              <option value="enviada">Enviada</option>
              <option value="cancelada">Cancelada</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tipo
            </label>
            <select
              value={filtros.tipo_campanha}
              onChange={(e) => setFiltros(prev => ({ ...prev, tipo_campanha: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="todos">Todos</option>
              <option value="email">E-mail</option>
              <option value="sms">SMS</option>
              <option value="ambos">E-mail + SMS</option>
            </select>
          </div>

          <div className="flex items-end space-x-2">
            <button
              onClick={aplicarFiltros}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              Filtrar
            </button>
            <button
              onClick={limparFiltros}
              className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors"
            >
              Limpar
            </button>
          </div>
        </div>
      </div>

      {/* Mensagem de erro */}
      {erro && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex justify-between items-center">
            <p className="text-red-800">{erro}</p>
            <button
              onClick={limparErro}
              className="text-red-600 hover:text-red-800"
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* Lista de campanhas */}
      <div className="bg-white rounded-lg shadow border">
        {carregando ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Carregando campanhas...</p>
          </div>
        ) : campanhas.length === 0 ? (
          <div className="p-8 text-center">
            <div className="mb-4">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <p className="text-gray-500 text-lg">Nenhuma campanha encontrada</p>
            <p className="text-gray-400 text-sm mt-1">Crie sua primeira campanha de marketing para engajar seus clientes</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Campanha
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tipo
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Destinatários
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Data
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ações
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {campanhas.map((campanha) => (
                  <tr key={campanha.campanha_id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {campanha.nome_campanha}
                        </div>
                        <div className="text-sm text-gray-500">
                          {campanha.descricao.length > 50 
                            ? `${campanha.descricao.substring(0, 50)}...`
                            : campanha.descricao
                          }
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {traduzirTipo(campanha.tipo_campanha)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCorStatus(campanha.status)}`}>
                        {traduzirStatus(campanha.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {campanha.total_destinatarios}
                        {campanha.total_enviados > 0 && (
                          <span className="text-gray-500"> ({campanha.total_enviados} enviados)</span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {campanha.data_envio 
                          ? new Date(campanha.data_envio).toLocaleDateString()
                          : campanha.data_agendamento
                          ? new Date(campanha.data_agendamento).toLocaleDateString()
                          : new Date(campanha.created_at).toLocaleDateString()
                        }
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      {campanha.status === 'rascunho' && (
                        <>
                          <button
                            onClick={() => alert('Funcionalidade em desenvolvimento')}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            Editar
                          </button>
                          <button
                            onClick={() => handleEnviarCampanha(campanha.campanha_id)}
                            className="text-green-600 hover:text-green-900"
                          >
                            Enviar
                          </button>
                        </>
                      )}
                      
                      {campanha.status === 'agendada' && (
                        <button
                          onClick={() => handleCancelarCampanha(campanha.campanha_id)}
                          className="text-yellow-600 hover:text-yellow-900"
                        >
                          Cancelar
                        </button>
                      )}

                      {['rascunho', 'cancelada'].includes(campanha.status) && (
                        <button
                          onClick={() => handleExcluirCampanha(campanha.campanha_id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          Excluir
                        </button>
                      )}

                      <button
                        onClick={() => alert('Funcionalidade em desenvolvimento')}
                        className="text-gray-600 hover:text-gray-900"
                      >
                        Ver Detalhes
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Paginação */}
        {totalPaginas > 1 && (
          <div className="px-6 py-3 border-t border-gray-200 flex justify-between items-center">
            <div className="text-sm text-gray-700">
              Mostrando {campanhas.length} de {total} campanhas
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => buscarCampanhas(filtros, paginaAtual - 1)}
                disabled={paginaAtual === 1}
                className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Anterior
              </button>
              <span className="px-3 py-1 text-sm text-gray-700">
                Página {paginaAtual} de {totalPaginas}
              </span>
              <button
                onClick={() => buscarCampanhas(filtros, paginaAtual + 1)}
                disabled={paginaAtual === totalPaginas}
                className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Próxima
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
