'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { HorarioDisponivel } from '@/types/disponibilidade';

interface SeletorHorarioProps {
  horariosDisponiveis: HorarioDisponivel[];
  horarioSelecionado?: {
    data_hora_inicio: string;
    data_hora_fim: string;
  };
  onSelecionarHorario: (horario: {
    data_hora_inicio: string;
    data_hora_fim: string;
  }) => void;
  loading?: boolean;
  colaboradorSelecionado?: {
    colaborador_user_id: string;
    name: string;
  };
}

export function SeletorHorario({
  horariosDisponiveis,
  horarioSelecionado,
  onSelecionarHorario,
  loading = false,
  colaboradorSelecionado
}: SeletorHorarioProps) {
  const [dataAtual, setDataAtual] = useState(new Date());
  const [visualizacao, setVisualizacao] = useState<'calendario' | 'lista'>('calendario');

  // Filtrar horários por colaborador se selecionado
  const horariosFiltrados = colaboradorSelecionado 
    ? horariosDisponiveis.filter(h => h.colaborador_user_id === colaboradorSelecionado.colaborador_user_id)
    : horariosDisponiveis;

  // Agrupar horários por data
  const horariosPorData = horariosFiltrados.reduce((grupos, horario) => {
    const data = horario.data_hora_inicio.split('T')[0];
    if (!grupos[data]) {
      grupos[data] = [];
    }
    grupos[data].push(horario);
    return grupos;
  }, {} as Record<string, HorarioDisponivel[]>);

  // Ordenar datas
  const datasOrdenadas = Object.keys(horariosPorData).sort();

  const formatarData = (dataStr: string) => {
    const data = new Date(dataStr + 'T00:00:00');
    const hoje = new Date();
    const amanha = new Date();
    amanha.setDate(hoje.getDate() + 1);

    if (data.toDateString() === hoje.toDateString()) {
      return 'Hoje';
    } else if (data.toDateString() === amanha.toDateString()) {
      return 'Amanhã';
    } else {
      return data.toLocaleDateString('pt-BR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long'
      });
    }
  };

  const formatarHorario = (dataHoraStr: string) => {
    const data = new Date(dataHoraStr);
    return data.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const obterProximosHorarios = (limite: number = 6) => {
    const agora = new Date();
    return horariosFiltrados
      .filter(horario => new Date(horario.data_hora_inicio) > agora)
      .sort((a, b) => new Date(a.data_hora_inicio).getTime() - new Date(b.data_hora_inicio).getTime())
      .slice(0, limite);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
          <p className="text-[var(--text-secondary)] mt-4">Verificando disponibilidade...</p>
        </div>
      </div>
    );
  }

  if (horariosFiltrados.length === 0) {
    return (
      <div className="space-y-4">
        <div>
          <h3 className="text-xl font-semibold text-[var(--text-primary)] mb-2">
            Selecione um Horário
          </h3>
          <p className="text-[var(--text-secondary)] text-sm">
            Escolha o melhor horário para seu agendamento
          </p>
        </div>

        <div className="text-center py-12">
          <svg className="mx-auto h-12 w-12 text-[var(--text-secondary)] mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="text-[var(--text-secondary)] text-lg">Nenhum horário disponível</p>
          <p className="text-[var(--text-secondary)] text-sm mt-2">
            {colaboradorSelecionado 
              ? `Não há horários disponíveis para ${colaboradorSelecionado.name} nos próximos dias`
              : 'Não há horários disponíveis nos próximos dias'
            }
          </p>
          <Button 
            variant="outline" 
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Tentar Novamente
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header com controles */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h3 className="text-xl font-semibold text-[var(--text-primary)]">
            Selecione um Horário
          </h3>
          <p className="text-[var(--text-secondary)] text-sm">
            {colaboradorSelecionado 
              ? `Horários disponíveis com ${colaboradorSelecionado.name}`
              : 'Horários disponíveis com qualquer profissional'
            }
          </p>
        </div>

        {/* Toggle de visualização */}
        <div className="flex bg-[var(--surface)] rounded-lg p-1">
          <button
            onClick={() => setVisualizacao('calendario')}
            className={`px-3 py-1 rounded text-sm transition-colors ${
              visualizacao === 'calendario'
                ? 'bg-[var(--primary)] text-white'
                : 'text-[var(--text-secondary)] hover:text-[var(--text-primary)]'
            }`}
          >
            Por Data
          </button>
          <button
            onClick={() => setVisualizacao('lista')}
            className={`px-3 py-1 rounded text-sm transition-colors ${
              visualizacao === 'lista'
                ? 'bg-[var(--primary)] text-white'
                : 'text-[var(--text-secondary)] hover:text-[var(--text-primary)]'
            }`}
          >
            Próximos
          </button>
        </div>
      </div>

      {/* Visualização por data */}
      {visualizacao === 'calendario' && (
        <div className="space-y-4">
          {datasOrdenadas.map((data) => {
            const horariosData = horariosPorData[data].sort((a, b) => 
              new Date(a.data_hora_inicio).getTime() - new Date(b.data_hora_inicio).getTime()
            );

            return (
              <Card key={data}>
                <div className="p-4 bg-[var(--surface)] border-b">
                  <h4 className="font-medium text-[var(--text-primary)]">
                    {formatarData(data)} ({horariosData.length} horários)
                  </h4>
                  <p className="text-sm text-[var(--text-secondary)]">
                    {new Date(data + 'T00:00:00').toLocaleDateString('pt-BR', {
                      day: 'numeric',
                      month: 'long',
                      year: 'numeric'
                    })}
                  </p>
                </div>
                <CardContent className="p-4">
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                    {horariosData.map((horario, index) => (
                      <button
                        key={index}
                        onClick={() => onSelecionarHorario({
                          data_hora_inicio: horario.data_hora_inicio,
                          data_hora_fim: horario.data_hora_fim
                        })}
                        className={`p-3 rounded-lg border-2 text-center transition-all ${
                          horarioSelecionado?.data_hora_inicio === horario.data_hora_inicio
                            ? 'border-[var(--primary)] bg-[var(--primary-light)] text-[var(--primary)]'
                            : 'border-[var(--border)] hover:border-[var(--primary-light)] hover:bg-[var(--surface-hover)]'
                        }`}
                      >
                        <div className="font-medium">
                          {formatarHorario(horario.data_hora_inicio)}
                        </div>
                        {!colaboradorSelecionado && horario.colaborador_nome && (
                          <div className="text-xs text-[var(--text-secondary)] mt-1">
                            {horario.colaborador_nome}
                          </div>
                        )}
                      </button>
                    ))}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Visualização em lista - próximos horários */}
      {visualizacao === 'lista' && (
        <div className="space-y-3">
          <h4 className="text-lg font-medium text-[var(--text-primary)]">
            Próximos Horários Disponíveis
          </h4>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {obterProximosHorarios(12).map((horario, index) => (
              <Card
                key={index}
                className={`cursor-pointer transition-all hover:shadow-lg ${
                  horarioSelecionado?.data_hora_inicio === horario.data_hora_inicio
                    ? 'ring-2 ring-[var(--primary)] bg-[var(--primary-light)]'
                    : 'hover:ring-1 hover:ring-[var(--primary-light)]'
                }`}
                onClick={() => onSelecionarHorario({
                  data_hora_inicio: horario.data_hora_inicio,
                  data_hora_fim: horario.data_hora_fim
                })}
              >
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="font-medium text-[var(--text-primary)]">
                        {formatarHorario(horario.data_hora_inicio)}
                      </div>
                      <div className="text-sm text-[var(--text-secondary)]">
                        {formatarData(horario.data_hora_inicio.split('T')[0])}
                      </div>
                      {!colaboradorSelecionado && horario.colaborador_nome && (
                        <div className="text-xs text-[var(--text-secondary)] mt-1">
                          com {horario.colaborador_nome}
                        </div>
                      )}
                    </div>
                    
                    {horarioSelecionado?.data_hora_inicio === horario.data_hora_inicio && (
                      <div className="w-6 h-6 bg-[var(--primary)] rounded-full flex items-center justify-center">
                        <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Horário selecionado - resumo */}
      {horarioSelecionado && (
        <Card className="bg-[var(--primary-light)] border-[var(--primary)]">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-[var(--primary)] rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-[var(--text-primary)]">
                  Horário Selecionado: {formatarHorario(horarioSelecionado.data_hora_inicio)}
                </h4>
                <p className="text-sm text-[var(--text-secondary)]">
                  {formatarData(horarioSelecionado.data_hora_inicio.split('T')[0])} • 
                  {new Date(horarioSelecionado.data_hora_inicio).toLocaleDateString('pt-BR', {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric'
                  })}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
