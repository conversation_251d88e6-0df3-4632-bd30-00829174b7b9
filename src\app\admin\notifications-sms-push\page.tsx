'use client';

import { useState, useEffect } from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import TestNotificationsSMSPush from '@/components/notifications/TestNotificationsSMSPush';
import NotificationPreferences from '@/components/notifications/NotificationPreferences';

interface ServiceStatus {
  twilio_configured: boolean;
  firebase_configured: boolean;
  service_status: string;
  service_error?: string;
  environment_variables: {
    TWILIO_ACCOUNT_SID: boolean;
    TWILIO_AUTH_TOKEN: boolean;
    TWILIO_PHONE_NUMBER: boolean;
    FIREBASE_PROJECT_ID: boolean;
    FIREBASE_PRIVATE_KEY: boolean;
    FIREBASE_CLIENT_EMAIL: boolean;
  };
}

export default function AdminNotificationsSMSPushPage() {
  const [serviceStatus, setServiceStatus] = useState<ServiceStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'test' | 'preferences' | 'status'>('test');

  useEffect(() => {
    loadServiceStatus();
  }, []);

  const loadServiceStatus = async () => {
    try {
      setLoading(true);

      // Verificar status do SMS
      const smsResponse = await fetch('/api/notifications/test-sms');
      const smsResult = await smsResponse.json();

      // Verificar status do Firebase (simulado)
      const firebaseConfigured = !!(
        process.env.NEXT_PUBLIC_FIREBASE_CONFIG &&
        process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY
      );

      setServiceStatus({
        twilio_configured: smsResult.data?.twilio_configured || false,
        firebase_configured: firebaseConfigured,
        service_status: smsResult.data?.service_status || 'unknown',
        service_error: smsResult.data?.service_error,
        environment_variables: {
          ...smsResult.data?.environment_variables,
          FIREBASE_PROJECT_ID: !!process.env.FIREBASE_PROJECT_ID,
          FIREBASE_PRIVATE_KEY: !!process.env.FIREBASE_PRIVATE_KEY,
          FIREBASE_CLIENT_EMAIL: !!process.env.FIREBASE_CLIENT_EMAIL
        }
      });

    } catch (error) {
      console.error('Erro ao carregar status dos serviços:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'configured':
        return 'text-green-600 bg-green-100';
      case 'not_configured':
        return 'text-yellow-600 bg-yellow-100';
      case 'error':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'configured':
        return 'Configurado';
      case 'not_configured':
        return 'Não Configurado';
      case 'error':
        return 'Erro';
      default:
        return 'Desconhecido';
    }
  };

  return (
    <ProtectedRoute requiredRole={['Administrador']}>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              Notificações SMS e Push
            </h1>
            <p className="mt-2 text-gray-600">
              Configuração e teste de notificações por SMS e Push
            </p>
          </div>

          {/* Tabs */}
          <div className="mb-6">
            <nav className="flex space-x-8">
              <button
                onClick={() => setActiveTab('test')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'test'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                🧪 Testes
              </button>
              <button
                onClick={() => setActiveTab('preferences')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'preferences'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                ⚙️ Preferências
              </button>
              <button
                onClick={() => setActiveTab('status')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'status'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                📊 Status dos Serviços
              </button>
            </nav>
          </div>

          {/* Content */}
          {activeTab === 'test' && (
            <TestNotificationsSMSPush />
          )}

          {activeTab === 'preferences' && (
            <NotificationPreferences />
          )}

          {activeTab === 'status' && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">
                Status dos Serviços de Notificação
              </h3>

              {loading ? (
                <div className="animate-pulse space-y-4">
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                </div>
              ) : serviceStatus ? (
                <div className="space-y-6">
                  {/* Status Geral */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-2">📱 SMS (Twilio)</h4>
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(serviceStatus.service_status)}`}>
                          {getStatusText(serviceStatus.service_status)}
                        </span>
                      </div>
                      {serviceStatus.service_error && (
                        <p className="mt-2 text-sm text-red-600">{serviceStatus.service_error}</p>
                      )}
                    </div>

                    <div className="p-4 border rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-2">🔔 Push (Firebase)</h4>
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          serviceStatus.firebase_configured ? 'text-green-600 bg-green-100' : 'text-yellow-600 bg-yellow-100'
                        }`}>
                          {serviceStatus.firebase_configured ? 'Configurado' : 'Não Configurado'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Variáveis de Ambiente */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Variáveis de Ambiente</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h5 className="text-sm font-medium text-gray-700 mb-2">Twilio (SMS)</h5>
                        <div className="space-y-1">
                          {Object.entries({
                            'TWILIO_ACCOUNT_SID': serviceStatus.environment_variables.TWILIO_ACCOUNT_SID,
                            'TWILIO_AUTH_TOKEN': serviceStatus.environment_variables.TWILIO_AUTH_TOKEN,
                            'TWILIO_PHONE_NUMBER': serviceStatus.environment_variables.TWILIO_PHONE_NUMBER
                          }).map(([key, value]) => (
                            <div key={key} className="flex items-center justify-between text-sm">
                              <span className="text-gray-600">{key}</span>
                              <span className={`px-2 py-1 rounded text-xs ${
                                value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                              }`}>
                                {value ? '✅ Configurada' : '❌ Não configurada'}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h5 className="text-sm font-medium text-gray-700 mb-2">Firebase (Push)</h5>
                        <div className="space-y-1">
                          {Object.entries({
                            'FIREBASE_PROJECT_ID': serviceStatus.environment_variables.FIREBASE_PROJECT_ID,
                            'FIREBASE_PRIVATE_KEY': serviceStatus.environment_variables.FIREBASE_PRIVATE_KEY,
                            'FIREBASE_CLIENT_EMAIL': serviceStatus.environment_variables.FIREBASE_CLIENT_EMAIL
                          }).map(([key, value]) => (
                            <div key={key} className="flex items-center justify-between text-sm">
                              <span className="text-gray-600">{key}</span>
                              <span className={`px-2 py-1 rounded text-xs ${
                                value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                              }`}>
                                {value ? '✅ Configurada' : '❌ Não configurada'}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Instruções de Configuração */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="font-medium text-blue-900 mb-2">📋 Instruções de Configuração</h4>
                    <div className="text-sm text-blue-800 space-y-2">
                      <p><strong>Para SMS (Twilio):</strong></p>
                      <ul className="list-disc list-inside ml-4 space-y-1">
                        <li>Crie uma conta no Twilio</li>
                        <li>Configure TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN e TWILIO_PHONE_NUMBER</li>
                        <li>Verifique se o número está ativo e pode enviar SMS</li>
                      </ul>
                      
                      <p className="mt-3"><strong>Para Push (Firebase):</strong></p>
                      <ul className="list-disc list-inside ml-4 space-y-1">
                        <li>Crie um projeto no Firebase Console</li>
                        <li>Configure Firebase Cloud Messaging</li>
                        <li>Configure as variáveis FIREBASE_PROJECT_ID, FIREBASE_PRIVATE_KEY e FIREBASE_CLIENT_EMAIL</li>
                        <li>Configure NEXT_PUBLIC_FIREBASE_VAPID_KEY para web push</li>
                      </ul>
                    </div>
                  </div>

                  {/* Botão de Refresh */}
                  <div className="flex justify-end">
                    <button
                      onClick={loadServiceStatus}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      🔄 Atualizar Status
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-center text-gray-500">
                  Erro ao carregar status dos serviços
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </ProtectedRoute>
  );
}
