import { NextRequest, NextResponse } from 'next/server';
import { createSecureApiHandler, withSecurityPreset } from '@/utils/security/apiMiddleware';
import { AuditLogger } from '@/utils/security/audit';
import { logger } from '@/services/LoggingService';

/**
 * API para métricas de performance e monitoramento
 * Endpoint: GET/POST /api/monitoring/metrics
 */

interface PerformanceMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  timestamp: string;
  tags?: Record<string, string>;
}

interface SystemMetrics {
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  cpu: {
    usage: number;
  };
  requests: {
    total: number;
    errors: number;
    averageResponseTime: number;
  };
  database: {
    connections: number;
    queryTime: number;
  };
}

// Armazenamento em memória para métricas (em produção, usar Redis ou banco)
const metricsStore: PerformanceMetric[] = [];
const requestMetrics = {
  total: 0,
  errors: 0,
  responseTimes: [] as number[]
};

// GET - Buscar métricas
export const GET = createSecureApiHandler(
  async (request: NextRequest, { user, securityContext }) => {
    try {
      const { searchParams } = new URL(request.url);
      const metricType = searchParams.get('type');
      const startDate = searchParams.get('startDate');
      const endDate = searchParams.get('endDate');
      const interval = searchParams.get('interval') || '1h';

      // Log do acesso às métricas
      AuditLogger.adminAction(
        user.id,
        'VIEW_PERFORMANCE_METRICS',
        'performance_metrics',
        undefined,
        true
      );

      // Coletar métricas do sistema
      const systemMetrics = await collectSystemMetrics();

      // Filtrar métricas por tipo se especificado
      let filteredMetrics = metricsStore;
      if (metricType) {
        filteredMetrics = metricsStore.filter(metric => 
          metric.name.includes(metricType)
        );
      }

      // Filtrar por período se especificado
      if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        filteredMetrics = filteredMetrics.filter(metric => {
          const metricDate = new Date(metric.timestamp);
          return metricDate >= start && metricDate <= end;
        });
      }

      // Agrupar métricas por intervalo
      const groupedMetrics = groupMetricsByInterval(filteredMetrics, interval);

      // Calcular estatísticas
      const stats = calculateMetricsStats(filteredMetrics);

      return NextResponse.json({
        success: true,
        data: {
          systemMetrics,
          historicalMetrics: groupedMetrics,
          stats,
          filters: {
            type: metricType,
            startDate,
            endDate,
            interval
          },
          collectedAt: new Date().toISOString()
        }
      });

    } catch (error: any) {
      logger.error('Erro ao buscar métricas', error, {
        userId: user.id,
        endpoint: '/api/monitoring/metrics'
      });

      return NextResponse.json(
        { success: false, error: 'Erro interno do servidor' },
        { status: 500 }
      );
    }
  },
  withSecurityPreset('admin', {
    auditAction: 'VIEW_METRICS',
    auditResource: 'performance_metrics'
  }) as any
);

// POST - Registrar métricas ou configurar alertas
export const POST = createSecureApiHandler(
  async (request: NextRequest, { user, securityContext }) => {
    try {
      const body = await request.json();
      const { action, ...data } = body;

      switch (action) {
        case 'record_metric':
          // Registrar métrica personalizada
          const { name, value, unit, tags } = data;
          
          if (!name || value === undefined) {
            return NextResponse.json(
              { success: false, error: 'Nome e valor são obrigatórios' },
              { status: 400 }
            );
          }

          const metric: PerformanceMetric = {
            id: generateMetricId(),
            name,
            value: parseFloat(value),
            unit: unit || 'count',
            timestamp: new Date().toISOString(),
            tags: tags || {}
          };

          metricsStore.push(metric);

          // Manter apenas as últimas 10000 métricas
          if (metricsStore.length > 10000) {
            metricsStore.splice(0, metricsStore.length - 10000);
          }

          logger.info('Métrica registrada', {
            metric,
            userId: user.id
          });

          return NextResponse.json({
            success: true,
            message: 'Métrica registrada com sucesso',
            data: { metric }
          });

        case 'record_request':
          // Registrar métrica de requisição
          const { responseTime, statusCode, endpoint } = data;
          
          requestMetrics.total++;
          if (statusCode >= 400) {
            requestMetrics.errors++;
          }
          
          if (responseTime) {
            requestMetrics.responseTimes.push(responseTime);
            // Manter apenas os últimos 1000 tempos de resposta
            if (requestMetrics.responseTimes.length > 1000) {
              requestMetrics.responseTimes.shift();
            }
          }

          // Registrar métrica detalhada
          const requestMetric: PerformanceMetric = {
            id: generateMetricId(),
            name: 'api_request',
            value: responseTime || 0,
            unit: 'ms',
            timestamp: new Date().toISOString(),
            tags: {
              endpoint: endpoint || 'unknown',
              statusCode: statusCode?.toString() || 'unknown',
              success: statusCode < 400 ? 'true' : 'false'
            }
          };

          metricsStore.push(requestMetric);

          return NextResponse.json({
            success: true,
            message: 'Métrica de requisição registrada'
          });

        case 'clear_metrics':
          // Limpar métricas (apenas desenvolvimento)
          if (process.env.NODE_ENV !== 'development') {
            return NextResponse.json(
              { success: false, error: 'Operação não permitida em produção' },
              { status: 403 }
            );
          }

          metricsStore.length = 0;
          requestMetrics.total = 0;
          requestMetrics.errors = 0;
          requestMetrics.responseTimes.length = 0;

          AuditLogger.adminAction(
            user.id,
            'CLEAR_PERFORMANCE_METRICS',
            'performance_metrics',
            undefined,
            true
          );

          return NextResponse.json({
            success: true,
            message: 'Métricas limpas com sucesso'
          });

        default:
          return NextResponse.json(
            { success: false, error: 'Ação não reconhecida' },
            { status: 400 }
          );
      }

    } catch (error: any) {
      logger.error('Erro na API de métricas', error, {
        userId: user.id,
        endpoint: '/api/monitoring/metrics'
      });

      return NextResponse.json(
        { success: false, error: 'Erro interno do servidor' },
        { status: 500 }
      );
    }
  },
  withSecurityPreset('admin', {
    auditAction: 'MANAGE_METRICS',
    auditResource: 'performance_metrics'
  }) as any
);

// Funções auxiliares

async function collectSystemMetrics(): Promise<SystemMetrics> {
  try {
    // Coletar métricas do sistema (Node.js)
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    // Calcular média de tempo de resposta
    const averageResponseTime = requestMetrics.responseTimes.length > 0
      ? requestMetrics.responseTimes.reduce((a, b) => a + b, 0) / requestMetrics.responseTimes.length
      : 0;

    return {
      memory: {
        used: memoryUsage.heapUsed,
        total: memoryUsage.heapTotal,
        percentage: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100
      },
      cpu: {
        usage: (cpuUsage.user + cpuUsage.system) / 1000000 // Converter para ms
      },
      requests: {
        total: requestMetrics.total,
        errors: requestMetrics.errors,
        averageResponseTime
      },
      database: {
        connections: 0, // Implementar se necessário
        queryTime: 0    // Implementar se necessário
      }
    };
  } catch (error) {
    logger.error('Erro ao coletar métricas do sistema', error as Error);
    throw error;
  }
}

function groupMetricsByInterval(metrics: PerformanceMetric[], interval: string): Record<string, PerformanceMetric[]> {
  const grouped: Record<string, PerformanceMetric[]> = {};
  
  metrics.forEach(metric => {
    const date = new Date(metric.timestamp);
    let key: string;
    
    switch (interval) {
      case '1m':
        key = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}-${date.getHours()}-${date.getMinutes()}`;
        break;
      case '1h':
        key = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}-${date.getHours()}`;
        break;
      case '1d':
        key = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`;
        break;
      default:
        key = date.toISOString();
    }
    
    if (!grouped[key]) {
      grouped[key] = [];
    }
    grouped[key].push(metric);
  });
  
  return grouped;
}

function calculateMetricsStats(metrics: PerformanceMetric[]) {
  if (metrics.length === 0) {
    return {
      total: 0,
      average: 0,
      min: 0,
      max: 0,
      byName: {}
    };
  }

  const values = metrics.map(m => m.value);
  const byName: Record<string, { count: number; average: number }> = {};

  metrics.forEach(metric => {
    if (!byName[metric.name]) {
      byName[metric.name] = { count: 0, average: 0 };
    }
    byName[metric.name].count++;
    byName[metric.name].average = 
      (byName[metric.name].average * (byName[metric.name].count - 1) + metric.value) / byName[metric.name].count;
  });

  return {
    total: metrics.length,
    average: values.reduce((a, b) => a + b, 0) / values.length,
    min: Math.min(...values),
    max: Math.max(...values),
    byName
  };
}

function generateMetricId(): string {
  return `metric_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
