// Tipos para o sistema de testes de usuário

export interface CenarioTeste {
  cenario_id: number;
  nome_cenario: string;
  descricao: string;
  papel_usuario: 'Administrador' | 'Proprietario' | 'Colaborador' | 'Cliente';
  categoria: 'Onboarding' | 'Agendamento' | 'Gestao' | 'Pagamento' | 'Navegacao';
  dificuldade: 'Facil' | 'Medio' | 'Dificil';
  tempo_estimado_minutos: number;
  passos: PassoTeste[];
  criterios_sucesso: string[];
  metricas_alvo: MetricasAlvo;
  ativo: boolean;
  created_at: string;
  updated_at: string;
}

export interface PassoTeste {
  passo_numero: number;
  descricao: string;
  acao_esperada: string;
  resultado_esperado: string;
  url_inicial?: string;
  elementos_interacao?: string[];
  observacoes?: string;
}

export interface MetricasAlvo {
  tempo_maximo_minutos: number;
  taxa_sucesso_minima: number; // Percentual
  pontuacao_satisfacao_minima: number; // 1-10
  taxa_erro_maxima: number; // Percentual
}

export interface SessaoTeste {
  sessao_id: number;
  nome_sessao: string;
  descricao: string;
  data_inicio: string;
  data_fim: string;
  status: 'Planejada' | 'Em_Andamento' | 'Concluida' | 'Cancelada';
  cenarios_incluidos: number[]; // IDs dos cenários
  participantes_alvo: number;
  participantes_confirmados: number;
  moderador_user_id: string;
  observacoes_gerais?: string;
  created_at: string;
  updated_at: string;
}

export interface ParticipanteTeste {
  participante_id: number;
  sessao_id: number;
  user_id?: string; // Usuário logado (opcional)
  nome_participante: string;
  email: string;
  telefone?: string;
  papel_usuario: 'Administrador' | 'Proprietario' | 'Colaborador' | 'Cliente';
  experiencia_tecnologia: 'Baixa' | 'Media' | 'Alta';
  experiencia_agendamento: 'Nenhuma' | 'Pouca' | 'Moderada' | 'Muita';
  status_participacao: 'Convidado' | 'Confirmado' | 'Participou' | 'Ausente';
  data_convite: string;
  data_confirmacao?: string;
  observacoes?: string;
}

export interface ExecucaoTeste {
  execucao_id: number;
  sessao_id: number;
  participante_id: number;
  cenario_id: number;
  data_inicio: string;
  data_fim?: string;
  status: 'Iniciado' | 'Em_Progresso' | 'Concluido' | 'Abandonado' | 'Erro_Tecnico';
  tempo_total_segundos?: number;
  passos_completados: number;
  passos_total: number;
  erros_encontrados: number;
  ajuda_solicitada: number;
  sucesso_completo: boolean;
  observacoes_participante?: string;
  observacoes_moderador?: string;
  gravacao_url?: string;
}

export interface FeedbackTeste {
  feedback_id: number;
  execucao_id: number;
  participante_id: number;
  cenario_id: number;
  
  // Avaliações quantitativas (1-10)
  facilidade_uso: number;
  clareza_interface: number;
  velocidade_sistema: number;
  satisfacao_geral: number;
  probabilidade_recomendacao: number; // NPS
  
  // Feedback qualitativo
  aspectos_positivos?: string;
  aspectos_negativos?: string;
  sugestoes_melhoria?: string;
  funcionalidades_faltantes?: string;
  comentarios_gerais?: string;
  
  // Problemas específicos
  problemas_encontrados: ProblemaEncontrado[];
  
  created_at: string;
}

export interface ProblemaEncontrado {
  problema_id: number;
  tipo_problema: 'Interface' | 'Funcionalidade' | 'Performance' | 'Conteudo' | 'Navegacao';
  severidade: 'Baixa' | 'Media' | 'Alta' | 'Critica';
  descricao: string;
  url_problema?: string;
  elemento_problema?: string;
  acao_que_causou?: string;
  impacto_usuario: string;
  sugestao_correcao?: string;
}

export interface MetricasSessao {
  sessao_id: number;
  total_participantes: number;
  total_cenarios_testados: number;
  total_execucoes: number;
  
  // Métricas de sucesso
  taxa_sucesso_geral: number;
  tempo_medio_conclusao: number;
  pontuacao_satisfacao_media: number;
  nps_score: number;
  
  // Métricas de problemas
  total_problemas: number;
  problemas_por_severidade: {
    baixa: number;
    media: number;
    alta: number;
    critica: number;
  };
  
  // Métricas por cenário
  metricas_por_cenario: MetricasCenario[];
  
  // Métricas por papel de usuário
  metricas_por_papel: MetricasPapel[];
}

export interface MetricasCenario {
  cenario_id: number;
  nome_cenario: string;
  total_execucoes: number;
  taxa_sucesso: number;
  tempo_medio: number;
  pontuacao_media: number;
  problemas_encontrados: number;
  recomendacoes: string[];
}

export interface MetricasPapel {
  papel_usuario: string;
  total_participantes: number;
  taxa_sucesso_media: number;
  satisfacao_media: number;
  problemas_principais: string[];
}

// Filtros para consultas
export interface FiltrosSessoes {
  status?: string;
  data_inicio?: string;
  data_fim?: string;
  moderador?: string;
  papel_usuario?: string;
}

export interface FiltrosCenarios {
  papel_usuario?: string;
  categoria?: string;
  dificuldade?: string;
  ativo?: boolean;
}

export interface FiltrosParticipantes {
  sessao_id?: number;
  papel_usuario?: string;
  experiencia_tecnologia?: string;
  status_participacao?: string;
}

// Respostas da API
export interface TestesUsuarioApiResponse {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

// Templates de cenários predefinidos
export interface TemplateCenario {
  nome: string;
  descricao: string;
  papel_usuario: string;
  categoria: string;
  passos: Omit<PassoTeste, 'passo_numero'>[];
  criterios_sucesso: string[];
  tempo_estimado: number;
}

// Configurações do sistema de testes
export interface ConfiguracaoTestes {
  tempo_maximo_sessao_minutos: number;
  intervalo_entre_cenarios_minutos: number;
  gravacao_obrigatoria: boolean;
  feedback_obrigatorio: boolean;
  notificacoes_habilitadas: boolean;
  email_lembrete_dias: number;
}

// Estatísticas globais do sistema de testes
export interface EstatisticasGlobais {
  total_sessoes_realizadas: number;
  total_participantes_unicos: number;
  total_cenarios_testados: number;
  taxa_sucesso_geral: number;
  nps_medio: number;
  problemas_criticos_abertos: number;
  melhorias_implementadas: number;
  tempo_medio_por_cenario: number;
}
