import { useState, useEffect, useCallback, useMemo } from 'react';
import { FiltrosBusca, ResultadoBusca, BuscaApiResponse, EstatisticasBusca } from '@/types/busca';

interface UseBuscaEmpresasReturn {
  // Estado dos dados
  resultado: ResultadoBusca | null;
  estatisticas: EstatisticasBusca | null;
  loading: boolean;
  error: string | null;
  
  // Controle de filtros
  filtros: FiltrosBusca;
  setFiltros: (filtros: FiltrosBusca) => void;
  limparFiltros: () => void;
  
  // Ações
  buscar: (novosFiltros?: Partial<FiltrosBusca>) => Promise<void>;
  carregarMais: () => Promise<void>;
  
  // Utilitários
  temFiltrosAtivos: boolean;
  podeCarregarMais: boolean;
}

const filtrosIniciais: FiltrosBusca = {
  termo: '',
  cidade: '',
  estado: '',
  bairro: '',
  categorias_servicos: [],
  preco_minimo: undefined,
  preco_maximo: undefined,
  ordenacao: 'relevancia',
  pagina: 1,
  limite: 12
};

export function useBuscaEmpresas(): UseBuscaEmpresasReturn {
  const [resultado, setResultado] = useState<ResultadoBusca | null>(null);
  const [estatisticas, setEstatisticas] = useState<EstatisticasBusca | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filtros, setFiltrosState] = useState<FiltrosBusca>(filtrosIniciais);

  // Carregar estatísticas iniciais
  useEffect(() => {
    carregarEstatisticas();
  }, []);

  // Função para carregar estatísticas
  const carregarEstatisticas = async () => {
    try {
      const response = await fetch('/api/empresas/estatisticas-busca');
      const data = await response.json();
      
      if (data.success) {
        setEstatisticas(data.data);
      }
    } catch (err) {
      console.error('Erro ao carregar estatísticas:', err);
    }
  };

  // Função principal de busca
  const buscar = useCallback(async (novosFiltros?: Partial<FiltrosBusca>) => {
    setLoading(true);
    setError(null);

    try {
      const filtrosParaBusca = novosFiltros ? { ...filtros, ...novosFiltros } : filtros;
      
      // Construir parâmetros da URL
      const params = new URLSearchParams();
      
      Object.entries(filtrosParaBusca).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            if (value.length > 0) {
              params.append(key, value.join(','));
            }
          } else {
            params.append(key, value.toString());
          }
        }
      });

      const response = await fetch(`/api/empresas/buscar?${params.toString()}`);
      const data: BuscaApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao buscar empresas');
      }

      if (data.success && data.data) {
        setResultado(data.data);
        
        // Atualizar filtros se foram passados novos
        if (novosFiltros) {
          setFiltrosState(filtrosParaBusca);
        }
      } else {
        throw new Error('Formato de resposta inválido');
      }

    } catch (err: any) {
      console.error('Erro na busca:', err);
      setError(err.message || 'Erro ao buscar empresas');
      setResultado(null);
    } finally {
      setLoading(false);
    }
  }, [filtros]);

  // Função para carregar mais resultados (paginação)
  const carregarMais = useCallback(async () => {
    if (!resultado || !resultado.tem_proxima_pagina || loading) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const novosFiltros = {
        ...filtros,
        pagina: resultado.pagina + 1
      };

      const params = new URLSearchParams();
      
      Object.entries(novosFiltros).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            if (value.length > 0) {
              params.append(key, value.join(','));
            }
          } else {
            params.append(key, value.toString());
          }
        }
      });

      const response = await fetch(`/api/empresas/buscar?${params.toString()}`);
      const data: BuscaApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao carregar mais empresas');
      }

      if (data.success && data.data) {
        // Combinar resultados existentes com novos
        setResultado(prevResultado => {
          if (!prevResultado) return data.data!;
          
          return {
            ...data.data!,
            empresas: [...prevResultado.empresas, ...data.data!.empresas]
          };
        });
        
        setFiltrosState(novosFiltros);
      } else {
        throw new Error('Formato de resposta inválido');
      }

    } catch (err: any) {
      console.error('Erro ao carregar mais:', err);
      setError(err.message || 'Erro ao carregar mais empresas');
    } finally {
      setLoading(false);
    }
  }, [resultado, filtros, loading]);

  // Função para definir filtros
  const setFiltros = useCallback((novosFiltros: FiltrosBusca) => {
    setFiltrosState({ ...novosFiltros, pagina: 1 }); // Reset página ao alterar filtros
  }, []);

  // Função para limpar filtros
  const limparFiltros = useCallback(() => {
    setFiltrosState(filtrosIniciais);
    setResultado(null);
    setError(null);
  }, []);

  // Verificar se há filtros ativos
  const temFiltrosAtivos = useMemo(() => {
    return !!(
      filtros.termo ||
      filtros.cidade ||
      filtros.estado ||
      filtros.bairro ||
      (filtros.categorias_servicos && filtros.categorias_servicos.length > 0) ||
      filtros.preco_minimo !== undefined ||
      filtros.preco_maximo !== undefined
    );
  }, [filtros]);

  // Verificar se pode carregar mais
  const podeCarregarMais = useMemo(() => {
    return !!(resultado && resultado.tem_proxima_pagina && !loading);
  }, [resultado, loading]);

  return {
    resultado,
    estatisticas,
    loading,
    error,
    filtros,
    setFiltros,
    limparFiltros,
    buscar,
    carregarMais,
    temFiltrosAtivos,
    podeCarregarMais
  };
}
