'use client';

import Link from "next/link";
import { buttonVariants } from "@/components/ui/Button";
import React from "react";

export function ClientButtons() {
  return (
    <div className="flex flex-col sm:flex-row justify-center gap-4">
      <Link href="/buscar" className={buttonVariants({ variant: 'outline', size: 'lg', className: 'border-[var(--text-primary)] text-[var(--text-primary)] hover:bg-[var(--primary)] hover:text-[var(--text-on-primary)] shadow-lg' })}>
          Buscar Estabelecimentos
      </Link>
      <Link href="/planos" className={buttonVariants({ variant: 'primary', size: 'lg', className: 'bg-black text-white hover:bg-gray-800 shadow-lg' })}>
          Cadastre Seu Negócio
      </Link>
    </div>
  );
}
