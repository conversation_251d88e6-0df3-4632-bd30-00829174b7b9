'use client';

import { useState } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useStripeConnect } from '@/hooks/useStripeConnect';
import { StripeAccountStatus } from './StripeAccountStatus';

export function StripeConnectConfig() {
  const {
    loading,
    error,
    status,
    conectando,
    desconectando,
    atualizandoConfig,
    conectarStripe,
    desconectarStripe,
    verificarPodeDesconectar,
    atualizarConfigPagamentos,
    limparErro,
    estaConfigurado,
    obterProximosPassos
  } = useStripeConnect();

  const [mostrarDesconectar, setMostrarDesconectar] = useState(false);
  const [verificandoDesconexao, setVerificandoDesconexao] = useState(false);
  const [infoDesconexao, setInfoDesconexao] = useState<any>(null);

  const handleConectar = async () => {
    const onboardingUrl = await conectarStripe();
    if (onboardingUrl) {
      // Redirecionar para o Stripe
      window.location.href = onboardingUrl;
    }
  };

  const handleVerificarDesconexao = async () => {
    setVerificandoDesconexao(true);
    const info = await verificarPodeDesconectar();
    setInfoDesconexao(info);
    setMostrarDesconectar(true);
    setVerificandoDesconexao(false);
  };

  const handleDesconectar = async () => {
    const sucesso = await desconectarStripe();
    if (sucesso) {
      setMostrarDesconectar(false);
      setInfoDesconexao(null);
    }
  };

  const handleTogglePagamentos = async () => {
    if (!status) return;
    
    const novoStatus = !status.empresa.pagamentos_habilitados;
    await atualizarConfigPagamentos(novoStatus);
  };

  const formatarPercentual = (valor: number) => {
    return `${valor.toFixed(1)}%`;
  };

  if (loading && !status) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 border-2 border-[var(--primary)] border-t-transparent rounded-full animate-spin"></div>
              <span className="text-[var(--text-secondary)]">Carregando configurações...</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-[var(--text-primary)] mb-2">
          Configurações de Pagamento
        </h2>
        <p className="text-[var(--text-secondary)]">
          Configure sua conta Stripe para receber pagamentos de agendamentos online
        </p>
      </div>

      {/* Erro */}
      {error && (
        <Card className="bg-[var(--error-light)] border-[var(--error)]">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-[var(--error)] rounded-full flex items-center justify-center mt-0.5">
                <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-[var(--error)] mb-1">Erro</h4>
                <p className="text-sm text-[var(--text-secondary)]">{error}</p>
              </div>
              <Button variant="outline" size="sm" onClick={limparErro}>
                Fechar
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Status da Conta */}
      {status && <StripeAccountStatus status={status} />}

      {/* Configuração Principal */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <svg className="w-5 h-5 text-[var(--primary)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
            Conta Stripe
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {!status?.connected ? (
            // Não conectado
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-[var(--info-light)] rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-[var(--info)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-2">
                Conectar com Stripe
              </h3>
              <p className="text-[var(--text-secondary)] mb-6 max-w-md mx-auto">
                Para receber pagamentos online, você precisa conectar sua conta Stripe. 
                O processo é rápido e seguro.
              </p>
              <Button 
                onClick={handleConectar}
                disabled={conectando}
                className="px-8"
              >
                {conectando ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Conectando...
                  </div>
                ) : (
                  'Conectar com Stripe'
                )}
              </Button>
            </div>
          ) : (
            // Conectado
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-[var(--text-primary)]">
                    Conta Conectada
                  </h4>
                  <p className="text-sm text-[var(--text-secondary)]">
                    ID: {status.account_id}
                  </p>
                </div>
                <div className="flex gap-2">
                  {status.dashboard_url && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(status.dashboard_url, '_blank')}
                    >
                      Dashboard Stripe
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleVerificarDesconexao}
                    disabled={verificandoDesconexao}
                  >
                    {verificandoDesconexao ? 'Verificando...' : 'Desconectar'}
                  </Button>
                </div>
              </div>

              {/* Toggle de Pagamentos Online */}
              <div className="flex items-center justify-between p-4 bg-[var(--surface)] rounded-lg">
                <div>
                  <h5 className="font-medium text-[var(--text-primary)]">
                    Pagamentos Online
                  </h5>
                  <p className="text-sm text-[var(--text-secondary)]">
                    Permitir que clientes paguem agendamentos online
                  </p>
                </div>
                <Button
                  variant={status.empresa.pagamentos_habilitados ? "primary" : "outline"}
                  size="sm"
                  onClick={handleTogglePagamentos}
                  disabled={atualizandoConfig || !status.pode_receber_pagamentos}
                >
                  {atualizandoConfig ? (
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                      Atualizando...
                    </div>
                  ) : status.empresa.pagamentos_habilitados ? (
                    'Habilitado'
                  ) : (
                    'Habilitar'
                  )}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Informações de Comissão */}
      {status && (
        <Card className="bg-[var(--info-light)] border-[var(--info)]">
          <CardHeader>
            <CardTitle className="text-[var(--text-primary)]">
              Informações de Comissão
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-[var(--text-secondary)]">Comissão da Plataforma:</span>
              <span className="font-medium text-[var(--text-primary)]">
                {formatarPercentual(status.empresa.percentual_comissao)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-[var(--text-secondary)]">Taxa do Stripe:</span>
              <span className="font-medium text-[var(--text-primary)]">
                3,4% + R$ 0,60
              </span>
            </div>
            <hr className="border-[var(--border)]" />
            <p className="text-sm text-[var(--text-secondary)]">
              <strong>Exemplo:</strong> Em um agendamento de R$ 100,00, você recebe aproximadamente R$ {(100 - status.empresa.percentual_comissao - 4).toFixed(2)} 
              (descontando comissão da plataforma e taxas do Stripe).
            </p>
          </CardContent>
        </Card>
      )}

      {/* Próximos Passos */}
      {status && obterProximosPassos().length > 0 && (
        <Card className="bg-[var(--warning-light)] border-[var(--warning)]">
          <CardHeader>
            <CardTitle className="text-[var(--text-primary)]">
              Próximos Passos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {obterProximosPassos().map((passo, index) => (
                <li key={index} className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-[var(--warning)] rounded-full"></div>
                  <span className="text-sm text-[var(--text-secondary)]">{passo}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Modal de Desconexão */}
      {mostrarDesconectar && infoDesconexao && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardHeader>
              <CardTitle className="text-[var(--text-primary)]">
                Desconectar Conta Stripe
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {infoDesconexao.pode_desconectar ? (
                <div>
                  <p className="text-[var(--text-secondary)] mb-4">
                    Tem certeza que deseja desconectar sua conta Stripe? 
                    Você não poderá mais receber pagamentos online até reconectar.
                  </p>
                  <div className="flex gap-3">
                    <Button
                      variant="outline"
                      onClick={() => setMostrarDesconectar(false)}
                      className="flex-1"
                    >
                      Cancelar
                    </Button>
                    <Button
                      onClick={handleDesconectar}
                      disabled={desconectando}
                      className="flex-1 bg-[var(--error)] hover:bg-[var(--error)]/90"
                    >
                      {desconectando ? 'Desconectando...' : 'Desconectar'}
                    </Button>
                  </div>
                </div>
              ) : (
                <div>
                  <p className="text-[var(--text-secondary)] mb-4">
                    {infoDesconexao.motivo}
                  </p>
                  {infoDesconexao.agendamentos_pendentes > 0 && (
                    <p className="text-sm text-[var(--warning)] mb-4">
                      Aguarde a conclusão dos pagamentos pendentes antes de desconectar.
                    </p>
                  )}
                  <Button
                    onClick={() => setMostrarDesconectar(false)}
                    className="w-full"
                  >
                    Entendi
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
