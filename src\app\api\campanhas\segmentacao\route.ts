import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { SegmentacaoCampanha, ResultadoSegmentacao, ClienteSegmentado } from '@/types/marketing';

// POST - Segmentar clientes baseado nos critérios
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { segmentacao }: { segmentacao: SegmentacaoCampanha } = await request.json();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ success: false, error: 'Não autorizado' }, { status: 401 });
    }

    // Buscar empresa do usuário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select('empresa_id, plano_saas_id, planos_saas(nome_plano)')
      .eq('proprietario_user_id', user.id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json({ success: false, error: 'Empresa não encontrada' }, { status: 404 });
    }

    // Verificar se é plano Premium
    const planoSaas = Array.isArray(empresa.planos_saas)
      ? empresa.planos_saas[0]
      : empresa.planos_saas;
    const planoNome = planoSaas?.nome_plano;
    
    if (planoNome?.toLowerCase() !== 'premium') {
      return NextResponse.json({ 
        success: false, 
        error: 'Módulo de marketing disponível apenas no plano Premium' 
      }, { status: 403 });
    }

    // Construir query de segmentação
    const resultado = await segmentarClientes(supabase, empresa.empresa_id, segmentacao);

    return NextResponse.json({
      success: true,
      data: resultado
    });

  } catch (error) {
    console.error('Erro na segmentação de clientes:', error);
    return NextResponse.json({ success: false, error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// Função para segmentar clientes
async function segmentarClientes(
  supabase: any, 
  empresaId: number, 
  segmentacao: SegmentacaoCampanha
): Promise<ResultadoSegmentacao> {
  
  const periodoAnalise = segmentacao.periodo_analise_dias || 365; // Padrão: 1 ano
  const dataLimite = new Date();
  dataLimite.setDate(dataLimite.getDate() - periodoAnalise);

  // Query base para buscar clientes que fizeram agendamentos na empresa
  let query = `
    SELECT DISTINCT
      u.id as user_id,
      u.email,
      u.raw_user_meta_data->>'telefone' as telefone,
      u.raw_user_meta_data->>'nome' as nome,
      COUNT(a.agendamento_id) as total_agendamentos,
      COALESCE(SUM(a.valor_total), 0) as valor_total_gasto,
      MAX(a.data_hora_inicio) as ultimo_agendamento,
      EXTRACT(DAYS FROM NOW() - MAX(a.data_hora_inicio)) as dias_inativo,
      ARRAY_AGG(DISTINCT a.servico_id) as servicos_utilizados
    FROM auth.users u
    INNER JOIN agendamentos a ON u.id = a.cliente_user_id
    WHERE a.empresa_id = $1
      AND a.status_agendamento IN ('confirmado', 'concluido')
      AND a.data_hora_inicio >= $2
  `;

  const params = [empresaId, dataLimite.toISOString()];
  let paramIndex = 3;

  // Aplicar filtros de segmentação
  if (segmentacao.min_agendamentos) {
    query += ` GROUP BY u.id, u.email, u.raw_user_meta_data
               HAVING COUNT(a.agendamento_id) >= $${paramIndex}`;
    params.push(segmentacao.min_agendamentos);
    paramIndex++;
  } else {
    query += ` GROUP BY u.id, u.email, u.raw_user_meta_data`;
  }

  if (segmentacao.max_agendamentos) {
    if (!query.includes('HAVING')) {
      query += ` HAVING COUNT(a.agendamento_id) <= $${paramIndex}`;
    } else {
      query += ` AND COUNT(a.agendamento_id) <= $${paramIndex}`;
    }
    params.push(segmentacao.max_agendamentos);
    paramIndex++;
  }

  if (segmentacao.valor_gasto_min) {
    if (!query.includes('HAVING')) {
      query += ` HAVING SUM(a.valor_total) >= $${paramIndex}`;
    } else {
      query += ` AND SUM(a.valor_total) >= $${paramIndex}`;
    }
    params.push(segmentacao.valor_gasto_min);
    paramIndex++;
  }

  if (segmentacao.valor_gasto_max) {
    if (!query.includes('HAVING')) {
      query += ` HAVING SUM(a.valor_total) <= $${paramIndex}`;
    } else {
      query += ` AND SUM(a.valor_total) <= $${paramIndex}`;
    }
    params.push(segmentacao.valor_gasto_max);
    paramIndex++;
  }

  query += ` ORDER BY valor_total_gasto DESC`;

  // Executar query
  const { data: clientesRaw, error: clientesError } = await supabase.rpc('execute_raw_sql', {
    query,
    params
  });

  if (clientesError) {
    console.error('Erro ao segmentar clientes:', clientesError);
    throw new Error('Erro ao segmentar clientes');
  }

  // Processar resultados
  let clientes: ClienteSegmentado[] = (clientesRaw || []).map((cliente: any) => ({
    user_id: cliente.user_id,
    email: cliente.email,
    telefone: cliente.telefone,
    nome: cliente.nome,
    total_agendamentos: parseInt(cliente.total_agendamentos),
    valor_total_gasto: parseFloat(cliente.valor_total_gasto),
    ultimo_agendamento: cliente.ultimo_agendamento,
    dias_inativo: parseInt(cliente.dias_inativo) || 0,
    servicos_utilizados: cliente.servicos_utilizados || [],
    elegivel_email: !!cliente.email,
    elegivel_sms: !!cliente.telefone
  }));

  // Aplicar filtros adicionais
  if (segmentacao.clientes_ativos !== undefined) {
    const diasInatividade = segmentacao.clientes_inativos_dias || 30;
    
    if (segmentacao.clientes_ativos) {
      // Filtrar apenas clientes ativos (com agendamento recente)
      clientes = clientes.filter(c => (c.dias_inativo ?? 0) <= diasInatividade);
    } else {
      // Filtrar apenas clientes inativos
      clientes = clientes.filter(c => (c.dias_inativo ?? 0) > diasInatividade);
    }
  }

  if (segmentacao.servicos_utilizados && segmentacao.servicos_utilizados.length > 0) {
    clientes = clientes.filter(cliente => 
      segmentacao.servicos_utilizados!.some(servicoId => 
        cliente.servicos_utilizados.includes(servicoId)
      )
    );
  }

  // Calcular estatísticas
  const totalClientes = clientes.length;
  const clientesAtivos = clientes.filter(c => (c.dias_inativo ?? 0) <= 30).length;
  const clientesInativos = totalClientes - clientesAtivos;
  const ticketMedio = totalClientes > 0 
    ? clientes.reduce((sum, c) => sum + c.valor_total_gasto, 0) / totalClientes 
    : 0;

  // Buscar serviços mais utilizados
  const { data: servicosMaisUtilizados, error: servicosError } = await supabase
    .from('agendamentos')
    .select(`
      servico_id,
      servicos(nome_servico),
      count:servico_id
    `)
    .eq('empresa_id', empresaId)
    .in('cliente_user_id', clientes.map(c => c.user_id))
    .gte('data_hora_inicio', dataLimite.toISOString());

  if (servicosError) {
    console.error('Erro ao buscar serviços mais utilizados:', servicosError);
  }

  // Processar serviços mais utilizados
  const servicosAgrupados = (servicosMaisUtilizados || []).reduce((acc: any, item: any) => {
    const servicoId = item.servico_id;
    if (!acc[servicoId]) {
      acc[servicoId] = {
        servico_id: servicoId,
        nome_servico: item.servicos?.nome_servico || 'Serviço não encontrado',
        total_usos: 0
      };
    }
    acc[servicoId].total_usos++;
    return acc;
  }, {});

  const servicosOrdenados = Object.values(servicosAgrupados)
    .sort((a: any, b: any) => b.total_usos - a.total_usos)
    .slice(0, 5); // Top 5

  const resultado: ResultadoSegmentacao = {
    total_clientes: totalClientes,
    clientes_elegiveis: clientes,
    criterios_aplicados: segmentacao,
    estatisticas: {
      clientes_ativos: clientesAtivos,
      clientes_inativos: clientesInativos,
      ticket_medio: ticketMedio,
      servicos_mais_utilizados: servicosOrdenados as any[]
    }
  };

  return resultado;
}
