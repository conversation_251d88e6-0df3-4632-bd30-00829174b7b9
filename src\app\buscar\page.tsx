'use client';

import { useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { Layout } from '@/components/layout/Layout';
import { Breadcrumbs } from '@/components/ui/Breadcrumbs';
import { FiltrosBuscaComponent } from '@/components/busca/FiltrosBusca';
import { ResultadosBusca } from '@/components/busca/ResultadosBusca';
import { useBuscaEmpresas } from '@/hooks/useBuscaEmpresas';
import { urlParamsParaFiltros } from '@/utils/busca';
import { FiltrosBusca } from '@/types/busca';

function BuscarPageContent() {
  const searchParams = useSearchParams();
  const {
    resultado,
    estatisticas,
    loading,
    error,
    filtros,
    setFiltros,
    limparFiltros,
    buscar,
    carregarMais,
    podeCarregarMais
  } = useBuscaEmpresas();

  // Carregar filtros da URL na inicialização
  useEffect(() => {
    const filtrosURL = urlParamsParaFiltros(searchParams);
    if (Object.keys(filtrosURL).length > 0) {
      setFiltros(filtrosURL);
      buscar(filtrosURL);
    } else {
      // Busca inicial sem filtros para mostrar todos os estabelecimentos
      buscar();
    }
  }, []);

  const handleAplicarFiltros = (novosFiltros: FiltrosBusca) => {
    // Atualizar URL com os novos filtros
    const params = new URLSearchParams();
    Object.entries(novosFiltros).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          if (value.length > 0) {
            params.append(key, value.join(','));
          }
        } else {
          params.append(key, String(value));
        }
      }
    });

    // Atualizar URL sem recarregar a página
    const newUrl = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
    window.history.pushState({}, '', newUrl);

    // Executar busca
    buscar(novosFiltros);
  };

  const handleLimparFiltros = () => {
    // Limpar URL
    window.history.pushState({}, '', window.location.pathname);

    // Limpar filtros e buscar novamente
    limparFiltros();
    buscar({});
  };

  return (
    <Layout>
      <div className="bg-[var(--background)] py-8">
        <div className="container mx-auto px-4">
          {/* Breadcrumbs */}
          <div className="mb-8">
            <Breadcrumbs
              items={[
                { label: 'Início', href: '/' },
                { label: 'Buscar Estabelecimentos' }
              ]}
            />
          </div>

          {/* Header Section */}
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-5xl font-bold text-[var(--text-primary)] mb-4">
              Buscar Estabelecimentos
            </h1>
            <p className="text-xl text-[var(--text-secondary)] max-w-2xl mx-auto">
              Encontre os melhores estabelecimentos de serviços pessoais com filtros avançados.
            </p>
          </div>

          {/* Filtros de Busca */}
          <div className="mb-8">
            <FiltrosBuscaComponent
              filtros={filtros}
              estatisticas={estatisticas}
              onAplicarFiltros={handleAplicarFiltros}
              onLimparFiltros={handleLimparFiltros}
              loading={loading}
            />
          </div>

          {/* Resultados */}
          <ResultadosBusca
            resultado={resultado}
            loading={loading}
            error={error}
            onCarregarMais={carregarMais}
            podeCarregarMais={podeCarregarMais}
          />
        </div>
      </div>
    </Layout>
  );
}

export default function BuscarPage() {
  return (
    <Suspense fallback={
      <Layout>
        <div className="bg-[var(--background)] py-8">
          <div className="container mx-auto px-4">
            <div className="text-center py-12">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
              <p className="text-[var(--text-secondary)] mt-4">Carregando página de busca...</p>
            </div>
          </div>
        </div>
      </Layout>
    }>
      <BuscarPageContent />
    </Suspense>
  );
}

