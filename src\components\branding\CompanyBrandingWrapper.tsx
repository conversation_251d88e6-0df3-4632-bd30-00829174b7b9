'use client';

import React, { useEffect } from 'react';
import { useCompanyBranding } from '@/contexts/BrandingContext';

interface CompanyBrandingWrapperProps {
  companySlug: string;
  children: React.ReactNode;
  fallbackLogo?: string;
  fallbackName?: string;
}

export function CompanyBrandingWrapper({ 
  companySlug, 
  children, 
  fallbackLogo, 
  fallbackName 
}: CompanyBrandingWrapperProps) {
  const { isLoading } = useCompanyBranding(companySlug);

  // Mostrar loading enquanto carrega o branding
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]" />
          <span className="text-[var(--text-secondary)]">Carregando...</span>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

// Hook para usar dados da empresa na página
export function useCompanyData(companySlug: string) {
  const [companyData, setCompanyData] = React.useState<any>(null);
  const [isLoading, setIsLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  useEffect(() => {
    const fetchCompanyData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/empresas/${companySlug}`);
        
        if (!response.ok) {
          throw new Error('Empresa não encontrada');
        }
        
        const data = await response.json();
        setCompanyData(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erro ao carregar empresa');
      } finally {
        setIsLoading(false);
      }
    };

    if (companySlug) {
      fetchCompanyData();
    }
  }, [companySlug]);

  return { companyData, isLoading, error };
}

// Componente para mostrar logo da empresa com fallback
interface CompanyLogoProps {
  logoUrl?: string;
  companyName?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export function CompanyLogo({ 
  logoUrl, 
  companyName, 
  size = 'md', 
  className = '' 
}: CompanyLogoProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24',
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-xl',
  };

  if (logoUrl) {
    return (
      <img
        src={logoUrl}
        alt={`Logo ${companyName || 'da empresa'}`}
        className={`${sizeClasses[size]} object-contain rounded-lg ${className}`}
        onError={(e) => {
          // Fallback para ícone genérico se a imagem falhar
          const target = e.target as HTMLImageElement;
          target.style.display = 'none';
          const fallback = target.nextElementSibling as HTMLElement;
          if (fallback) {
            fallback.style.display = 'flex';
          }
        }}
      />
    );
  }

  // Fallback: ícone genérico ou iniciais da empresa
  const initials = companyName
    ? companyName
        .split(' ')
        .map(word => word.charAt(0))
        .join('')
        .substring(0, 2)
        .toUpperCase()
    : 'E';

  return (
    <div 
      className={`${sizeClasses[size]} bg-[var(--primary)] text-[var(--text-on-primary)] rounded-lg flex items-center justify-center font-semibold ${textSizeClasses[size]} ${className}`}
    >
      {initials}
    </div>
  );
}

// Componente para aplicar cores da empresa em elementos específicos
interface BrandedElementProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'accent';
  className?: string;
  style?: React.CSSProperties;
}

export function BrandedElement({ 
  children, 
  variant = 'primary', 
  className = '', 
  style = {} 
}: BrandedElementProps) {
  const colorVar = variant === 'primary' 
    ? 'var(--primary)' 
    : variant === 'secondary' 
    ? 'var(--secondary)' 
    : 'var(--accent)';

  return (
    <div 
      className={className}
      style={{
        color: colorVar,
        ...style,
      }}
    >
      {children}
    </div>
  );
}

// Componente para botões com cores da empresa
interface BrandedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'accent' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
}

export function BrandedButton({ 
  children, 
  onClick, 
  variant = 'primary', 
  size = 'md', 
  disabled = false,
  className = '',
  type = 'button',
}: BrandedButtonProps) {
  const baseClasses = 'font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
  };

  const getVariantClasses = () => {
    if (disabled) {
      return 'bg-gray-300 text-gray-500 cursor-not-allowed';
    }

    switch (variant) {
      case 'primary':
        return 'bg-[var(--primary)] hover:bg-[var(--primary-hover)] text-[var(--text-on-primary)] focus:ring-[var(--primary)]';
      case 'secondary':
        return 'bg-[var(--secondary)] hover:opacity-90 text-white focus:ring-[var(--secondary)]';
      case 'accent':
        return 'bg-[var(--accent)] hover:bg-[var(--accent-hover)] text-[var(--text-on-accent)] focus:ring-[var(--accent)]';
      case 'outline':
        return 'border-2 border-[var(--primary)] text-[var(--primary)] hover:bg-[var(--primary)] hover:text-[var(--text-on-primary)] focus:ring-[var(--primary)]';
      default:
        return 'bg-[var(--primary)] hover:bg-[var(--primary-hover)] text-[var(--text-on-primary)] focus:ring-[var(--primary)]';
    }
  };

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses[size]} ${getVariantClasses()} ${className}`}
    >
      {children}
    </button>
  );
}

// Componente para cards com bordas da cor da empresa
interface BrandedCardProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'accent';
  className?: string;
  onClick?: () => void;
}

export function BrandedCard({ 
  children, 
  variant = 'primary', 
  className = '',
  onClick,
}: BrandedCardProps) {
  const borderColor = variant === 'primary' 
    ? 'var(--primary)' 
    : variant === 'secondary' 
    ? 'var(--secondary)' 
    : 'var(--accent)';

  return (
    <div 
      className={`bg-[var(--surface)] rounded-lg border-l-4 border-[var(--border-color)] shadow-sm hover:shadow-md transition-shadow duration-200 ${onClick ? 'cursor-pointer' : ''} ${className}`}
      style={{
        borderLeftColor: borderColor,
      }}
      onClick={onClick}
    >
      {children}
    </div>
  );
}

// Componente para badges com cores da empresa
interface BrandedBadgeProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'accent';
  size?: 'sm' | 'md';
  className?: string;
}

export function BrandedBadge({ 
  children, 
  variant = 'primary', 
  size = 'sm',
  className = '',
}: BrandedBadgeProps) {
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
  };

  const getVariantClasses = () => {
    const opacity = '20'; // 20% de opacidade para o fundo
    
    switch (variant) {
      case 'primary':
        return `text-[var(--primary)]`;
      case 'secondary':
        return `text-[var(--secondary)]`;
      case 'accent':
        return `text-[var(--accent)]`;
      default:
        return `text-[var(--primary)]`;
    }
  };

  const getBgColor = () => {
    switch (variant) {
      case 'primary':
        return 'var(--primary)';
      case 'secondary':
        return 'var(--secondary)';
      case 'accent':
        return 'var(--accent)';
      default:
        return 'var(--primary)';
    }
  };

  return (
    <span 
      className={`inline-flex items-center font-medium rounded-full ${sizeClasses[size]} ${getVariantClasses()} ${className}`}
      style={{
        backgroundColor: `${getBgColor()}20`,
      }}
    >
      {children}
    </span>
  );
}
