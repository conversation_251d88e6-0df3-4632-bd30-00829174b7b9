import { useState, useCallback } from 'react';
import { 
  CriarPaymentIntentAgendamentoData, 
  PaymentIntentResponse, 
  ProcessarReembolsoData, 
  ReembolsoResponse 
} from '@/types/agendamentos';

interface EstadoPagamento {
  loading: boolean;
  error: string | null;
  paymentIntent: PaymentIntentResponse | null;
  processandoReembolso: boolean;
}

export function usePagamentoAgendamento() {
  const [estado, setEstado] = useState<EstadoPagamento>({
    loading: false,
    error: null,
    paymentIntent: null,
    processandoReembolso: false
  });

  // Criar Payment Intent para agendamento
  const criarPaymentIntent = useCallback(async (dados: CriarPaymentIntentAgendamentoData): Promise<PaymentIntentResponse | null> => {
    setEstado(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch('/api/agendamentos/payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dados),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao criar Payment Intent');
      }

      const paymentIntentData = result.data;
      setEstado(prev => ({
        ...prev,
        paymentIntent: paymentIntentData,
        loading: false
      }));

      return paymentIntentData;

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        error: error.message,
        loading: false
      }));
      return null;
    }
  }, []);

  // Processar reembolso
  const processarReembolso = useCallback(async (agendamentoId: number, dados: Omit<ProcessarReembolsoData, 'agendamento_id'>): Promise<ReembolsoResponse | null> => {
    setEstado(prev => ({ ...prev, processandoReembolso: true, error: null }));

    try {
      const response = await fetch(`/api/agendamentos/${agendamentoId}/refund`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          agendamento_id: agendamentoId,
          ...dados
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao processar reembolso');
      }

      setEstado(prev => ({
        ...prev,
        processandoReembolso: false
      }));

      return result.data;

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        error: error.message,
        processandoReembolso: false
      }));
      return null;
    }
  }, []);

  // Verificar status do pagamento
  const verificarStatusPagamento = useCallback(async (agendamentoId: number): Promise<{ status_pagamento: string; stripe_payment_intent_id?: string } | null> => {
    try {
      const response = await fetch(`/api/agendamentos/${agendamentoId}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao verificar status do pagamento');
      }

      return {
        status_pagamento: result.data.status_pagamento,
        stripe_payment_intent_id: result.data.stripe_payment_intent_id
      };

    } catch (error: any) {
      console.error('Erro ao verificar status do pagamento:', error);
      return null;
    }
  }, []);

  // Limpar erro
  const limparErro = useCallback(() => {
    setEstado(prev => ({ ...prev, error: null }));
  }, []);

  // Limpar Payment Intent
  const limparPaymentIntent = useCallback(() => {
    setEstado(prev => ({ ...prev, paymentIntent: null }));
  }, []);

  // Verificar se pode processar reembolso
  const podeProcessarReembolso = useCallback((statusPagamento: string, formaPagamento: string): boolean => {
    return formaPagamento === 'Online' && statusPagamento === 'Pago';
  }, []);

  // Calcular valor do reembolso baseado na política de cancelamento
  const calcularValorReembolso = useCallback((
    valorTotal: number, 
    dataHoraAgendamento: string, 
    agendamentoConfirmado: boolean,
    porcentagemReembolso: number = 100
  ): number => {
    const agora = new Date();
    const dataAgendamento = new Date(dataHoraAgendamento);
    const horasAteAgendamento = (dataAgendamento.getTime() - agora.getTime()) / (1000 * 60 * 60);

    // Se cancelar com mais de 24h de antecedência e não foi confirmado = 100%
    if (horasAteAgendamento > 24 && !agendamentoConfirmado) {
      return valorTotal;
    }

    // Se cancelar com menos de 24h e foi confirmado = usar política da empresa
    if (horasAteAgendamento <= 24 && agendamentoConfirmado) {
      return (valorTotal * porcentagemReembolso) / 100;
    }

    // Se cancelamento pela empresa = 100%
    return valorTotal;
  }, []);

  return {
    // Estado
    loading: estado.loading,
    error: estado.error,
    paymentIntent: estado.paymentIntent,
    processandoReembolso: estado.processandoReembolso,

    // Ações
    criarPaymentIntent,
    processarReembolso,
    verificarStatusPagamento,
    limparErro,
    limparPaymentIntent,

    // Utilitários
    podeProcessarReembolso,
    calcularValorReembolso
  };
}
