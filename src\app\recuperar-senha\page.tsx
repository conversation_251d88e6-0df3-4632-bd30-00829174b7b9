'use client';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/Card';
import { useAuth } from '@/contexts/AuthContext';

interface FormErrors {
  email?: string;
}

export default function RecuperarSenhaPage() {
  const router = useRouter();
  const { resetPassword, user } = useAuth();

  const [email, setEmail] = useState('');
  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  // Redirecionar se já estiver logado
  useEffect(() => {
    if (user) {
      router.push('/');
    }
  }, [user, router]);

  // Validação do formulário
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Validar e-mail
    if (!email.trim()) {
      newErrors.email = 'E-mail é obrigatório';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      newErrors.email = 'E-mail inválido';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Submissão do formulário
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setMessage('');

    try {
      const { error } = await resetPassword(email);

      if (error) {
        setMessage(error.message || 'Erro ao enviar e-mail de recuperação. Tente novamente.');
      } else {
        setMessage('E-mail de recuperação enviado! Verifique sua caixa de entrada e siga as instruções.');
      }
    } catch (error) {
      setMessage('Erro inesperado. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  // Limpar erros quando o usuário começar a digitar
  const handleEmailChange = (value: string) => {
    setEmail(value);
    if (errors.email) {
      setErrors(prev => ({ ...prev, email: undefined }));
    }
  };

  const isSuccessMessage = message.includes('enviado');

  if (user) {
    return null; // Não renderizar se já estiver logado
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-[var(--background)] p-4">
      <Card className="max-w-md w-full">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center text-[var(--text-primary)]">
            Recuperar Senha
          </CardTitle>
          <p className="text-center text-[var(--text-secondary)] text-sm">
            Digite seu e-mail para receber instruções de recuperação
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* E-mail */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-[var(--text-primary)] mb-1">
                E-mail
              </label>
              <Input
                type="email"
                id="email"
                value={email}
                onChange={(e) => handleEmailChange(e.target.value)}
                placeholder="Digite seu e-mail"
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">{errors.email}</p>
              )}
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={loading}
            >
              {loading ? 'Enviando...' : 'Enviar Instruções'}
            </Button>
          </form>

          {message && (
            <div className={`mt-4 p-3 rounded-md text-sm text-center ${
              isSuccessMessage
                ? 'bg-green-50 text-green-700 border border-green-200'
                : 'bg-red-50 text-red-700 border border-red-200'
            }`}>
              {message}
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-col items-center gap-3">
          <div>
            <Link href="/login">
              <span className="text-sm font-medium text-[var(--primary)] hover:text-[var(--primary-hover)] cursor-pointer">
                Lembrou a senha? Faça login
              </span>
            </Link>
          </div>
          <div className="text-sm">
            <span className="text-[var(--text-secondary)]">Não tem uma conta? </span>
            <Link href="/cadastro">
              <span className="text-sm font-medium text-[var(--primary)] hover:text-[var(--primary-hover)] cursor-pointer">
                Cadastre-se
              </span>
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}