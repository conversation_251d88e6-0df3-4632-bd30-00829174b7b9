'use client';

import React from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import MonitoringDashboard from '@/components/monitoring/MonitoringDashboard';
import { Breadcrumbs } from '@/components/ui/Breadcrumbs';

export default function MonitoringPage() {
  const breadcrumbItems = [
    { label: 'Admin', href: '/admin/dashboard' },
    { label: 'Monitoramento', href: '/admin/monitoring' }
  ];

  return (
    <ProtectedRoute requiredRole="Administrador">
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto">
          {/* Breadcrumbs */}
          <div className="px-6 pt-6">
            <Breadcrumbs items={breadcrumbItems} />
          </div>

          {/* Dashboard de Monitoramento */}
          <MonitoringDashboard />
        </div>
      </div>
    </ProtectedRoute>
  );
}
