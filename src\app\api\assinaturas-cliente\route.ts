import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/client';
import { AssinarPlanoData } from '@/types/planosAssinatura';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-05-28.basil',
});

// GET - Listar assinaturas do cliente
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ success: false, error: 'Não autorizado' }, { status: 401 });
    }

    // Parâmetros de filtro
    const empresaId = searchParams.get('empresa_id');
    const servicoId = searchParams.get('servico_id');
    const status = searchParams.get('status');
    const apenasAtivos = searchParams.get('apenas_ativos') === 'true';

    // Buscar assinaturas do cliente
    let query = supabase
      .from('planos_servico_cliente')
      .select(`
        *,
        servicos(
          nome_servico,
          descricao,
          preco,
          duracao_minutos,
          categoria
        ),
        empresas(
          nome_empresa,
          telefone,
          endereco,
          cidade,
          estado
        )
      `)
      .eq('cliente_user_id', user.id);

    if (empresaId) {
      query = query.eq('empresa_id', parseInt(empresaId));
    }

    if (servicoId) {
      query = query.eq('servico_id', parseInt(servicoId));
    }

    if (status) {
      query = query.eq('status', status);
    }

    if (apenasAtivos) {
      query = query.eq('ativo', true).in('status', ['ativa', 'trial']);
    }

    query = query.order('created_at', { ascending: false });

    const { data: assinaturas, error: assinaturasError } = await query;

    if (assinaturasError) {
      console.error('Erro ao buscar assinaturas:', assinaturasError);
      return NextResponse.json({ 
        success: false, 
        error: 'Erro ao buscar assinaturas' 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: assinaturas || [],
      total: assinaturas?.length || 0
    });

  } catch (error) {
    console.error('Erro na API de assinaturas:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Erro interno do servidor' 
    }, { status: 500 });
  }
}

// POST - Criar nova assinatura
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const body: AssinarPlanoData = await request.json();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ success: false, error: 'Não autorizado' }, { status: 401 });
    }

    // Validar dados de entrada
    if (!body.empresa_id || !body.servico_id) {
      return NextResponse.json({ 
        success: false, 
        error: 'Empresa e serviço são obrigatórios' 
      }, { status: 400 });
    }

    // Verificar se a empresa oferece planos de assinatura (Premium)
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select(`
        empresa_id,
        nome_empresa,
        assinaturas_saas_empresas!inner(
          status_assinatura,
          planos_saas!inner(nome_plano)
        )
      `)
      .eq('empresa_id', body.empresa_id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json({ 
        success: false, 
        error: 'Empresa não encontrada' 
      }, { status: 404 });
    }

    // Verificar se a empresa tem plano Premium ativo
    const assinaturaSaas = Array.isArray(empresa.assinaturas_saas_empresas) 
      ? empresa.assinaturas_saas_empresas[0] 
      : empresa.assinaturas_saas_empresas;

    const planoSaas = Array.isArray(assinaturaSaas?.planos_saas)
      ? assinaturaSaas.planos_saas[0]
      : assinaturaSaas?.planos_saas;

    if (!assinaturaSaas ||
        assinaturaSaas.status_assinatura !== 'ativa' ||
        !planoSaas ||
        planoSaas.nome_plano !== 'Premium') {
      return NextResponse.json({ 
        success: false, 
        error: 'Esta empresa não oferece planos de assinatura' 
      }, { status: 403 });
    }

    // Verificar se o serviço existe e está ativo
    const { data: servico, error: servicoError } = await supabase
      .from('servicos')
      .select('servico_id, nome_servico, preco, duracao_minutos')
      .eq('servico_id', body.servico_id)
      .eq('empresa_id', body.empresa_id)
      .eq('ativo', true)
      .single();

    if (servicoError || !servico) {
      return NextResponse.json({ 
        success: false, 
        error: 'Serviço não encontrado ou inativo' 
      }, { status: 404 });
    }

    // Verificar se o cliente já tem assinatura ativa para este serviço
    const { data: assinaturaExistente } = await supabase
      .from('planos_servico_cliente')
      .select('plano_cliente_id')
      .eq('cliente_user_id', user.id)
      .eq('empresa_id', body.empresa_id)
      .eq('servico_id', body.servico_id)
      .eq('ativo', true)
      .in('status', ['ativa', 'trial'])
      .single();

    if (assinaturaExistente) {
      return NextResponse.json({ 
        success: false, 
        error: 'Você já possui uma assinatura ativa para este serviço' 
      }, { status: 409 });
    }

    // Calcular datas do ciclo (mensal)
    const dataInicioCiclo = new Date();
    const dataFimCiclo = new Date();
    dataFimCiclo.setMonth(dataFimCiclo.getMonth() + 1);

    const dataProximaCobranca = new Date(dataFimCiclo);

    // Configurar plano padrão
    const precoAssinatura = servico.preco * 0.8; // 20% de desconto
    const limiteUsosMes = 10; // Padrão: 10 usos por mês

    // Se método de pagamento for Stripe, criar subscription
    let stripeSubscriptionId: string | undefined;

    if (body.metodo_pagamento === 'stripe') {
      try {
        // Buscar ou criar customer no Stripe
        const { data: profile } = await supabase.auth.getUser();
        const customerEmail = profile.user?.email;

        if (!customerEmail) {
          return NextResponse.json({ 
            success: false, 
            error: 'Email do usuário não encontrado' 
          }, { status: 400 });
        }

        // Criar produto no Stripe se não existir
        const produto = await stripe.products.create({
          name: `Assinatura - ${servico.nome_servico}`,
          description: `Plano mensal para ${servico.nome_servico} na ${empresa.nome_empresa}`,
          metadata: {
            empresa_id: body.empresa_id.toString(),
            servico_id: body.servico_id.toString(),
            cliente_user_id: user.id
          }
        });

        // Criar preço no Stripe
        const preco = await stripe.prices.create({
          product: produto.id,
          unit_amount: Math.round(precoAssinatura * 100), // Converter para centavos
          currency: 'brl',
          recurring: {
            interval: 'month'
          }
        });

        // Criar customer no Stripe
        const customer = await stripe.customers.create({
          email: customerEmail,
          metadata: {
            user_id: user.id,
            empresa_id: body.empresa_id.toString()
          }
        });

        // Criar subscription no Stripe
        const subscription = await stripe.subscriptions.create({
          customer: customer.id,
          items: [{ price: preco.id }],
          payment_behavior: 'default_incomplete',
          payment_settings: { save_default_payment_method: 'on_subscription' },
          expand: ['latest_invoice.payment_intent'],
          metadata: {
            empresa_id: body.empresa_id.toString(),
            servico_id: body.servico_id.toString(),
            cliente_user_id: user.id
          }
        });

        stripeSubscriptionId = subscription.id;

        // Retornar client_secret para completar pagamento no frontend
        const invoice = subscription.latest_invoice as any;
        const paymentIntent = invoice?.payment_intent;

        if (paymentIntent?.client_secret) {
          // Criar assinatura com status 'trial' até confirmação do pagamento
          const novaAssinatura = {
            cliente_user_id: user.id,
            empresa_id: body.empresa_id,
            servico_id: body.servico_id,
            nome_plano_empresa: `Plano Mensal - ${servico.nome_servico}`,
            descricao_plano: `Acesso mensal ao serviço ${servico.nome_servico} com 20% de desconto`,
            preco_mensal_assinatura: precoAssinatura,
            limite_usos_mes: limiteUsosMes,
            stripe_subscription_id: stripeSubscriptionId,
            status: 'trial' as const,
            data_inicio_ciclo: dataInicioCiclo.toISOString(),
            data_fim_ciclo: dataFimCiclo.toISOString(),
            data_proxima_cobranca: dataProximaCobranca.toISOString(),
            ativo: true
          };

          const { data: assinaturaCriada, error: criarError } = await supabase
            .from('planos_servico_cliente')
            .insert(novaAssinatura)
            .select()
            .single();

          if (criarError) {
            console.error('Erro ao criar assinatura:', criarError);
            // Cancelar subscription no Stripe se falhou no banco
            await stripe.subscriptions.cancel(subscription.id);
            return NextResponse.json({ 
              success: false, 
              error: 'Erro ao criar assinatura' 
            }, { status: 500 });
          }

          return NextResponse.json({
            success: true,
            data: assinaturaCriada,
            client_secret: paymentIntent.client_secret,
            subscription_id: subscription.id,
            message: 'Assinatura criada. Complete o pagamento para ativar.'
          }, { status: 201 });
        }

      } catch (stripeError) {
        console.error('Erro no Stripe:', stripeError);
        return NextResponse.json({ 
          success: false, 
          error: 'Erro ao processar pagamento' 
        }, { status: 500 });
      }
    }

    // Se não for Stripe, criar assinatura diretamente (pagamento local)
    const novaAssinatura = {
      cliente_user_id: user.id,
      empresa_id: body.empresa_id,
      servico_id: body.servico_id,
      nome_plano_empresa: `Plano Mensal - ${servico.nome_servico}`,
      descricao_plano: `Acesso mensal ao serviço ${servico.nome_servico} com 20% de desconto`,
      preco_mensal_assinatura: precoAssinatura,
      limite_usos_mes: limiteUsosMes,
      status: 'ativa' as const,
      data_inicio_ciclo: dataInicioCiclo.toISOString(),
      data_fim_ciclo: dataFimCiclo.toISOString(),
      data_proxima_cobranca: dataProximaCobranca.toISOString(),
      ativo: true
    };

    const { data: assinaturaCriada, error: criarError } = await supabase
      .from('planos_servico_cliente')
      .insert(novaAssinatura)
      .select(`
        *,
        servicos(nome_servico, preco),
        empresas(nome_empresa)
      `)
      .single();

    if (criarError) {
      console.error('Erro ao criar assinatura:', criarError);
      return NextResponse.json({ 
        success: false, 
        error: 'Erro ao criar assinatura' 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: assinaturaCriada,
      message: 'Assinatura criada com sucesso'
    }, { status: 201 });

  } catch (error) {
    console.error('Erro ao criar assinatura:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Erro interno do servidor' 
    }, { status: 500 });
  }
}
