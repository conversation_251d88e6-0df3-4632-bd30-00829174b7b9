'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ComboDetectado } from '@/types/combos';

interface Servico {
  servico_id: number;
  nome_servico: string;
  descricao: string;
  duracao_minutos: number;
  preco: number;
  categoria: string;
}

interface SeletorServicosProps {
  servicos: Servico[];
  servicosPorCategoria: Record<string, Servico[]>;
  servicoSelecionado?: {
    servico_id: number;
    nome_servico: string;
    duracao_minutos: number;
    preco: number;
  };
  onSelecionarServico: (servico: {
    servico_id: number;
    nome_servico: string;
    duracao_minutos: number;
    preco: number;
  }) => void;
  loading?: boolean;
  empresaId?: number;
  onComboDetectado?: (combo: ComboDetectado | null) => void;
}

export function SeletorServicos({
  servicos,
  servicosPorCategoria,
  servicoSelecionado,
  onSelecionarServico,
  loading = false,
  empresaId,
  onComboDetectado
}: SeletorServicosProps) {
  const [categoriaExpandida, setCategoriaExpandida] = useState<string | null>(null);
  const [visualizacao, setVisualizacao] = useState<'lista' | 'categoria'>('categoria');
  const [combosDisponiveis, setCombosDisponiveis] = useState<ComboDetectado[]>([]);
  const [carregandoCombos, setCarregandoCombos] = useState(false);

  // Buscar combos da empresa
  const buscarCombos = async () => {
    if (!empresaId) return;

    setCarregandoCombos(true);
    try {
      const response = await fetch(`/api/combos?ativo=true`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.data)) {
          // Detectar combos aplicáveis se há serviço selecionado
          if (servicoSelecionado) {
            const { detectarCombosAplicaveis } = await import('@/utils/combos');
            const combosDetectados = detectarCombosAplicaveis([servicoSelecionado.servico_id], data.data);
            setCombosDisponiveis(combosDetectados);

            // Notificar o melhor combo se houver
            const melhorCombo = combosDetectados.find(combo => combo.pode_aplicar);
            if (onComboDetectado) {
              onComboDetectado(melhorCombo || null);
            }
          }
        }
      }
    } catch (error) {
      console.error('Erro ao buscar combos:', error);
    } finally {
      setCarregandoCombos(false);
    }
  };

  // Buscar combos quando empresa ou serviço selecionado mudar
  useEffect(() => {
    if (empresaId) {
      buscarCombos();
    }
  }, [empresaId, servicoSelecionado]);

  const formatarPreco = (preco: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(preco);
  };

  const formatarDuracao = (minutos: number) => {
    if (minutos < 60) {
      return `${minutos}min`;
    }
    const horas = Math.floor(minutos / 60);
    const minutosRestantes = minutos % 60;
    return minutosRestantes > 0 ? `${horas}h ${minutosRestantes}min` : `${horas}h`;
  };

  const toggleCategoria = (categoria: string) => {
    setCategoriaExpandida(categoriaExpandida === categoria ? null : categoria);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
          <p className="text-[var(--text-secondary)] mt-4">Carregando serviços...</p>
        </div>
      </div>
    );
  }

  if (!servicos || servicos.length === 0) {
    return (
      <div className="text-center py-8">
        <svg className="mx-auto h-12 w-12 text-[var(--text-secondary)] mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <p className="text-[var(--text-secondary)] text-lg">Nenhum serviço disponível</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header com controles */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h3 className="text-xl font-semibold text-[var(--text-primary)]">
            Selecione um Serviço
          </h3>
          <p className="text-[var(--text-secondary)] text-sm">
            Escolha o serviço que deseja agendar
          </p>
        </div>

        {/* Toggle de visualização */}
        <div className="flex bg-[var(--surface)] rounded-lg p-1">
          <button
            onClick={() => setVisualizacao('categoria')}
            className={`px-3 py-1 rounded text-sm transition-colors ${
              visualizacao === 'categoria'
                ? 'bg-[var(--primary)] text-white'
                : 'text-[var(--text-secondary)] hover:text-[var(--text-primary)]'
            }`}
          >
            Por Categoria
          </button>
          <button
            onClick={() => setVisualizacao('lista')}
            className={`px-3 py-1 rounded text-sm transition-colors ${
              visualizacao === 'lista'
                ? 'bg-[var(--primary)] text-white'
                : 'text-[var(--text-secondary)] hover:text-[var(--text-primary)]'
            }`}
          >
            Lista Completa
          </button>
        </div>
      </div>

      {/* Visualização por categoria */}
      {visualizacao === 'categoria' && (
        <div className="space-y-4">
          {Object.entries(servicosPorCategoria).map(([categoria, servicosCategoria]) => (
            <Card key={categoria} className="overflow-hidden">
              <div
                className="p-4 bg-[var(--surface)] border-b cursor-pointer hover:bg-[var(--surface-hover)] transition-colors"
                onClick={() => toggleCategoria(categoria)}
              >
                <div className="flex justify-between items-center">
                  <h4 className="font-medium text-[var(--text-primary)]">
                    {categoria} ({servicosCategoria.length})
                  </h4>
                  <svg
                    className={`w-5 h-5 text-[var(--text-secondary)] transition-transform ${
                      categoriaExpandida === categoria ? 'rotate-180' : ''
                    }`}
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>

              {categoriaExpandida === categoria && (
                <CardContent className="p-0">
                  <div className="space-y-2 p-4">
                    {servicosCategoria.map((servico) => (
                      <div
                        key={servico.servico_id}
                        className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                          servicoSelecionado?.servico_id === servico.servico_id
                            ? 'border-[var(--primary)] bg-[var(--primary-light)]'
                            : 'border-[var(--border)] hover:border-[var(--primary-light)] hover:bg-[var(--surface-hover)]'
                        }`}
                        onClick={() => onSelecionarServico({
                          servico_id: servico.servico_id,
                          nome_servico: servico.nome_servico,
                          duracao_minutos: servico.duracao_minutos,
                          preco: servico.preco
                        })}
                      >
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h5 className="font-medium text-[var(--text-primary)] mb-1">
                              {servico.nome_servico}
                            </h5>
                            {servico.descricao && (
                              <p className="text-sm text-[var(--text-secondary)] mb-2">
                                {servico.descricao}
                              </p>
                            )}
                            <div className="flex items-center gap-4 text-sm">
                              <span className="text-[var(--text-secondary)]">
                                ⏱️ {formatarDuracao(servico.duracao_minutos)}
                              </span>
                              <span className="font-semibold text-[var(--primary)]">
                                {formatarPreco(servico.preco)}
                              </span>
                            </div>
                          </div>
                          
                          {servicoSelecionado?.servico_id === servico.servico_id && (
                            <div className="ml-4">
                              <div className="w-6 h-6 bg-[var(--primary)] rounded-full flex items-center justify-center">
                                <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      )}

      {/* Visualização em lista */}
      {visualizacao === 'lista' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {servicos.map((servico) => (
            <Card
              key={servico.servico_id}
              className={`cursor-pointer transition-all hover:shadow-lg ${
                servicoSelecionado?.servico_id === servico.servico_id
                  ? 'ring-2 ring-[var(--primary)] bg-[var(--primary-light)]'
                  : 'hover:ring-1 hover:ring-[var(--primary-light)]'
              }`}
              onClick={() => onSelecionarServico({
                servico_id: servico.servico_id,
                nome_servico: servico.nome_servico,
                duracao_minutos: servico.duracao_minutos,
                preco: servico.preco
              })}
            >
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <CardTitle className="text-lg">{servico.nome_servico}</CardTitle>
                  {servicoSelecionado?.servico_id === servico.servico_id && (
                    <div className="w-6 h-6 bg-[var(--primary)] rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  )}
                </div>
                
                <div className="text-xs text-[var(--text-secondary)] mb-2 bg-[var(--surface)] px-2 py-1 rounded inline-block">
                  {servico.categoria}
                </div>
                
                {servico.descricao && (
                  <p className="text-sm text-[var(--text-secondary)] mb-3 line-clamp-2">
                    {servico.descricao}
                  </p>
                )}
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-[var(--text-secondary)]">
                    ⏱️ {formatarDuracao(servico.duracao_minutos)}
                  </span>
                  <span className="font-semibold text-lg text-[var(--primary)]">
                    {formatarPreco(servico.preco)}
                  </span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Combos sugeridos */}
      {combosDisponiveis.length > 0 && (
        <Card className="mt-6 border-purple-200 bg-purple-50">
          <CardHeader>
            <CardTitle className="text-purple-700 flex items-center gap-2">
              🎁 Combos Disponíveis
              {carregandoCombos && (
                <span className="text-sm font-normal text-purple-600">Carregando...</span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {combosDisponiveis.slice(0, 3).map((comboDetectado) => (
                <div
                  key={comboDetectado.combo.combo_id}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    comboDetectado.pode_aplicar
                      ? 'border-purple-300 bg-purple-100'
                      : 'border-gray-300 bg-gray-50'
                  }`}
                >
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-semibold text-purple-800">
                        {comboDetectado.combo.nome_combo}
                      </h4>
                      <p className="text-sm text-purple-600">
                        {comboDetectado.combo.descricao}
                      </p>
                    </div>
                    {comboDetectado.pode_aplicar && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                        Aplicável
                      </span>
                    )}
                  </div>

                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Valor original:</span>
                      <div className="font-medium">{formatarPreco(comboDetectado.valor_original)}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Com desconto:</span>
                      <div className="font-medium text-green-600">
                        {formatarPreco(comboDetectado.valor_com_desconto)}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-600">Economia:</span>
                      <div className="font-medium text-purple-600">
                        {formatarPreco(comboDetectado.economia)}
                      </div>
                    </div>
                  </div>

                  {comboDetectado.servicos_faltantes.length > 0 && (
                    <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                      <p className="text-sm text-yellow-800">
                        <strong>Para ativar este combo, adicione:</strong>
                      </p>
                      <div className="text-sm text-yellow-700 mt-1">
                        {comboDetectado.servicos_faltantes.map(servicoId => {
                          const servico = servicos.find(s => s.servico_id === servicoId);
                          return servico ? servico.nome_servico : 'Serviço não encontrado';
                        }).join(', ')}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {combosDisponiveis.length > 3 && (
              <div className="text-center mt-4">
                <p className="text-sm text-purple-600">
                  + {combosDisponiveis.length - 3} outros combos disponíveis
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Serviço selecionado - resumo */}
      {servicoSelecionado && (
        <Card className="bg-[var(--primary-light)] border-[var(--primary)]">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-[var(--primary)] rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-[var(--text-primary)]">
                  Serviço Selecionado: {servicoSelecionado.nome_servico}
                </h4>
                <p className="text-sm text-[var(--text-secondary)]">
                  {formatarDuracao(servicoSelecionado.duracao_minutos)} • {formatarPreco(servicoSelecionado.preco)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
