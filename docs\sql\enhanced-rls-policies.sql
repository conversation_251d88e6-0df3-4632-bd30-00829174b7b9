-- =====================================================
-- POLÍTICAS RLS APRIMORADAS PARA SERVICETECH
-- =====================================================
-- Este arquivo contém políticas RLS adicionais e melhoradas
-- para fortalecer a segurança do banco de dados

-- =====================================================
-- 1. POLÍTICAS PARA TABELA AGENDAMENTOS
-- =====================================================

-- Habilitar RLS se ainda não estiver habilitado
ALTER TABLE agendamentos ENABLE ROW LEVEL SECURITY;

-- Remover políticas existentes se houver conflito
DROP POLICY IF EXISTS "agendamentos_cliente_policy" ON agendamentos;
DROP POLICY IF EXISTS "agendamentos_colaborador_policy" ON agendamentos;
DROP POLICY IF EXISTS "agendamentos_proprietario_policy" ON agendamentos;
DROP POLICY IF EXISTS "agendamentos_admin_policy" ON agendamentos;

-- Clientes podem ver e criar seus próprios agendamentos
CREATE POLICY "agendamentos_cliente_select" ON agendamentos
  FOR SELECT USING (cliente_user_id = auth.uid());

CREATE POLICY "agendamentos_cliente_insert" ON agendamentos
  FOR INSERT WITH CHECK (cliente_user_id = auth.uid());

CREATE POLICY "agendamentos_cliente_update" ON agendamentos
  FOR UPDATE USING (
    cliente_user_id = auth.uid() AND
    status_agendamento IN ('pendente', 'confirmado') -- Só pode alterar se não finalizado
  );

-- Colaboradores podem ver agendamentos onde são prestadores
CREATE POLICY "agendamentos_colaborador_select" ON agendamentos
  FOR SELECT USING (colaborador_user_id = auth.uid());

CREATE POLICY "agendamentos_colaborador_update" ON agendamentos
  FOR UPDATE USING (
    colaborador_user_id = auth.uid() AND
    status_agendamento IN ('pendente', 'confirmado', 'em_andamento')
  );

-- Proprietários podem gerenciar agendamentos de sua empresa
CREATE POLICY "agendamentos_proprietario_all" ON agendamentos
  FOR ALL USING (
    empresa_id IN (
      SELECT empresa_id FROM empresas 
      WHERE proprietario_user_id = auth.uid()
    )
  );

-- Administradores têm acesso total (bypass via service role)

-- =====================================================
-- 2. POLÍTICAS PARA TABELA PAGAMENTOS
-- =====================================================

ALTER TABLE pagamentos ENABLE ROW LEVEL SECURITY;

-- Clientes podem ver seus próprios pagamentos
CREATE POLICY "pagamentos_cliente_select" ON pagamentos
  FOR SELECT USING (
    agendamento_id IN (
      SELECT agendamento_id FROM agendamentos 
      WHERE cliente_user_id = auth.uid()
    )
  );

-- Proprietários podem ver pagamentos de sua empresa
CREATE POLICY "pagamentos_proprietario_select" ON pagamentos
  FOR SELECT USING (
    agendamento_id IN (
      SELECT a.agendamento_id FROM agendamentos a
      JOIN empresas e ON a.empresa_id = e.empresa_id
      WHERE e.proprietario_user_id = auth.uid()
    )
  );

-- Colaboradores podem ver pagamentos de seus agendamentos
CREATE POLICY "pagamentos_colaborador_select" ON pagamentos
  FOR SELECT USING (
    agendamento_id IN (
      SELECT agendamento_id FROM agendamentos 
      WHERE colaborador_user_id = auth.uid()
    )
  );

-- =====================================================
-- 3. POLÍTICAS PARA TABELA ASSINATURAS_SAAS_EMPRESAS
-- =====================================================

ALTER TABLE assinaturas_saas_empresas ENABLE ROW LEVEL SECURITY;

-- Proprietários podem ver suas próprias assinaturas
CREATE POLICY "assinaturas_proprietario_select" ON assinaturas_saas_empresas
  FOR SELECT USING (
    empresa_id IN (
      SELECT empresa_id FROM empresas 
      WHERE proprietario_user_id = auth.uid()
    )
  );

-- =====================================================
-- 4. POLÍTICAS PARA TABELA NOTIFICACOES
-- =====================================================

ALTER TABLE notificacoes ENABLE ROW LEVEL SECURITY;

-- Usuários podem ver suas próprias notificações
CREATE POLICY "notificacoes_user_select" ON notificacoes
  FOR SELECT USING (destinatario_user_id = auth.uid());

CREATE POLICY "notificacoes_user_update" ON notificacoes
  FOR UPDATE USING (
    destinatario_user_id = auth.uid() AND
    -- Só pode marcar como lida
    OLD.lida = false AND NEW.lida = true
  );

-- =====================================================
-- 5. POLÍTICAS PARA TABELA COMBOS_SERVICOS
-- =====================================================

ALTER TABLE combos_servicos ENABLE ROW LEVEL SECURITY;

-- Proprietários podem gerenciar combos de sua empresa
CREATE POLICY "combos_proprietario_all" ON combos_servicos
  FOR ALL USING (
    empresa_id IN (
      SELECT empresa_id FROM empresas 
      WHERE proprietario_user_id = auth.uid()
    )
  );

-- Clientes podem ver combos ativos
CREATE POLICY "combos_cliente_select" ON combos_servicos
  FOR SELECT USING (ativo = true);

-- =====================================================
-- 6. POLÍTICAS PARA TABELA COMBO_ITENS
-- =====================================================

ALTER TABLE combo_itens ENABLE ROW LEVEL SECURITY;

-- Proprietários podem gerenciar itens de combos de sua empresa
CREATE POLICY "combo_itens_proprietario_all" ON combo_itens
  FOR ALL USING (
    combo_id IN (
      SELECT combo_id FROM combos_servicos cs
      JOIN empresas e ON cs.empresa_id = e.empresa_id
      WHERE e.proprietario_user_id = auth.uid()
    )
  );

-- Clientes podem ver itens de combos ativos
CREATE POLICY "combo_itens_cliente_select" ON combo_itens
  FOR SELECT USING (
    combo_id IN (
      SELECT combo_id FROM combos_servicos 
      WHERE ativo = true
    )
  );

-- =====================================================
-- 7. POLÍTICAS PARA TABELA PLANOS_SERVICO_CLIENTE
-- =====================================================

ALTER TABLE planos_servico_cliente ENABLE ROW LEVEL SECURITY;

-- Clientes podem ver seus próprios planos
CREATE POLICY "planos_cliente_select" ON planos_servico_cliente
  FOR SELECT USING (cliente_user_id = auth.uid());

CREATE POLICY "planos_cliente_insert" ON planos_servico_cliente
  FOR INSERT WITH CHECK (cliente_user_id = auth.uid());

-- Proprietários podem ver planos de sua empresa
CREATE POLICY "planos_proprietario_select" ON planos_servico_cliente
  FOR SELECT USING (
    empresa_id IN (
      SELECT empresa_id FROM empresas 
      WHERE proprietario_user_id = auth.uid()
    )
  );

-- =====================================================
-- 8. POLÍTICAS APRIMORADAS PARA COLABORADOR_SERVICOS
-- =====================================================

ALTER TABLE colaborador_servicos ENABLE ROW LEVEL SECURITY;

-- Remover políticas existentes
DROP POLICY IF EXISTS "colaborador_servicos_policy" ON colaborador_servicos;

-- Proprietários podem gerenciar associações de sua empresa
CREATE POLICY "colaborador_servicos_proprietario_all" ON colaborador_servicos
  FOR ALL USING (
    colaborador_empresa_id IN (
      SELECT colaborador_empresa_id FROM colaboradores_empresa ce
      JOIN empresas e ON ce.empresa_id = e.empresa_id
      WHERE e.proprietario_user_id = auth.uid()
    )
  );

-- Colaboradores podem ver suas próprias associações
CREATE POLICY "colaborador_servicos_colaborador_select" ON colaborador_servicos
  FOR SELECT USING (
    colaborador_empresa_id IN (
      SELECT colaborador_empresa_id FROM colaboradores_empresa 
      WHERE colaborador_user_id = auth.uid()
    )
  );

-- =====================================================
-- 9. FUNÇÃO PARA VERIFICAR PERMISSÕES DE EMPRESA
-- =====================================================

CREATE OR REPLACE FUNCTION user_has_company_access(company_id INTEGER)
RETURNS BOOLEAN AS $$
BEGIN
  -- Verificar se é proprietário
  IF EXISTS (
    SELECT 1 FROM empresas 
    WHERE empresa_id = company_id AND proprietario_user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;
  
  -- Verificar se é colaborador ativo
  IF EXISTS (
    SELECT 1 FROM colaboradores_empresa 
    WHERE empresa_id = company_id 
    AND colaborador_user_id = auth.uid() 
    AND ativo = true 
    AND convite_aceito = true
  ) THEN
    RETURN TRUE;
  END IF;
  
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 10. FUNÇÃO PARA VERIFICAR SE USUÁRIO É ADMIN
-- =====================================================

CREATE OR REPLACE FUNCTION user_is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN (
    SELECT COALESCE(
      (auth.jwt() ->> 'user_metadata')::jsonb ->> 'role' = 'Administrador',
      false
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 11. POLÍTICAS DE SEGURANÇA PARA HORARIOS_COMERCIAIS
-- =====================================================

-- Remover políticas existentes se houver
DROP POLICY IF EXISTS "horarios_comerciais_policy" ON horarios_comerciais;

-- Proprietários podem gerenciar horários de sua empresa
CREATE POLICY "horarios_proprietario_all" ON horarios_comerciais
  FOR ALL USING (user_has_company_access(empresa_id));

-- Colaboradores podem ver horários da empresa onde trabalham
CREATE POLICY "horarios_colaborador_select" ON horarios_comerciais
  FOR SELECT USING (user_has_company_access(empresa_id));

-- Clientes podem ver horários de empresas ativas
CREATE POLICY "horarios_cliente_select" ON horarios_comerciais
  FOR SELECT USING (
    empresa_id IN (
      SELECT empresa_id FROM empresas 
      WHERE status = 'ativo'
    )
  );

-- =====================================================
-- 12. ÍNDICES PARA PERFORMANCE DE SEGURANÇA
-- =====================================================

-- Índices para melhorar performance das consultas RLS
CREATE INDEX IF NOT EXISTS idx_agendamentos_cliente_user_id ON agendamentos(cliente_user_id);
CREATE INDEX IF NOT EXISTS idx_agendamentos_colaborador_user_id ON agendamentos(colaborador_user_id);
CREATE INDEX IF NOT EXISTS idx_agendamentos_empresa_id ON agendamentos(empresa_id);
CREATE INDEX IF NOT EXISTS idx_pagamentos_agendamento_id ON pagamentos(agendamento_id);
CREATE INDEX IF NOT EXISTS idx_notificacoes_destinatario ON notificacoes(destinatario_user_id);
CREATE INDEX IF NOT EXISTS idx_colaboradores_empresa_user_id ON colaboradores_empresa(colaborador_user_id);
CREATE INDEX IF NOT EXISTS idx_colaboradores_empresa_ativo ON colaboradores_empresa(ativo, convite_aceito);

-- =====================================================
-- 13. TRIGGER PARA LOG DE SEGURANÇA
-- =====================================================

CREATE OR REPLACE FUNCTION log_security_event()
RETURNS TRIGGER AS $$
BEGIN
  -- Log de eventos de segurança sensíveis
  IF TG_OP = 'DELETE' AND TG_TABLE_NAME IN ('empresas', 'agendamentos', 'pagamentos') THEN
    INSERT INTO audit_log (
      table_name,
      operation,
      old_data,
      user_id,
      timestamp
    ) VALUES (
      TG_TABLE_NAME,
      TG_OP,
      row_to_json(OLD),
      auth.uid(),
      NOW()
    );
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Aplicar trigger às tabelas sensíveis
DROP TRIGGER IF EXISTS security_log_empresas ON empresas;
CREATE TRIGGER security_log_empresas
  AFTER DELETE ON empresas
  FOR EACH ROW EXECUTE FUNCTION log_security_event();

DROP TRIGGER IF EXISTS security_log_agendamentos ON agendamentos;
CREATE TRIGGER security_log_agendamentos
  AFTER DELETE ON agendamentos
  FOR EACH ROW EXECUTE FUNCTION log_security_event();

-- =====================================================
-- 14. VERIFICAÇÃO DE INTEGRIDADE
-- =====================================================

-- Função para verificar integridade das políticas RLS
CREATE OR REPLACE FUNCTION verify_rls_policies()
RETURNS TABLE(table_name TEXT, rls_enabled BOOLEAN, policy_count INTEGER) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.tablename::TEXT,
    t.rowsecurity,
    COUNT(p.policyname)::INTEGER
  FROM pg_tables t
  LEFT JOIN pg_policies p ON t.tablename = p.tablename
  WHERE t.schemaname = 'public'
  AND t.tablename NOT LIKE 'pg_%'
  GROUP BY t.tablename, t.rowsecurity
  ORDER BY t.tablename;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- COMENTÁRIOS E DOCUMENTAÇÃO
-- =====================================================

COMMENT ON FUNCTION user_has_company_access(INTEGER) IS 'Verifica se o usuário atual tem acesso a uma empresa específica';
COMMENT ON FUNCTION user_is_admin() IS 'Verifica se o usuário atual é administrador';
COMMENT ON FUNCTION verify_rls_policies() IS 'Retorna status das políticas RLS para todas as tabelas';
COMMENT ON FUNCTION log_security_event() IS 'Registra eventos de segurança sensíveis no log de auditoria';
