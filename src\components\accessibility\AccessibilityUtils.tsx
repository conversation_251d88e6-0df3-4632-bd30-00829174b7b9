'use client';

import React, { useEffect, useRef } from 'react';

// Componente para anúncios de screen reader
interface ScreenReaderAnnouncementProps {
  message: string;
  priority?: 'polite' | 'assertive';
  clearAfter?: number;
}

export function ScreenReaderAnnouncement({ 
  message, 
  priority = 'polite',
  clearAfter = 5000 
}: ScreenReaderAnnouncementProps) {
  const [currentMessage, setCurrentMessage] = React.useState(message);

  useEffect(() => {
    setCurrentMessage(message);
    
    if (clearAfter > 0) {
      const timer = setTimeout(() => {
        setCurrentMessage('');
      }, clearAfter);
      
      return () => clearTimeout(timer);
    }
  }, [message, clearAfter]);

  return (
    <div
      aria-live={priority}
      aria-atomic="true"
      className="sr-only"
      role="status"
    >
      {currentMessage}
    </div>
  );
}

// Hook para gerenciar foco
export function useFocusManagement() {
  const previousFocusRef = useRef<HTMLElement | null>(null);

  const saveFocus = () => {
    previousFocusRef.current = document.activeElement as HTMLElement;
  };

  const restoreFocus = () => {
    if (previousFocusRef.current && typeof previousFocusRef.current.focus === 'function') {
      previousFocusRef.current.focus();
    }
  };

  const focusElement = (selector: string) => {
    const element = document.querySelector(selector) as HTMLElement;
    if (element && typeof element.focus === 'function') {
      element.focus();
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  const trapFocus = (containerRef: React.RefObject<HTMLElement>) => {
    const container = containerRef.current;
    if (!container) return () => {};

    const focusableElements = container.querySelectorAll(
      'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"]):not([disabled])'
    );
    
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault();
            lastElement?.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault();
            firstElement?.focus();
          }
        }
      }
      
      if (e.key === 'Escape') {
        const closeButton = container.querySelector('[data-close]') as HTMLElement;
        if (closeButton && typeof closeButton.click === 'function') {
          closeButton.click();
        }
      }
    };

    container.addEventListener('keydown', handleKeyDown);
    
    // Focar no primeiro elemento focável
    if (firstElement && typeof firstElement.focus === 'function') {
      firstElement.focus();
    }

    return () => {
      container.removeEventListener('keydown', handleKeyDown);
    };
  };

  return { 
    saveFocus, 
    restoreFocus, 
    focusElement, 
    trapFocus 
  };
}

// Componente para melhorar contraste de cores
interface HighContrastTextProps {
  children: React.ReactNode;
  className?: string;
  level?: 'AA' | 'AAA';
}

export function HighContrastText({ 
  children, 
  className = '', 
  level = 'AA' 
}: HighContrastTextProps) {
  const contrastClass = level === 'AAA' 
    ? 'text-black dark:text-white' 
    : 'text-[var(--text-primary)]';
    
  return (
    <span className={`${contrastClass} ${className}`}>
      {children}
    </span>
  );
}

// Componente para indicar elementos obrigatórios
interface RequiredIndicatorProps {
  label: string;
  required?: boolean;
  className?: string;
}

export function RequiredIndicator({ 
  label, 
  required = false, 
  className = '' 
}: RequiredIndicatorProps) {
  return (
    <span className={className}>
      {label}
      {required && (
        <span 
          className="text-[var(--error)] ml-1" 
          aria-label="campo obrigatório"
          title="Este campo é obrigatório"
        >
          *
        </span>
      )}
    </span>
  );
}

// Hook para detectar preferências de acessibilidade
export function useAccessibilityPreferences() {
  const [preferences, setPreferences] = React.useState({
    reducedMotion: false,
    highContrast: false,
    largeText: false,
  });

  useEffect(() => {
    const checkPreferences = () => {
      setPreferences({
        reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
        highContrast: window.matchMedia('(prefers-contrast: high)').matches,
        largeText: window.matchMedia('(prefers-reduced-data: reduce)').matches,
      });
    };

    checkPreferences();

    const mediaQueries = [
      window.matchMedia('(prefers-reduced-motion: reduce)'),
      window.matchMedia('(prefers-contrast: high)'),
      window.matchMedia('(prefers-reduced-data: reduce)'),
    ];

    mediaQueries.forEach(mq => mq.addEventListener('change', checkPreferences));

    return () => {
      mediaQueries.forEach(mq => mq.removeEventListener('change', checkPreferences));
    };
  }, []);

  return preferences;
}

// Componente para pular para conteúdo principal
export function SkipToMainContent() {
  return (
    <a
      href="#main-content"
      className="
        sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 
        bg-[var(--primary)] text-[var(--text-on-primary)] px-4 py-2 rounded-md 
        font-medium z-50 focus:outline-none focus:ring-2 focus:ring-[var(--accent)] 
        focus:ring-offset-2 transition-all duration-200
      "
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          const mainContent = document.getElementById('main-content');
          if (mainContent) {
            mainContent.focus();
            mainContent.scrollIntoView({ behavior: 'smooth' });
          }
        }
      }}
    >
      Pular para o conteúdo principal
    </a>
  );
}

// Componente para indicar carregamento acessível
interface AccessibleLoadingProps {
  isLoading: boolean;
  loadingText?: string;
  children: React.ReactNode;
}

export function AccessibleLoading({ 
  isLoading, 
  loadingText = 'Carregando...', 
  children 
}: AccessibleLoadingProps) {
  return (
    <div aria-busy={isLoading} aria-live="polite">
      {isLoading ? (
        <div className="flex items-center gap-2">
          <div 
            className="animate-spin h-4 w-4 border-2 border-[var(--primary)] border-t-transparent rounded-full"
            aria-hidden="true"
          />
          <span>{loadingText}</span>
        </div>
      ) : (
        children
      )}
    </div>
  );
}
