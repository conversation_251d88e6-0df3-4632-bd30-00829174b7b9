'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { AgendamentoCompleto } from '@/types/agendamentos';
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface ModalDetalhesAgendamentoProps {
  agendamento: AgendamentoCompleto | null;
  isOpen: boolean;
  onClose: () => void;
  onConfirmar?: (id: number) => void;
  onRecusar?: (id: number) => void;
  onCancelar?: (id: number) => void;
  onConcluir?: (id: number) => void;
  onMarcarPago?: (id: number) => void;
  loading?: boolean;
  userRole?: string;
}

export function ModalDetalhesAgendamento({
  agendamento,
  isOpen,
  onClose,
  onConfirmar,
  onRecusar,
  onCancelar,
  onConcluir,
  onMarcarPago,
  loading = false,
  userRole
}: ModalDetalhesAgendamentoProps) {
  
  if (!isOpen || !agendamento) return null;

  const dataHoraInicio = parseISO(agendamento.data_hora_inicio);
  const dataHoraFim = parseISO(agendamento.data_hora_fim);
  const prazoConfirmacao = parseISO(agendamento.prazo_confirmacao);
  const dataCriacao = parseISO(agendamento.created_at);

  // Verificar permissões para ações
  const podeConfirmar = agendamento.status_agendamento === 'Pendente' &&
    (userRole === 'Proprietario' || userRole === 'Colaborador');
  
  const podeRecusar = agendamento.status_agendamento === 'Pendente' &&
    (userRole === 'Proprietario' || userRole === 'Colaborador');
  
  const podeCancelar = ['Pendente', 'Confirmado'].includes(agendamento.status_agendamento);
  
  const podeConcluir = agendamento.status_agendamento === 'Confirmado' &&
    (userRole === 'Proprietario' || userRole === 'Colaborador');
  
  const podeMarcarPago = agendamento.forma_pagamento === 'Local' && 
    agendamento.status_pagamento === 'Pendente' &&
    (userRole === 'Proprietario' || userRole === 'Colaborador');

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
      onKeyDown={(e) => {
        if (e.key === 'Escape') {
          onClose();
        }
      }}
    >
      <dialog
        open
        className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto border-0 p-0"
        aria-labelledby="modal-title"
      >
        <Card className="border-0 shadow-none">
          <CardHeader className="border-b">
            <div className="flex items-center justify-between">
              <CardTitle id="modal-title" className="text-xl font-bold">
                Detalhes do Agendamento
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700"
                aria-label="Fechar modal"
                data-close
              >
                <span aria-hidden="true">✕</span>
              </Button>
            </div>
          </CardHeader>

          <CardContent className="p-6 space-y-6">
            {/* Status e Código */}
            <div className="flex items-center justify-between">
              <div>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  agendamento.status_agendamento === 'Pendente' ? 'bg-yellow-100 text-yellow-800' :
                  agendamento.status_agendamento === 'Confirmado' ? 'bg-blue-100 text-blue-800' :
                  agendamento.status_agendamento === 'Concluido' ? 'bg-green-100 text-green-800' :
                  agendamento.status_agendamento === 'Cancelado' ? 'bg-gray-100 text-gray-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {agendamento.status_agendamento}
                </span>
              </div>
              <div className="text-right">
                <div className="text-sm text-[var(--text-secondary)]">Código</div>
                <div className="font-mono font-bold text-lg">
                  {agendamento.codigo_confirmacao}
                </div>
              </div>
            </div>

            {/* Informações do Serviço */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold text-[var(--text-primary)] mb-3">Serviço</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-[var(--text-secondary)]">Nome:</span>
                  <span className="font-medium">{agendamento.servico.nome_servico}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[var(--text-secondary)]">Categoria:</span>
                  <span>{agendamento.servico.categoria}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[var(--text-secondary)]">Duração:</span>
                  <span>{agendamento.servico.duracao_minutos} minutos</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[var(--text-secondary)]">Preço:</span>
                  <span className="font-bold text-[var(--primary)]">
                    R$ {agendamento.servico.preco.toFixed(2)}
                  </span>
                </div>
                {agendamento.servico.descricao && (
                  <div>
                    <span className="text-[var(--text-secondary)]">Descrição:</span>
                    <p className="mt-1 text-sm">{agendamento.servico.descricao}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Data e Horário */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-[var(--text-primary)] mb-3">Data e Horário</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-[var(--text-secondary)]">Data:</span>
                  <span className="font-medium">
                    {format(dataHoraInicio, "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[var(--text-secondary)]">Horário:</span>
                  <span className="font-medium">
                    {format(dataHoraInicio, 'HH:mm')} - {format(dataHoraFim, 'HH:mm')}
                  </span>
                </div>
                {agendamento.status_agendamento === 'Pendente' && (
                  <div className="flex justify-between">
                    <span className="text-[var(--text-secondary)]">Prazo confirmação:</span>
                    <span className="font-medium text-orange-600">
                      {format(prazoConfirmacao, "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Informações do Cliente */}
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-semibold text-[var(--text-primary)] mb-3">Cliente</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-[var(--text-secondary)]">Nome:</span>
                  <span className="font-medium">{agendamento.cliente?.name || 'Não informado'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[var(--text-secondary)]">Email:</span>
                  <span>{agendamento.cliente?.email || 'Não informado'}</span>
                </div>
                {agendamento.cliente?.phone && (
                  <div className="flex justify-between">
                    <span className="text-[var(--text-secondary)]">Telefone:</span>
                    <span>{agendamento.cliente.phone}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Informações do Colaborador */}
            {userRole === 'Proprietario' && (
              <div className="bg-purple-50 p-4 rounded-lg">
                <h3 className="font-semibold text-[var(--text-primary)] mb-3">Colaborador</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-[var(--text-secondary)]">Nome:</span>
                    <span className="font-medium">{agendamento.colaborador?.name || 'Não informado'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-[var(--text-secondary)]">Email:</span>
                    <span>{agendamento.colaborador?.email || 'Não informado'}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Pagamento */}
            <div className="bg-yellow-50 p-4 rounded-lg">
              <h3 className="font-semibold text-[var(--text-primary)] mb-3">Pagamento</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-[var(--text-secondary)]">Forma:</span>
                  <span className="font-medium">{agendamento.forma_pagamento}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[var(--text-secondary)]">Status:</span>
                  <span className={`font-medium ${
                    agendamento.status_pagamento === 'Pago' ? 'text-green-600' :
                    agendamento.status_pagamento === 'Pendente' ? 'text-orange-600' :
                    agendamento.status_pagamento === 'Reembolsado' ? 'text-blue-600' :
                    'text-red-600'
                  }`}>
                    {agendamento.status_pagamento}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[var(--text-secondary)]">Valor Total:</span>
                  <span className="font-bold text-lg text-[var(--primary)]">
                    R$ {agendamento.valor_total.toFixed(2)}
                  </span>
                </div>
                {agendamento.valor_desconto && agendamento.valor_desconto > 0 && (
                  <div className="flex justify-between">
                    <span className="text-[var(--text-secondary)]">Desconto:</span>
                    <span className="font-medium text-green-600">
                      - R$ {agendamento.valor_desconto.toFixed(2)}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Observações */}
            {agendamento.observacoes_cliente && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold text-[var(--text-primary)] mb-3">Observações do Cliente</h3>
                <p className="text-[var(--text-secondary)]">{agendamento.observacoes_cliente}</p>
              </div>
            )}

            {/* Informações Técnicas */}
            <div className="text-xs text-[var(--text-secondary)] space-y-1">
              <div>ID do Agendamento: {agendamento.agendamento_id}</div>
              <div>Criado em: {format(dataCriacao, "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}</div>
              {agendamento.stripe_payment_intent_id && (
                <div>Payment Intent: {agendamento.stripe_payment_intent_id}</div>
              )}
            </div>

            {/* Ações */}
            <div className="flex flex-wrap gap-3 pt-4 border-t">
              {podeConfirmar && onConfirmar && (
                <Button
                  onClick={() => onConfirmar(agendamento.agendamento_id)}
                  disabled={loading}
                  className="bg-green-600 hover:bg-green-700"
                >
                  ✅ Confirmar
                </Button>
              )}
              
              {podeRecusar && onRecusar && (
                <Button
                  variant="outline"
                  onClick={() => onRecusar(agendamento.agendamento_id)}
                  disabled={loading}
                  className="border-red-300 text-red-600 hover:bg-red-50"
                >
                  ❌ Recusar
                </Button>
              )}
              
              {podeConcluir && onConcluir && (
                <Button
                  onClick={() => onConcluir(agendamento.agendamento_id)}
                  disabled={loading}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  ✨ Concluir
                </Button>
              )}
              
              {podeMarcarPago && onMarcarPago && (
                <Button
                  variant="outline"
                  onClick={() => onMarcarPago(agendamento.agendamento_id)}
                  disabled={loading}
                  className="border-green-300 text-green-600 hover:bg-green-50"
                >
                  💰 Marcar como Pago
                </Button>
              )}
              
              {podeCancelar && onCancelar && (
                <Button
                  variant="outline"
                  onClick={() => onCancelar(agendamento.agendamento_id)}
                  disabled={loading}
                  className="border-gray-300 text-gray-600 hover:bg-gray-50"
                >
                  🚫 Cancelar
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </dialog>
    </div>
  );
}
