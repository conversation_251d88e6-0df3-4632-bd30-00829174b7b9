import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { PreferenciasNotificacao } from '@/types/notifications';

/**
 * GET - Obter preferências de notificação do usuário
 */
export async function GET() {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'Usuário não autenticado'
      }, { status: 401 });
    }

    // Buscar preferências do usuário
    const { data: preferencias, error } = await supabase
      .from('preferencias_notificacao')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = not found
      console.error('Erro ao buscar preferências:', error);
      return NextResponse.json({
        success: false,
        error: 'Erro ao buscar preferências'
      }, { status: 500 });
    }

    // Se não encontrou, retornar preferências padrão
    if (!preferencias) {
      const preferenciasDefault: Partial<PreferenciasNotificacao> = {
        user_id: user.id,
        email_enabled: true,
        sms_enabled: true,
        push_enabled: true,
        tipos_habilitados: [
          'novo_agendamento',
          'agendamento_confirmado',
          'agendamento_recusado',
          'agendamento_cancelado',
          'lembrete_confirmacao',
          'lembrete_agendamento',
          'pagamento_confirmado'
        ]
      };

      return NextResponse.json({
        success: true,
        data: preferenciasDefault
      });
    }

    return NextResponse.json({
      success: true,
      data: preferencias
    });

  } catch (error) {
    console.error('Erro na API de preferências:', error);
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 });
  }
}

/**
 * PUT - Atualizar preferências de notificação do usuário
 */
export async function PUT(request: Request) {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'Usuário não autenticado'
      }, { status: 401 });
    }

    const body = await request.json();
    const { email_enabled, sms_enabled, push_enabled, tipos_habilitados } = body;

    // Validações
    if (typeof email_enabled !== 'boolean' || 
        typeof sms_enabled !== 'boolean' || 
        typeof push_enabled !== 'boolean') {
      return NextResponse.json({
        success: false,
        error: 'Valores booleanos inválidos para canais'
      }, { status: 400 });
    }

    if (!Array.isArray(tipos_habilitados)) {
      return NextResponse.json({
        success: false,
        error: 'tipos_habilitados deve ser um array'
      }, { status: 400 });
    }

    // Tipos válidos de notificação
    const tiposValidos = [
      'novo_agendamento',
      'agendamento_confirmado',
      'agendamento_recusado',
      'agendamento_cancelado',
      'lembrete_confirmacao',
      'lembrete_agendamento',
      'pagamento_confirmado'
    ];

    // Validar tipos habilitados
    const tiposInvalidos = tipos_habilitados.filter(tipo => !tiposValidos.includes(tipo));
    if (tiposInvalidos.length > 0) {
      return NextResponse.json({
        success: false,
        error: `Tipos de notificação inválidos: ${tiposInvalidos.join(', ')}`
      }, { status: 400 });
    }

    // Atualizar ou inserir preferências
    const { data: preferencias, error } = await supabase
      .from('preferencias_notificacao')
      .upsert({
        user_id: user.id,
        email_enabled,
        sms_enabled,
        push_enabled,
        tipos_habilitados
      })
      .select()
      .single();

    if (error) {
      console.error('Erro ao atualizar preferências:', error);
      return NextResponse.json({
        success: false,
        error: 'Erro ao atualizar preferências'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: preferencias,
      message: 'Preferências atualizadas com sucesso'
    });

  } catch (error) {
    console.error('Erro na API de preferências:', error);
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 });
  }
}

/**
 * POST - Resetar preferências para padrão
 */
export async function POST() {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'Usuário não autenticado'
      }, { status: 401 });
    }

    // Resetar para preferências padrão
    const { data: preferencias, error } = await supabase
      .from('preferencias_notificacao')
      .upsert({
        user_id: user.id,
        email_enabled: true,
        sms_enabled: true,
        push_enabled: true,
        tipos_habilitados: [
          'novo_agendamento',
          'agendamento_confirmado',
          'agendamento_recusado',
          'agendamento_cancelado',
          'lembrete_confirmacao',
          'lembrete_agendamento',
          'pagamento_confirmado'
        ]
      })
      .select()
      .single();

    if (error) {
      console.error('Erro ao resetar preferências:', error);
      return NextResponse.json({
        success: false,
        error: 'Erro ao resetar preferências'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: preferencias,
      message: 'Preferências resetadas para padrão'
    });

  } catch (error) {
    console.error('Erro na API de preferências:', error);
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 });
  }
}
