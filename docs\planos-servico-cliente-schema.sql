-- =====================================================
-- SCHEMA PARA PLANOS DE ASSINATURA DE SERVIÇOS
-- Funcionalidade Premium - Tarefa #23
-- =====================================================

-- Criar tabela planos_servico_cliente
CREATE TABLE IF NOT EXISTS planos_servico_cliente (
  plano_cliente_id SERIAL PRIMARY KEY,
  cliente_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  empresa_id INTEGER REFERENCES empresas(empresa_id) ON DELETE CASCADE,
  servico_id INTEGER REFERENCES servicos(servico_id) ON DELETE CASCADE,
  nome_plano_empresa VARCHAR(255) NOT NULL,
  descricao_plano TEXT,
  preco_mensal_assinatura DECIMAL(10,2) NOT NULL CHECK (preco_mensal_assinatura >= 0),
  limite_usos_mes INTEGER CHECK (limite_usos_mes > 0), -- NULL = ilimitado
  usos_consumidos_ciclo_atual INTEGER DEFAULT 0 CHECK (usos_consumidos_ciclo_atual >= 0),
  stripe_subscription_id VARCHAR(255),
  status VARCHAR(50) DEFAULT 'ativa' CHECK (status IN ('ativa', 'pausada', 'cancelada', 'expirada', 'trial')),
  data_inicio_ciclo TIMESTAMP WITH TIME ZONE NOT NULL,
  data_fim_ciclo TIMESTAMP WITH TIME ZONE NOT NULL,
  data_proxima_cobranca TIMESTAMP WITH TIME ZONE,
  data_cancelamento TIMESTAMP WITH TIME ZONE,
  motivo_cancelamento TEXT,
  ativo BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT unique_cliente_servico_ativo UNIQUE (cliente_user_id, servico_id, ativo) DEFERRABLE INITIALLY DEFERRED,
  CONSTRAINT valid_cycle_dates CHECK (data_fim_ciclo > data_inicio_ciclo),
  CONSTRAINT valid_usage_limit CHECK (
    limite_usos_mes IS NULL OR 
    usos_consumidos_ciclo_atual <= limite_usos_mes
  )
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_planos_cliente_user_id ON planos_servico_cliente(cliente_user_id);
CREATE INDEX IF NOT EXISTS idx_planos_empresa_id ON planos_servico_cliente(empresa_id);
CREATE INDEX IF NOT EXISTS idx_planos_servico_id ON planos_servico_cliente(servico_id);
CREATE INDEX IF NOT EXISTS idx_planos_status ON planos_servico_cliente(status);
CREATE INDEX IF NOT EXISTS idx_planos_ativo ON planos_servico_cliente(ativo);
CREATE INDEX IF NOT EXISTS idx_planos_stripe_subscription ON planos_servico_cliente(stripe_subscription_id);
CREATE INDEX IF NOT EXISTS idx_planos_ciclo_fim ON planos_servico_cliente(data_fim_ciclo);
CREATE INDEX IF NOT EXISTS idx_planos_proxima_cobranca ON planos_servico_cliente(data_proxima_cobranca);

-- Trigger para atualizar updated_at
CREATE OR REPLACE FUNCTION update_planos_servico_cliente_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_planos_servico_cliente_updated_at
  BEFORE UPDATE ON planos_servico_cliente
  FOR EACH ROW
  EXECUTE FUNCTION update_planos_servico_cliente_updated_at();

-- Função para resetar usos no início de novo ciclo
CREATE OR REPLACE FUNCTION reset_cycle_usage()
RETURNS TRIGGER AS $$
BEGIN
  -- Se a data de início do ciclo mudou, resetar usos
  IF OLD.data_inicio_ciclo != NEW.data_inicio_ciclo THEN
    NEW.usos_consumidos_ciclo_atual = 0;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_reset_cycle_usage
  BEFORE UPDATE ON planos_servico_cliente
  FOR EACH ROW
  EXECUTE FUNCTION reset_cycle_usage();

-- =====================================================
-- POLÍTICAS RLS (Row Level Security)
-- =====================================================

-- Habilitar RLS
ALTER TABLE planos_servico_cliente ENABLE ROW LEVEL SECURITY;

-- Clientes podem ver e gerenciar seus próprios planos
CREATE POLICY "planos_cliente_select" ON planos_servico_cliente
  FOR SELECT USING (cliente_user_id = auth.uid());

CREATE POLICY "planos_cliente_insert" ON planos_servico_cliente
  FOR INSERT WITH CHECK (cliente_user_id = auth.uid());

CREATE POLICY "planos_cliente_update" ON planos_servico_cliente
  FOR UPDATE USING (cliente_user_id = auth.uid())
  WITH CHECK (cliente_user_id = auth.uid());

-- Proprietários podem ver planos de sua empresa
CREATE POLICY "planos_proprietario_select" ON planos_servico_cliente
  FOR SELECT USING (
    empresa_id IN (
      SELECT empresa_id FROM empresas 
      WHERE proprietario_user_id = auth.uid()
    )
  );

-- Proprietários podem criar planos para sua empresa (apenas Premium)
CREATE POLICY "planos_proprietario_insert" ON planos_servico_cliente
  FOR INSERT WITH CHECK (
    empresa_id IN (
      SELECT e.empresa_id FROM empresas e
      JOIN assinaturas_saas_empresas ase ON e.empresa_id = ase.empresa_id
      JOIN planos_saas ps ON ase.plano_saas_id = ps.plano_saas_id
      WHERE e.proprietario_user_id = auth.uid()
      AND ps.nome_plano = 'Premium'
      AND ase.status_assinatura = 'ativa'
    )
  );

-- Proprietários podem atualizar planos de sua empresa
CREATE POLICY "planos_proprietario_update" ON planos_servico_cliente
  FOR UPDATE USING (
    empresa_id IN (
      SELECT empresa_id FROM empresas 
      WHERE proprietario_user_id = auth.uid()
    )
  );

-- Colaboradores podem ver planos das empresas onde trabalham
CREATE POLICY "planos_colaborador_select" ON planos_servico_cliente
  FOR SELECT USING (
    empresa_id IN (
      SELECT empresa_id FROM colaboradores_empresa 
      WHERE colaborador_user_id = auth.uid() 
      AND ativo = true 
      AND convite_aceito = true
    )
  );

-- Administradores têm acesso total (via service role)

-- =====================================================
-- FUNÇÕES AUXILIARES
-- =====================================================

-- Função para verificar se cliente pode usar serviço (tem assinatura ativa)
CREATE OR REPLACE FUNCTION cliente_pode_usar_servico(
  p_cliente_user_id UUID,
  p_servico_id INTEGER
)
RETURNS BOOLEAN AS $$
DECLARE
  plano_ativo RECORD;
BEGIN
  -- Buscar plano ativo para o serviço
  SELECT * INTO plano_ativo
  FROM planos_servico_cliente
  WHERE cliente_user_id = p_cliente_user_id
    AND servico_id = p_servico_id
    AND status = 'ativa'
    AND ativo = true
    AND data_fim_ciclo > NOW();
  
  -- Se não encontrou plano ativo, retorna false
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- Se tem limite de usos, verificar se ainda pode usar
  IF plano_ativo.limite_usos_mes IS NOT NULL THEN
    RETURN plano_ativo.usos_consumidos_ciclo_atual < plano_ativo.limite_usos_mes;
  END IF;
  
  -- Se não tem limite, pode usar
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para consumir uso de assinatura
CREATE OR REPLACE FUNCTION consumir_uso_assinatura(
  p_cliente_user_id UUID,
  p_servico_id INTEGER
)
RETURNS BOOLEAN AS $$
DECLARE
  plano_ativo RECORD;
BEGIN
  -- Buscar plano ativo para o serviço
  SELECT * INTO plano_ativo
  FROM planos_servico_cliente
  WHERE cliente_user_id = p_cliente_user_id
    AND servico_id = p_servico_id
    AND status = 'ativa'
    AND ativo = true
    AND data_fim_ciclo > NOW()
  FOR UPDATE; -- Lock para evitar race conditions
  
  -- Se não encontrou plano ativo, retorna false
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- Se tem limite de usos, verificar se ainda pode usar
  IF plano_ativo.limite_usos_mes IS NOT NULL THEN
    IF plano_ativo.usos_consumidos_ciclo_atual >= plano_ativo.limite_usos_mes THEN
      RETURN FALSE;
    END IF;
  END IF;
  
  -- Incrementar uso consumido
  UPDATE planos_servico_cliente
  SET usos_consumidos_ciclo_atual = usos_consumidos_ciclo_atual + 1,
      updated_at = NOW()
  WHERE plano_cliente_id = plano_ativo.plano_cliente_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para obter desconto de assinatura
CREATE OR REPLACE FUNCTION obter_desconto_assinatura(
  p_cliente_user_id UUID,
  p_servico_id INTEGER,
  p_preco_original DECIMAL
)
RETURNS DECIMAL AS $$
DECLARE
  plano_ativo RECORD;
  desconto DECIMAL DEFAULT 0;
BEGIN
  -- Buscar plano ativo para o serviço
  SELECT * INTO plano_ativo
  FROM planos_servico_cliente psc
  JOIN servicos s ON psc.servico_id = s.servico_id
  WHERE psc.cliente_user_id = p_cliente_user_id
    AND psc.servico_id = p_servico_id
    AND psc.status = 'ativa'
    AND psc.ativo = true
    AND psc.data_fim_ciclo > NOW();
  
  -- Se não encontrou plano ativo, sem desconto
  IF NOT FOUND THEN
    RETURN 0;
  END IF;
  
  -- Se tem limite de usos e já esgotou, sem desconto
  IF plano_ativo.limite_usos_mes IS NOT NULL THEN
    IF plano_ativo.usos_consumidos_ciclo_atual >= plano_ativo.limite_usos_mes THEN
      RETURN 0;
    END IF;
  END IF;
  
  -- Calcular desconto (exemplo: 20% para assinantes)
  -- Pode ser configurável por plano no futuro
  desconto = p_preco_original * 0.20;
  
  RETURN desconto;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- DADOS INICIAIS (EXEMPLOS)
-- =====================================================

-- Comentário: Dados de exemplo serão inseridos via aplicação
-- após a empresa configurar seus planos de assinatura
