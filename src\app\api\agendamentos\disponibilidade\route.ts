import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import {
  HorarioDisponivel,
  ColaboradorDisponivel
} from '@/types/disponibilidade';

// GET - Verificar disponibilidade de horários
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);

    // Extrair parâmetros da query
    const empresa_id = searchParams.get('empresa_id');
    const servico_id = searchParams.get('servico_id');
    const colaborador_user_id = searchParams.get('colaborador_user_id');
    const data_inicio = searchParams.get('data_inicio');
    const data_fim = searchParams.get('data_fim');

    // Validar parâmetros obrigatórios
    if (!empresa_id || !servico_id || !data_inicio || !data_fim) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Parâmetros obrigatórios: empresa_id, servico_id, data_inicio, data_fim' 
        },
        { status: 400 }
      );
    }

    // Validar formato das datas
    const dataInicioObj = new Date(data_inicio);
    const dataFimObj = new Date(data_fim);
    
    if (isNaN(dataInicioObj.getTime()) || isNaN(dataFimObj.getTime())) {
      return NextResponse.json(
        { success: false, error: 'Formato de data inválido. Use YYYY-MM-DD' },
        { status: 400 }
      );
    }

    // Verificar se a empresa existe e está ativa
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select('empresa_id, nome_empresa, horario_funcionamento, status')
      .eq('empresa_id', empresa_id)
      .eq('status', 'ativo')
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada ou inativa' },
        { status: 404 }
      );
    }

    // Verificar se o serviço existe e está ativo
    const { data: servico, error: servicoError } = await supabase
      .from('servicos')
      .select('servico_id, nome_servico, duracao_minutos, preco, ativo')
      .eq('servico_id', servico_id)
      .eq('empresa_id', empresa_id)
      .eq('ativo', true)
      .single();

    if (servicoError || !servico) {
      return NextResponse.json(
        { success: false, error: 'Serviço não encontrado ou inativo' },
        { status: 404 }
      );
    }

    // Buscar colaboradores que podem realizar o serviço
    let colaboradoresQuery = supabase
      .from('colaboradores_empresa')
      .select(`
        colaborador_user_id,
        ativo_como_prestador,
        horarios_trabalho_individual,
        auth_users:colaborador_user_id (
          raw_user_meta_data
        ),
        colaborador_servicos!inner (
          servico_id,
          ativo
        )
      `)
      .eq('empresa_id', empresa_id)
      .eq('ativo', true)
      .eq('ativo_como_prestador', true)
      .eq('colaborador_servicos.servico_id', servico_id)
      .eq('colaborador_servicos.ativo', true);

    // Se colaborador específico foi informado, filtrar por ele
    if (colaborador_user_id) {
      colaboradoresQuery = colaboradoresQuery.eq('colaborador_user_id', colaborador_user_id);
    }

    const { data: colaboradores, error: colaboradoresError } = await colaboradoresQuery;

    if (colaboradoresError) {
      console.error('Erro ao buscar colaboradores:', colaboradoresError);
      return NextResponse.json(
        { success: false, error: 'Erro ao buscar colaboradores disponíveis' },
        { status: 500 }
      );
    }

    if (!colaboradores || colaboradores.length === 0) {
      return NextResponse.json(
        { 
          success: true, 
          data: {
            empresa_id: Number(empresa_id),
            servico,
            colaboradores_disponiveis: [],
            horarios_disponiveis: [],
            periodo_consultado: { data_inicio, data_fim },
            message: 'Nenhum colaborador disponível para este serviço'
          }
        }
      );
    }

    // Buscar agendamentos existentes no período
    const { data: agendamentosExistentes, error: agendamentosError } = await supabase
      .from('agendamentos')
      .select('agendamento_id, colaborador_user_id, data_hora_inicio, data_hora_fim, status_agendamento')
      .eq('empresa_id', empresa_id)
      .in('status_agendamento', ['Pendente', 'Confirmado'])
      .gte('data_hora_inicio', data_inicio)
      .lte('data_hora_inicio', data_fim + ' 23:59:59');

    if (agendamentosError) {
      console.error('Erro ao buscar agendamentos:', agendamentosError);
      return NextResponse.json(
        { success: false, error: 'Erro ao verificar agendamentos existentes' },
        { status: 500 }
      );
    }

    // Processar disponibilidade para cada colaborador
    const colaboradoresDisponiveis: ColaboradorDisponivel[] = [];
    const todosHorariosDisponiveis: HorarioDisponivel[] = [];

    for (const colaborador of colaboradores) {
      const horariosColaborador = calcularHorariosDisponiveis({
        colaborador,
        empresa,
        servico,
        dataInicio: dataInicioObj,
        dataFim: dataFimObj,
        agendamentosExistentes: agendamentosExistentes || []
      });

      const authUser = Array.isArray(colaborador.auth_users) ? colaborador.auth_users[0] : colaborador.auth_users;
      const colaboradorDisponivel: ColaboradorDisponivel = {
        colaborador_user_id: colaborador.colaborador_user_id,
        name: authUser?.raw_user_meta_data?.name || 'Colaborador',
        email: authUser?.raw_user_meta_data?.email || '',
        pode_realizar_servico: true,
        horarios_disponiveis: horariosColaborador
      };

      colaboradoresDisponiveis.push(colaboradorDisponivel);
      todosHorariosDisponiveis.push(...horariosColaborador);
    }

    // Remover horários duplicados e ordenar
    const horariosUnicos = todosHorariosDisponiveis
      .filter((horario, index, array) => 
        array.findIndex(h => h.data_hora_inicio === horario.data_hora_inicio) === index
      )
      .sort((a, b) => new Date(a.data_hora_inicio).getTime() - new Date(b.data_hora_inicio).getTime());

    return NextResponse.json({
      success: true,
      data: {
        empresa_id: Number(empresa_id),
        servico,
        colaboradores_disponiveis: colaboradoresDisponiveis,
        horarios_disponiveis: horariosUnicos,
        periodo_consultado: { data_inicio, data_fim }
      }
    });

  } catch (error: any) {
    console.error('Erro geral na API de disponibilidade:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// Função auxiliar para calcular horários disponíveis
function calcularHorariosDisponiveis({
  colaborador,
  empresa,
  servico,
  dataInicio,
  dataFim,
  agendamentosExistentes
}: {
  colaborador: any;
  empresa: any;
  servico: any;
  dataInicio: Date;
  dataFim: Date;
  agendamentosExistentes: any[];
}): HorarioDisponivel[] {
  const horariosDisponiveis: HorarioDisponivel[] = [];
  const duracaoServico = servico.duracao_minutos;
  const intervaloSlot = 30; // Intervalo de 30 minutos entre slots

  // Iterar por cada dia no período
  const dataAtual = new Date(dataInicio);
  while (dataAtual <= dataFim) {
    const diaSemana = dataAtual.getDay(); // 0=Domingo, 6=Sábado
    const dataStr = dataAtual.toISOString().split('T')[0];

    // Verificar horário de funcionamento da empresa
    const horarioEmpresa = empresa.horario_funcionamento;
    const diasSemana = ['domingo', 'segunda', 'terca', 'quarta', 'quinta', 'sexta', 'sabado'];
    const nomeDay = diasSemana[diaSemana];
    
    if (horarioEmpresa && horarioEmpresa[nomeDay] && horarioEmpresa[nomeDay].ativo) {
      const horarioDia = horarioEmpresa[nomeDay];
      const abertura = horarioDia.abertura;
      const fechamento = horarioDia.fechamento;

      // Gerar slots de horário
      const slots = gerarSlotsHorario(dataStr, abertura, fechamento, intervaloSlot, duracaoServico);
      
      // Filtrar slots disponíveis (sem conflitos)
      for (const slot of slots) {
        const temConflito = verificarConflito(slot, colaborador.colaborador_user_id, agendamentosExistentes);
        
        if (!temConflito) {
          horariosDisponiveis.push({
            data_hora_inicio: slot.data_hora_inicio,
            data_hora_fim: slot.data_hora_fim,
            colaborador_user_id: colaborador.colaborador_user_id,
            colaborador_nome: (Array.isArray(colaborador.auth_users) ? colaborador.auth_users[0] : colaborador.auth_users)?.raw_user_meta_data?.name || 'Colaborador',
            disponivel: true
          });
        }
      }
    }

    // Avançar para o próximo dia
    dataAtual.setDate(dataAtual.getDate() + 1);
  }

  return horariosDisponiveis;
}

// Função auxiliar para gerar slots de horário
function gerarSlotsHorario(data: string, abertura: string, fechamento: string, intervalo: number, duracao: number) {
  const slots = [];
  const [horaAbertura, minutoAbertura] = abertura.split(':').map(Number);
  const [horaFechamento, minutoFechamento] = fechamento.split(':').map(Number);

  let horaAtual = horaAbertura;
  let minutoAtual = minutoAbertura;

  while (true) {
    const inicioSlot = new Date(`${data}T${String(horaAtual).padStart(2, '0')}:${String(minutoAtual).padStart(2, '0')}:00`);
    const fimSlot = new Date(inicioSlot.getTime() + duracao * 60000);

    // Verificar se o slot cabe no horário de funcionamento
    const horaFimSlot = fimSlot.getHours();
    const minutoFimSlot = fimSlot.getMinutes();
    
    if (horaFimSlot > horaFechamento || (horaFimSlot === horaFechamento && minutoFimSlot > minutoFechamento)) {
      break;
    }

    slots.push({
      data_hora_inicio: inicioSlot.toISOString(),
      data_hora_fim: fimSlot.toISOString()
    });

    // Avançar para o próximo slot
    minutoAtual += intervalo;
    if (minutoAtual >= 60) {
      horaAtual += Math.floor(minutoAtual / 60);
      minutoAtual = minutoAtual % 60;
    }
  }

  return slots;
}

// Função auxiliar para verificar conflitos
function verificarConflito(slot: any, colaboradorId: string, agendamentos: any[]): boolean {
  const inicioSlot = new Date(slot.data_hora_inicio);
  const fimSlot = new Date(slot.data_hora_fim);

  return agendamentos.some(agendamento => {
    if (agendamento.colaborador_user_id !== colaboradorId) return false;

    const inicioAgendamento = new Date(agendamento.data_hora_inicio);
    const fimAgendamento = new Date(agendamento.data_hora_fim);

    // Verificar sobreposição
    return (inicioSlot < fimAgendamento && fimSlot > inicioAgendamento);
  });
}
