import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/client';
import { AtualizarPlanoAssinaturaData, LIMITES_PLANOS_ASSINATURA } from '@/types/planosAssinatura';

// GET - Obter template específico de plano de assinatura
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient();
    const { id } = await params;
    const templateId = parseInt(id);

    if (isNaN(templateId)) {
      return NextResponse.json({ success: false, error: 'ID inválido' }, { status: 400 });
    }

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ success: false, error: 'Não autorizado' }, { status: 401 });
    }

    // Buscar template com verificação de propriedade
    const { data: template, error: templateError } = await supabase
      .from('templates_planos_assinatura')
      .select(`
        *,
        servicos(
          nome_servico,
          descricao,
          preco,
          duracao_minutos,
          categoria
        ),
        empresas!inner(
          nome_empresa,
          proprietario_user_id
        )
      `)
      .eq('template_id', templateId)
      .eq('empresas.proprietario_user_id', user.id)
      .single();

    if (templateError || !template) {
      return NextResponse.json({ 
        success: false, 
        error: 'Template não encontrado ou não autorizado' 
      }, { status: 404 });
    }

    // Buscar estatísticas do template
    const { data: estatisticas } = await supabase
      .from('planos_servico_cliente')
      .select('status')
      .eq('empresa_id', template.empresa_id)
      .eq('servico_id', template.servico_id);

    const totalAssinantes = estatisticas?.length || 0;
    const assinantesAtivos = estatisticas?.filter(e => e.status === 'ativa').length || 0;

    return NextResponse.json({
      success: true,
      data: {
        ...template,
        estatisticas: {
          total_assinantes: totalAssinantes,
          assinantes_ativos: assinantesAtivos
        }
      }
    });

  } catch (error) {
    console.error('Erro ao buscar template:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Erro interno do servidor' 
    }, { status: 500 });
  }
}

// PUT - Atualizar template de plano de assinatura
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient();
    const { id } = await params;
    const templateId = parseInt(id);
    const body: AtualizarPlanoAssinaturaData = await request.json();

    if (isNaN(templateId)) {
      return NextResponse.json({ success: false, error: 'ID inválido' }, { status: 400 });
    }

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ success: false, error: 'Não autorizado' }, { status: 401 });
    }

    // Validar dados de entrada
    const validacao = validarDadosAtualizacao(body);
    if (!validacao.valido) {
      return NextResponse.json({ 
        success: false, 
        error: 'Dados inválidos',
        detalhes: validacao.erros 
      }, { status: 400 });
    }

    // Verificar se o template existe e pertence ao usuário
    const { data: templateExistente, error: templateError } = await supabase
      .from('templates_planos_assinatura')
      .select(`
        template_id,
        empresa_id,
        servico_id,
        empresas!inner(proprietario_user_id)
      `)
      .eq('template_id', templateId)
      .eq('empresas.proprietario_user_id', user.id)
      .single();

    if (templateError || !templateExistente) {
      return NextResponse.json({ 
        success: false, 
        error: 'Template não encontrado ou não autorizado' 
      }, { status: 404 });
    }

    // Preparar dados para atualização
    const dadosAtualizacao: any = {
      updated_at: new Date().toISOString()
    };

    if (body.nome_plano_empresa !== undefined) {
      dadosAtualizacao.nome_plano = body.nome_plano_empresa;
    }

    if (body.descricao_plano !== undefined) {
      dadosAtualizacao.descricao = body.descricao_plano;
    }

    if (body.preco_mensal_assinatura !== undefined) {
      dadosAtualizacao.preco_mensal = body.preco_mensal_assinatura;
    }

    if (body.limite_usos_mes !== undefined) {
      dadosAtualizacao.limite_usos_mes = body.limite_usos_mes;
      
      // Atualizar benefícios baseado no limite
      const descontoPercentual = 20; // Pode ser configurável
      dadosAtualizacao.beneficios = [
        `${descontoPercentual}% de desconto em cada agendamento`,
        body.limite_usos_mes ? `Até ${body.limite_usos_mes} usos por mês` : 'Usos ilimitados por mês',
        'Prioridade no agendamento',
        'Cancelamento flexível'
      ];
    }

    // Atualizar template
    const { data: templateAtualizado, error: atualizarError } = await supabase
      .from('templates_planos_assinatura')
      .update(dadosAtualizacao)
      .eq('template_id', templateId)
      .select(`
        *,
        servicos(
          nome_servico,
          descricao,
          preco,
          duracao_minutos,
          categoria
        )
      `)
      .single();

    if (atualizarError) {
      console.error('Erro ao atualizar template:', atualizarError);
      return NextResponse.json({ 
        success: false, 
        error: 'Erro ao atualizar plano de assinatura' 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: templateAtualizado,
      message: 'Plano de assinatura atualizado com sucesso'
    });

  } catch (error) {
    console.error('Erro ao atualizar template:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Erro interno do servidor' 
    }, { status: 500 });
  }
}

// DELETE - Excluir template de plano de assinatura
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient();
    const { id } = await params;
    const templateId = parseInt(id);

    if (isNaN(templateId)) {
      return NextResponse.json({ success: false, error: 'ID inválido' }, { status: 400 });
    }

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ success: false, error: 'Não autorizado' }, { status: 401 });
    }

    // Verificar se o template existe e pertence ao usuário
    const { data: templateExistente, error: templateError } = await supabase
      .from('templates_planos_assinatura')
      .select(`
        template_id,
        empresa_id,
        servico_id,
        empresas!inner(proprietario_user_id)
      `)
      .eq('template_id', templateId)
      .eq('empresas.proprietario_user_id', user.id)
      .single();

    if (templateError || !templateExistente) {
      return NextResponse.json({ 
        success: false, 
        error: 'Template não encontrado ou não autorizado' 
      }, { status: 404 });
    }

    // Verificar se existem assinaturas ativas baseadas neste template
    const { data: assinaturasAtivas, error: assinaturasError } = await supabase
      .from('planos_servico_cliente')
      .select('plano_cliente_id')
      .eq('empresa_id', templateExistente.empresa_id)
      .eq('servico_id', templateExistente.servico_id)
      .eq('status', 'ativa')
      .eq('ativo', true);

    if (assinaturasError) {
      console.error('Erro ao verificar assinaturas:', assinaturasError);
      return NextResponse.json({ 
        success: false, 
        error: 'Erro ao verificar assinaturas ativas' 
      }, { status: 500 });
    }

    if (assinaturasAtivas && assinaturasAtivas.length > 0) {
      return NextResponse.json({ 
        success: false, 
        error: `Não é possível excluir o plano. Existem ${assinaturasAtivas.length} assinatura(s) ativa(s). Desative o plano primeiro.` 
      }, { status: 409 });
    }

    // Excluir template (soft delete - marcar como inativo)
    const { error: excluirError } = await supabase
      .from('templates_planos_assinatura')
      .update({ 
        ativo: false,
        updated_at: new Date().toISOString()
      })
      .eq('template_id', templateId);

    if (excluirError) {
      console.error('Erro ao excluir template:', excluirError);
      return NextResponse.json({ 
        success: false, 
        error: 'Erro ao excluir plano de assinatura' 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Plano de assinatura excluído com sucesso'
    });

  } catch (error) {
    console.error('Erro ao excluir template:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Erro interno do servidor' 
    }, { status: 500 });
  }
}

// Função auxiliar para validação de atualização
function validarDadosAtualizacao(data: AtualizarPlanoAssinaturaData) {
  const erros: string[] = [];

  // Validar nome do plano se fornecido
  if (data.nome_plano_empresa !== undefined) {
    if (!data.nome_plano_empresa || data.nome_plano_empresa.trim().length < LIMITES_PLANOS_ASSINATURA.nome_plano_min) {
      erros.push(`Nome do plano deve ter pelo menos ${LIMITES_PLANOS_ASSINATURA.nome_plano_min} caracteres`);
    }

    if (data.nome_plano_empresa.length > LIMITES_PLANOS_ASSINATURA.nome_plano_max) {
      erros.push(`Nome do plano deve ter no máximo ${LIMITES_PLANOS_ASSINATURA.nome_plano_max} caracteres`);
    }
  }

  // Validar preço se fornecido
  if (data.preco_mensal_assinatura !== undefined) {
    if (data.preco_mensal_assinatura < LIMITES_PLANOS_ASSINATURA.preco_min) {
      erros.push(`Preço deve ser pelo menos R$ ${LIMITES_PLANOS_ASSINATURA.preco_min}`);
    }

    if (data.preco_mensal_assinatura > LIMITES_PLANOS_ASSINATURA.preco_max) {
      erros.push(`Preço deve ser no máximo R$ ${LIMITES_PLANOS_ASSINATURA.preco_max}`);
    }
  }

  // Validar limite de usos se fornecido
  if (data.limite_usos_mes !== undefined && data.limite_usos_mes !== null) {
    if (data.limite_usos_mes < LIMITES_PLANOS_ASSINATURA.usos_min) {
      erros.push(`Limite de usos deve ser pelo menos ${LIMITES_PLANOS_ASSINATURA.usos_min}`);
    }

    if (data.limite_usos_mes > LIMITES_PLANOS_ASSINATURA.usos_max) {
      erros.push(`Limite de usos deve ser no máximo ${LIMITES_PLANOS_ASSINATURA.usos_max}`);
    }
  }

  // Validar descrição se fornecida
  if (data.descricao_plano !== undefined && data.descricao_plano && data.descricao_plano.length > LIMITES_PLANOS_ASSINATURA.descricao_max) {
    erros.push(`Descrição deve ter no máximo ${LIMITES_PLANOS_ASSINATURA.descricao_max} caracteres`);
  }

  return {
    valido: erros.length === 0,
    erros
  };
}
