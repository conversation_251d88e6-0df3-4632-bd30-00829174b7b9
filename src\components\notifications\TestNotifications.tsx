'use client';

import { useState } from 'react';
import { useNotifications } from '@/hooks/useNotifications';
import { TipoNotificacao } from '@/types/notifications';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';

interface TestNotificationsProps {
  agendamentoId?: number;
  clienteId?: string;
  proprietarioId?: string;
}

export function TestNotifications({ 
  agendamentoId, 
  clienteId, 
  proprietarioId 
}: TestNotificationsProps) {
  const [resultado, setResultado] = useState<string>('');
  const [estatisticas, setEstatisticas] = useState<any>(null);
  
  const { 
    enviando, 
    erro, 
    enviarNotificacao,
    buscarNotificacoesPendentes 
  } = useNotifications();

  const testarNotificacao = async (tipo: TipoNotificacao) => {
    if (!agendamentoId || !clienteId) {
      setResultado('❌ Dados necessários não fornecidos (agendamentoId, clienteId)');
      return;
    }

    // Contexto de teste
    const contextoTeste = {
      agendamento_id: agendamentoId,
      codigo_confirmacao: 'TEST123',
      cliente_nome: 'Cliente Teste',
      cliente_email: '<EMAIL>',
      empresa_nome: 'Empresa Teste',
      empresa_endereco: 'Rua Teste, 123 - Cidade Teste',
      servico_nome: 'Serviço Teste',
      servico_preco: 50.00,
      colaborador_nome: 'Profissional Teste',
      data_hora_inicio: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Amanhã
      data_hora_fim: new Date(Date.now() + 24 * 60 * 60 * 1000 + 60 * 60 * 1000).toISOString(), // Amanhã + 1h
      forma_pagamento: 'Local',
      valor_total: 50.00,
      observacoes_cliente: 'Teste de notificação por email',
      prazo_confirmacao: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    };

    const sucesso = await enviarNotificacao({
      tipo,
      destinatario_id: clienteId,
      contexto: contextoTeste,
      canal: 'email',
      agendamento_id: agendamentoId,
      empresa_id: 1
    });

    if (sucesso) {
      setResultado(`✅ Notificação ${tipo} enviada com sucesso!`);
    } else {
      setResultado(`❌ Erro ao enviar notificação: ${erro}`);
    }
  };

  const buscarEstatisticas = async () => {
    try {
      const response = await fetch('/api/notifications/batch');
      const data = await response.json();
      
      if (data.success) {
        setEstatisticas(data.data);
        setResultado('✅ Estatísticas carregadas');
      } else {
        setResultado(`❌ Erro ao carregar estatísticas: ${data.error}`);
      }
    } catch (error) {
      setResultado(`❌ Erro: ${error}`);
    }
  };

  const processarLembretes = async (action: string) => {
    try {
      const response = await fetch('/api/notifications/batch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action, limite: 5 })
      });
      
      const data = await response.json();
      
      if (data.success) {
        setResultado(`✅ ${data.message}\nResultados: ${JSON.stringify(data.resultados, null, 2)}`);
      } else {
        setResultado(`❌ Erro: ${data.error}`);
      }
    } catch (error) {
      setResultado(`❌ Erro: ${error}`);
    }
  };

  const buscarPendentes = async () => {
    const pendentes = await buscarNotificacoesPendentes(10);
    setResultado(`📧 Notificações pendentes: ${JSON.stringify(pendentes, null, 2)}`);
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <h3 className="text-lg font-semibold text-[var(--text-primary)]">
          🧪 Teste do Sistema de Notificações
        </h3>
        <p className="text-sm text-[var(--text-secondary)]">
          Ferramenta para testar e monitorar o sistema de notificações por email
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Informações do teste */}
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <h4 className="font-medium text-blue-800 mb-2">Dados do Teste:</h4>
          <div className="text-sm text-blue-700 space-y-1">
            <p><strong>Agendamento ID:</strong> {agendamentoId || 'Não fornecido'}</p>
            <p><strong>Cliente ID:</strong> {clienteId || 'Não fornecido'}</p>
            <p><strong>Proprietário ID:</strong> {proprietarioId || 'Não fornecido'}</p>
          </div>
        </div>

        {/* Testes de notificações individuais */}
        <div>
          <h4 className="font-medium text-[var(--text-primary)] mb-3">
            📧 Testar Notificações Individuais
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            <Button
              onClick={() => testarNotificacao('novo_agendamento')}
              disabled={enviando || !agendamentoId || !clienteId}
              className="text-sm"
            >
              Novo Agendamento
            </Button>
            
            <Button
              onClick={() => testarNotificacao('agendamento_confirmado')}
              disabled={enviando || !agendamentoId || !clienteId}
              className="text-sm"
            >
              Confirmado
            </Button>
            
            <Button
              onClick={() => testarNotificacao('agendamento_recusado')}
              disabled={enviando || !agendamentoId || !clienteId}
              className="text-sm"
            >
              Recusado
            </Button>
            
            <Button
              onClick={() => testarNotificacao('agendamento_cancelado')}
              disabled={enviando || !agendamentoId || !clienteId}
              className="text-sm"
            >
              Cancelado
            </Button>
            
            <Button
              onClick={() => testarNotificacao('lembrete_confirmacao')}
              disabled={enviando || !agendamentoId || !proprietarioId}
              className="text-sm"
            >
              Lembrete Confirmação
            </Button>
            
            <Button
              onClick={() => testarNotificacao('lembrete_agendamento')}
              disabled={enviando || !agendamentoId || !clienteId}
              className="text-sm"
            >
              Lembrete Agendamento
            </Button>
          </div>
        </div>

        {/* Processamento em lote */}
        <div>
          <h4 className="font-medium text-[var(--text-primary)] mb-3">
            📦 Processamento em Lote
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <Button
              onClick={() => processarLembretes('lembretes_confirmacao')}
              disabled={enviando}
              className="text-sm"
            >
              Lembretes de Confirmação
            </Button>
            
            <Button
              onClick={() => processarLembretes('lembretes_agendamento')}
              disabled={enviando}
              className="text-sm"
            >
              Lembretes de Agendamento
            </Button>
            
            <Button
              onClick={() => processarLembretes('reenviar_falhadas')}
              disabled={enviando}
              className="text-sm"
            >
              Reenviar Falhadas
            </Button>
          </div>
        </div>

        {/* Monitoramento */}
        <div>
          <h4 className="font-medium text-[var(--text-primary)] mb-3">
            📊 Monitoramento
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <Button
              onClick={buscarEstatisticas}
              disabled={enviando}
              className="text-sm"
            >
              Buscar Estatísticas
            </Button>
            
            <Button
              onClick={buscarPendentes}
              disabled={enviando}
              className="text-sm"
            >
              Notificações Pendentes
            </Button>
          </div>
        </div>

        {/* Status */}
        {enviando && (
          <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
            <p className="text-yellow-800">⏳ Processando...</p>
          </div>
        )}

        {/* Resultado */}
        {resultado && (
          <div className="bg-gray-50 p-4 rounded-lg border">
            <h4 className="font-medium text-[var(--text-primary)] mb-2">Resultado:</h4>
            <pre className="text-sm text-[var(--text-secondary)] whitespace-pre-wrap overflow-x-auto">
              {resultado}
            </pre>
          </div>
        )}

        {/* Estatísticas */}
        {estatisticas && (
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <h4 className="font-medium text-green-800 mb-2">📊 Estatísticas (últimas 24h):</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <div className="font-medium text-green-700">Total</div>
                <div className="text-green-600">{estatisticas.total}</div>
              </div>
              <div>
                <div className="font-medium text-green-700">Enviadas</div>
                <div className="text-green-600">{estatisticas.enviadas}</div>
              </div>
              <div>
                <div className="font-medium text-green-700">Pendentes</div>
                <div className="text-green-600">{estatisticas.pendentes}</div>
              </div>
              <div>
                <div className="font-medium text-green-700">Reenvios</div>
                <div className="text-green-600">{estatisticas.tentativas_multiplas}</div>
              </div>
            </div>
          </div>
        )}

        {/* Instruções */}
        <div className="bg-gray-50 p-4 rounded-lg border text-sm text-[var(--text-secondary)]">
          <h4 className="font-medium text-[var(--text-primary)] mb-2">💡 Instruções:</h4>
          <ul className="space-y-1 list-disc list-inside">
            <li>Configure a chave RESEND_API_KEY no arquivo .env.local</li>
            <li>Verifique se o email RESEND_FROM_EMAIL está configurado</li>
            <li>Os emails de teste serão enviados para o email do usuário logado</li>
            <li>Use as estatísticas para monitorar o desempenho do sistema</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
