import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { getSecurityAlerts, resolveSecurityAlert, AuditLogger } from '@/utils/security/audit';
import { getRequestIdentifier, withRateLimit } from '@/utils/security/rateLimiting';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      AuditLogger.securityViolation(
        undefined,
        'Unauthorized access to security alerts endpoint',
        getRequestIdentifier(request)
      );
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    // Verificar se é administrador
    const userRole = user.user_metadata?.role;
    if (userRole !== 'Administrador') {
      AuditLogger.securityViolation(
        user.id,
        'Non-admin access attempt to security alerts endpoint',
        getRequestIdentifier(request),
        { userRole }
      );
      return NextResponse.json(
        { success: false, error: 'Acesso negado' },
        { status: 403 }
      );
    }

    // Aplicar rate limiting
    try {
      withRateLimit(
        getRequestIdentifier(request, user.id),
        '/api/security/alerts',
        { maxRequests: 100, windowMs: 60 * 60 * 1000 } // 100 por hora
      );
    } catch (rateLimitError: any) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Rate limit excedido',
          retryAfter: rateLimitError.retryAfter 
        },
        { status: 429 }
      );
    }

    const { searchParams } = new URL(request.url);
    
    // Parâmetros de filtro
    const filters = {
      type: searchParams.get('type') || undefined,
      severity: searchParams.get('severity') || undefined,
      resolved: searchParams.get('resolved') ? searchParams.get('resolved') === 'true' : undefined,
      limit: parseInt(searchParams.get('limit') || '50')
    };

    // Buscar alertas de segurança
    const alerts = getSecurityAlerts(filters);

    // Estatísticas dos alertas
    const allAlerts = getSecurityAlerts();
    const stats = {
      total: allAlerts.length,
      active: allAlerts.filter(a => !a.resolved).length,
      resolved: allAlerts.filter(a => a.resolved).length,
      bySeverity: {
        critical: allAlerts.filter(a => a.severity === 'CRITICAL').length,
        high: allAlerts.filter(a => a.severity === 'HIGH').length,
        medium: allAlerts.filter(a => a.severity === 'MEDIUM').length,
        low: allAlerts.filter(a => a.severity === 'LOW').length
      },
      byType: allAlerts.reduce((acc, alert) => {
        acc[alert.type] = (acc[alert.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };

    // Log do acesso
    AuditLogger.adminAction(
      user.id,
      'VIEW_SECURITY_ALERTS',
      'security_alerts',
      undefined,
      true
    );

    return NextResponse.json({
      success: true,
      data: {
        alerts,
        stats,
        filters
      }
    });

  } catch (error: any) {
    console.error('Erro na API de alertas de segurança:', error);
    
    AuditLogger.securityViolation(
      undefined,
      'Error in security alerts API',
      getRequestIdentifier(request),
      { error: error.message }
    );

    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    // Verificar se é administrador
    const userRole = user.user_metadata?.role;
    if (userRole !== 'Administrador') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado' },
        { status: 403 }
      );
    }

    // Aplicar rate limiting
    try {
      withRateLimit(
        getRequestIdentifier(request, user.id),
        '/api/security/alerts',
        { maxRequests: 50, windowMs: 60 * 60 * 1000 } // 50 PATCH por hora
      );
    } catch (rateLimitError: any) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Rate limit excedido',
          retryAfter: rateLimitError.retryAfter 
        },
        { status: 429 }
      );
    }

    const body = await request.json();
    const { alertId, action } = body;

    if (!alertId) {
      return NextResponse.json(
        { success: false, error: 'ID do alerta é obrigatório' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'resolve':
        const resolved = resolveSecurityAlert(alertId);
        
        if (!resolved) {
          return NextResponse.json(
            { success: false, error: 'Alerta não encontrado' },
            { status: 404 }
          );
        }

        // Log da ação
        AuditLogger.adminAction(
          user.id,
          'RESOLVE_SECURITY_ALERT',
          'security_alerts',
          alertId,
          true
        );

        return NextResponse.json({
          success: true,
          message: 'Alerta resolvido com sucesso'
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Ação não reconhecida' },
          { status: 400 }
        );
    }

  } catch (error: any) {
    console.error('Erro na API de alertas de segurança (PATCH):', error);
    
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
