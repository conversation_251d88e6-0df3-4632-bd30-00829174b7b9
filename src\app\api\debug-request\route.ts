import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  console.log('🔍 DEBUG: Requisição recebida');
  console.log('🔍 DEBUG: URL:', request.url);
  console.log('🔍 DEBUG: Method:', request.method);
  console.log('🔍 DEBUG: Headers:', Object.fromEntries(request.headers.entries()));

  return NextResponse.json({
    success: true,
    debug: {
      url: request.url,
      method: request.method,
      headers: Object.fromEntries(request.headers.entries()),
      timestamp: new Date().toISOString()
    }
  });
}
