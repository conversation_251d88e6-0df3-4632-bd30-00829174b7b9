'use client';

import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  HorarioEmpresa,
  HorarioColaborador,
  EstadoHorarios,
  AcoesHorarios,
  HorarioApiResponse,
  BloqueioRecorrente,
  BloqueioEspecifico,
  VerificacaoConflito,
  DisponibilidadeHorario,
  ValidacaoHorario,
  HorarioDia
} from '@/types/horarios';

export function useHorarios(): EstadoHorarios & AcoesHorarios {
  const { user } = useAuth();
  
  const [estado, setEstado] = useState<EstadoHorarios>({
    horarios_empresa: null,
    horarios_colaboradores: [],
    loading: false,
    error: null,
    salvando: false,
    configuracoes: null
  });

  // Buscar horários da empresa
  const buscarHorariosEmpresa = useCallback(async () => {
    if (!user) return;

    setEstado(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch('/api/horarios/empresa');
      const data: HorarioApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao buscar horários da empresa');
      }

      if (data.success && data.data) {
        setEstado(prev => ({
          ...prev,
          horarios_empresa: data.data.horario_funcionamento || null
        }));
      }
    } catch (err: any) {
      setEstado(prev => ({ ...prev, error: err.message }));
      console.error('Erro ao buscar horários da empresa:', err);
    } finally {
      setEstado(prev => ({ ...prev, loading: false }));
    }
  }, [user]);

  // Buscar horários de um colaborador específico
  const buscarHorariosColaborador = useCallback(async (colaboradorId: string) => {
    if (!user) return;

    setEstado(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch(`/api/horarios/colaborador?colaboradorId=${colaboradorId}`);
      const data: HorarioApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao buscar horários do colaborador');
      }

      if (data.success && data.data) {
        setEstado(prev => {
          const colaboradoresAtualizados = prev.horarios_colaboradores.filter(
            c => c.colaborador_user_id !== colaboradorId
          );
          colaboradoresAtualizados.push(data.data);
          
          return {
            ...prev,
            horarios_colaboradores: colaboradoresAtualizados
          };
        });
      }
    } catch (err: any) {
      setEstado(prev => ({ ...prev, error: err.message }));
      console.error('Erro ao buscar horários do colaborador:', err);
    } finally {
      setEstado(prev => ({ ...prev, loading: false }));
    }
  }, [user]);

  // Atualizar horários da empresa
  const atualizarHorariosEmpresa = useCallback(async (horarios: HorarioEmpresa): Promise<boolean> => {
    if (!user) return false;

    setEstado(prev => ({ ...prev, salvando: true, error: null }));

    try {
      const response = await fetch('/api/horarios/empresa', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ horario_funcionamento: horarios }),
      });

      const data: HorarioApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao atualizar horários da empresa');
      }

      if (data.success) {
        setEstado(prev => ({
          ...prev,
          horarios_empresa: horarios
        }));
        return true;
      }

      return false;
    } catch (err: any) {
      setEstado(prev => ({ ...prev, error: err.message }));
      console.error('Erro ao atualizar horários da empresa:', err);
      return false;
    } finally {
      setEstado(prev => ({ ...prev, salvando: false }));
    }
  }, [user]);

  // Atualizar horários de colaborador
  const atualizarHorariosColaborador = useCallback(async (
    colaboradorId: string, 
    horarios: HorarioColaborador
  ): Promise<boolean> => {
    if (!user) return false;

    setEstado(prev => ({ ...prev, salvando: true, error: null }));

    try {
      const response = await fetch('/api/horarios/colaborador', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          colaborador_user_id: colaboradorId,
          horarios_trabalho_individual: horarios
        }),
      });

      const data: HorarioApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao atualizar horários do colaborador');
      }

      if (data.success) {
        setEstado(prev => {
          const colaboradoresAtualizados = prev.horarios_colaboradores.filter(
            c => c.colaborador_user_id !== colaboradorId
          );
          colaboradoresAtualizados.push(horarios);
          
          return {
            ...prev,
            horarios_colaboradores: colaboradoresAtualizados
          };
        });
        return true;
      }

      return false;
    } catch (err: any) {
      setEstado(prev => ({ ...prev, error: err.message }));
      console.error('Erro ao atualizar horários do colaborador:', err);
      return false;
    } finally {
      setEstado(prev => ({ ...prev, salvando: false }));
    }
  }, [user]);

  // Adicionar bloqueio
  const adicionarBloqueio = useCallback(async (
    colaboradorId: string,
    bloqueio: BloqueioRecorrente | BloqueioEspecifico
  ): Promise<boolean> => {
    if (!user) return false;

    setEstado(prev => ({ ...prev, salvando: true, error: null }));

    try {
      const response = await fetch('/api/horarios/bloqueios', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          colaborador_user_id: colaboradorId,
          bloqueio
        }),
      });

      const data: HorarioApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao adicionar bloqueio');
      }

      if (data.success) {
        // Recarregar horários do colaborador
        await buscarHorariosColaborador(colaboradorId);
        return true;
      }

      return false;
    } catch (err: any) {
      setEstado(prev => ({ ...prev, error: err.message }));
      console.error('Erro ao adicionar bloqueio:', err);
      return false;
    } finally {
      setEstado(prev => ({ ...prev, salvando: false }));
    }
  }, [user, buscarHorariosColaborador]);

  // Remover bloqueio
  const removerBloqueio = useCallback(async (
    colaboradorId: string,
    bloqueioId: string,
    tipo: 'recorrente' | 'especifico'
  ): Promise<boolean> => {
    if (!user) return false;

    setEstado(prev => ({ ...prev, salvando: true, error: null }));

    try {
      const response = await fetch('/api/horarios/bloqueios', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          colaborador_user_id: colaboradorId,
          bloqueio_id: bloqueioId,
          tipo
        }),
      });

      const data: HorarioApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao remover bloqueio');
      }

      if (data.success) {
        // Recarregar horários do colaborador
        await buscarHorariosColaborador(colaboradorId);
        return true;
      }

      return false;
    } catch (err: any) {
      setEstado(prev => ({ ...prev, error: err.message }));
      console.error('Erro ao remover bloqueio:', err);
      return false;
    } finally {
      setEstado(prev => ({ ...prev, salvando: false }));
    }
  }, [user, buscarHorariosColaborador]);

  // Verificar conflitos
  const verificarConflitos = useCallback(async (
    colaboradorId: string,
    data: string,
    horarioInicio?: string,
    horarioFim?: string
  ): Promise<VerificacaoConflito> => {
    // Por enquanto, retorna sem conflitos até implementar a API completa
    // Será implementado quando a tabela de agendamentos estiver pronta
    return { tem_conflitos: false, conflitos: [], pode_prosseguir: true };
  }, []);

  // Verificar disponibilidade
  const verificarDisponibilidade = useCallback(async (
    colaboradorId: string,
    data: string
  ): Promise<DisponibilidadeHorario> => {
    // Por enquanto, retorna disponibilidade básica até implementar a API completa
    // Será implementado quando a tabela de agendamentos estiver pronta
    return { data, horarios_disponiveis: [], bloqueios: [] };
  }, []);

  // Validar horário
  const validarHorario = useCallback((horario: HorarioDia): ValidacaoHorario => {
    const erros: string[] = [];

    if (horario.ativo) {
      if (!horario.abertura || !horario.fechamento) {
        erros.push('Horário de abertura e fechamento são obrigatórios');
      } else {
        const abertura = new Date(`2000-01-01T${horario.abertura}:00`);
        const fechamento = new Date(`2000-01-01T${horario.fechamento}:00`);

        if (abertura >= fechamento) {
          erros.push('Horário de fechamento deve ser posterior ao de abertura');
        }

        // Validar pausas
        if (horario.pausas) {
          for (const pausa of horario.pausas) {
            const inicioPausa = new Date(`2000-01-01T${pausa.inicio}:00`);
            const fimPausa = new Date(`2000-01-01T${pausa.fim}:00`);

            if (inicioPausa >= fimPausa) {
              erros.push('Horário de fim da pausa deve ser posterior ao de início');
            }

            if (inicioPausa < abertura || fimPausa > fechamento) {
              erros.push('Pausas devem estar dentro do horário de funcionamento');
            }
          }
        }
      }
    }

    return {
      valido: erros.length === 0,
      erros
    };
  }, []);

  // Limpar erro
  const limparError = useCallback(() => {
    setEstado(prev => ({ ...prev, error: null }));
  }, []);

  return {
    ...estado,
    buscarHorariosEmpresa,
    buscarHorariosColaborador,
    atualizarHorariosEmpresa,
    atualizarHorariosColaborador,
    adicionarBloqueio,
    removerBloqueio,
    verificarConflitos,
    verificarDisponibilidade,
    validarHorario,
    limparError
  };
}
