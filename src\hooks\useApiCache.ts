/**
 * Hook para cache de APIs
 * Reduz chamadas desnecessárias e melhora performance
 */

import { useState, useCallback, useRef, useEffect } from 'react';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

interface CacheConfig {
  ttl?: number; // Time to live em milissegundos (padrão: 5 minutos)
  maxSize?: number; // Tamanho máximo do cache (padrão: 100)
  staleWhileRevalidate?: boolean; // Retornar dados antigos enquanto revalida (padrão: true)
}

interface ApiCacheState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastFetch: number | null;
  isStale: boolean;
}

// Cache global em memória
const globalCache = new Map<string, CacheEntry<any>>();

// Função para limpar entradas expiradas
function cleanExpiredEntries() {
  const now = Date.now();
  for (const [key, entry] of globalCache.entries()) {
    if (entry.expiresAt < now) {
      globalCache.delete(key);
    }
  }
}

// Limpar cache expirado a cada 5 minutos
setInterval(cleanExpiredEntries, 5 * 60 * 1000);

export function useApiCache<T>(
  cacheKey: string,
  fetcher: () => Promise<T>,
  config: CacheConfig = {}
) {
  const {
    ttl = 5 * 60 * 1000, // 5 minutos
    maxSize = 100,
    staleWhileRevalidate = true
  } = config;

  const [state, setState] = useState<ApiCacheState<T>>({
    data: null,
    loading: false,
    error: null,
    lastFetch: null,
    isStale: false
  });

  const fetcherRef = useRef(fetcher);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Atualizar referência do fetcher
  useEffect(() => {
    fetcherRef.current = fetcher;
  }, [fetcher]);

  // Verificar se dados estão no cache
  const getCachedData = useCallback(() => {
    const cached = globalCache.get(cacheKey);
    if (!cached) return null;

    const now = Date.now();
    const isExpired = cached.expiresAt < now;
    const isStale = (now - cached.timestamp) > (ttl * 0.8); // 80% do TTL

    return {
      data: cached.data,
      isExpired,
      isStale
    };
  }, [cacheKey, ttl]);

  // Armazenar dados no cache
  const setCachedData = useCallback((data: T) => {
    const now = Date.now();
    
    // Limitar tamanho do cache
    if (globalCache.size >= maxSize) {
      // Remover entrada mais antiga
      const oldestKey = globalCache.keys().next().value;
      if (oldestKey) {
        globalCache.delete(oldestKey);
      }
    }

    globalCache.set(cacheKey, {
      data,
      timestamp: now,
      expiresAt: now + ttl
    });
  }, [cacheKey, ttl, maxSize]);

  // Função para buscar dados
  const fetchData = useCallback(async (force = false) => {
    // Cancelar requisição anterior se existir
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    const cached = getCachedData();
    
    // Se tem dados válidos no cache e não é forçado, usar cache
    if (cached && !cached.isExpired && !force) {
      setState(prev => ({
        ...prev,
        data: cached.data,
        loading: false,
        error: null,
        lastFetch: Date.now(),
        isStale: cached.isStale
      }));
      return cached.data;
    }

    // Se tem dados antigos e staleWhileRevalidate está ativo, mostrar dados antigos
    if (cached && staleWhileRevalidate && !force) {
      setState(prev => ({
        ...prev,
        data: cached.data,
        loading: true,
        error: null,
        isStale: true
      }));
    } else {
      setState(prev => ({
        ...prev,
        loading: true,
        error: null,
        isStale: false
      }));
    }

    try {
      abortControllerRef.current = new AbortController();
      
      const data = await fetcherRef.current();
      
      // Armazenar no cache
      setCachedData(data);
      
      setState(prev => ({
        ...prev,
        data,
        loading: false,
        error: null,
        lastFetch: Date.now(),
        isStale: false
      }));

      return data;

    } catch (error: any) {
      // Se foi cancelado, não atualizar estado
      if (error.name === 'AbortError') {
        return;
      }

      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
        // Manter dados antigos se existirem
        data: cached?.data ?? prev.data,
        isStale: !!cached?.data
      }));

      throw error;
    } finally {
      abortControllerRef.current = null;
    }
  }, [getCachedData, setCachedData, staleWhileRevalidate]);

  // Função para invalidar cache
  const invalidate = useCallback(() => {
    globalCache.delete(cacheKey);
    setState(prev => ({
      ...prev,
      isStale: true
    }));
  }, [cacheKey]);

  // Função para revalidar dados
  const revalidate = useCallback(() => {
    return fetchData(true);
  }, [fetchData]);

  // Função para mutar dados localmente
  const mutate = useCallback((newData: T | ((current: T | null) => T)) => {
    const updatedData = typeof newData === 'function' 
      ? (newData as (current: T | null) => T)(state.data)
      : newData;

    // Atualizar cache
    setCachedData(updatedData);
    
    // Atualizar estado
    setState(prev => ({
      ...prev,
      data: updatedData,
      isStale: false,
      lastFetch: Date.now()
    }));

    return updatedData;
  }, [state.data, setCachedData]);

  // Carregar dados iniciais
  useEffect(() => {
    const cached = getCachedData();
    
    if (cached && !cached.isExpired) {
      // Usar dados do cache
      setState(prev => ({
        ...prev,
        data: cached.data,
        loading: false,
        error: null,
        lastFetch: Date.now(),
        isStale: cached.isStale
      }));

      // Se dados estão obsoletos, revalidar em background
      if (cached.isStale && staleWhileRevalidate) {
        fetchData();
      }
    } else {
      // Buscar dados frescos
      fetchData();
    }

    // Cleanup ao desmontar
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [cacheKey]); // Apenas cacheKey como dependência

  return {
    ...state,
    refetch: fetchData,
    invalidate,
    revalidate,
    mutate
  };
}

// Hook para cache de lista com paginação
export function useApiListCache<T>(
  cacheKey: string,
  fetcher: (page: number, limit: number) => Promise<{ data: T[]; total: number; hasMore: boolean }>,
  config: CacheConfig & { pageSize?: number } = {}
) {
  const { pageSize = 20, ...cacheConfig } = config;
  const [page, setPage] = useState(1);
  const [allData, setAllData] = useState<T[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [total, setTotal] = useState(0);

  const fetchPage = useCallback(() => {
    return fetcher(page, pageSize);
  }, [fetcher, page, pageSize]);

  const {
    data: pageData,
    loading,
    error,
    refetch,
    invalidate: invalidateCache,
    revalidate
  } = useApiCache(`${cacheKey}-page-${page}`, fetchPage, cacheConfig);

  // Atualizar dados quando página carrega
  useEffect(() => {
    if (pageData) {
      if (page === 1) {
        setAllData(pageData.data);
      } else {
        setAllData(prev => [...prev, ...pageData.data]);
      }
      setHasMore(pageData.hasMore);
      setTotal(pageData.total);
    }
  }, [pageData, page]);

  const loadMore = useCallback(() => {
    if (hasMore && !loading) {
      setPage(prev => prev + 1);
    }
  }, [hasMore, loading]);

  const reset = useCallback(() => {
    setPage(1);
    setAllData([]);
    setHasMore(true);
    setTotal(0);
    // Invalidar todas as páginas do cache
    for (let i = 1; i <= page; i++) {
      globalCache.delete(`${cacheKey}-page-${i}`);
    }
  }, [cacheKey, page]);

  const invalidate = useCallback(() => {
    invalidateCache();
    reset();
  }, [invalidateCache, reset]);

  return {
    data: allData,
    loading,
    error,
    hasMore,
    total,
    page,
    loadMore,
    reset,
    invalidate,
    revalidate,
    refetch
  };
}
