/**
 * Script para debugar problemas específicos do frontend
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Variáveis de ambiente do Supabase não encontradas');
  process.exit(1);
}

// Cliente administrativo do Supabase
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function debugFrontend() {
  try {
    console.log('🔍 DEBUG DO PROBLEMA DO FRONTEND');
    console.log('=' .repeat(60));
    console.log('');

    const slug = 'barbearia-santos-3';

    // 1. Testar exatamente como o frontend faz
    console.log('1️⃣ Simulando exatamente o que o frontend faz...');
    
    try {
      console.log(`   📡 Fazendo fetch para: /api/empresas/${slug}`);
      const response = await fetch(`http://localhost:3000/api/empresas/${slug}`);
      console.log(`   📊 Status da resposta: ${response.status} ${response.statusText}`);
      console.log(`   📊 Headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`);
      
      const result = await response.json();
      console.log(`   📊 Response.ok: ${response.ok}`);
      console.log(`   📊 Result.success: ${result.success}`);
      
      if (!response.ok) {
        console.log(`   ❌ Response não OK: ${result.error}`);
        throw new Error(result.error ?? 'Erro ao buscar dados da empresa');
      }

      console.log(`   ✅ API retornou dados com sucesso`);
      console.log(`   📊 Tipo de result.data: ${typeof result.data}`);
      console.log(`   📊 result.data é null: ${result.data === null}`);
      console.log(`   📊 result.data é undefined: ${result.data === undefined}`);
      
      if (result.data) {
        console.log(`   📊 Chaves em result.data: ${Object.keys(result.data).join(', ')}`);
        
        if (result.data.empresa) {
          console.log(`   ✅ result.data.empresa existe`);
          console.log(`   📊 Nome da empresa: ${result.data.empresa.nome_empresa}`);
        } else {
          console.log(`   ❌ result.data.empresa é ${result.data.empresa}`);
        }
      } else {
        console.log(`   ❌ result.data é ${result.data}`);
      }

      // Simular a lógica do frontend
      const dadosEmpresa = result.data;
      
      console.log('');
      console.log('2️⃣ Simulando condições do frontend...');
      console.log(`   📊 dadosEmpresa: ${dadosEmpresa ? 'existe' : 'null/undefined'}`);
      console.log(`   📊 !dadosEmpresa: ${!dadosEmpresa}`);
      
      // Esta é a condição que causa o erro no frontend
      if (!dadosEmpresa) {
        console.log('   ❌ PROBLEMA ENCONTRADO: dadosEmpresa é falsy');
        console.log('   🔧 Isso faria o frontend mostrar "Empresa não encontrada"');
        return false;
      } else {
        console.log('   ✅ dadosEmpresa passou na verificação');
      }

      // Verificar se a estrutura está correta
      const { empresa, servicos, servicos_por_categoria, colaboradores, estatisticas } = dadosEmpresa;
      
      console.log('');
      console.log('3️⃣ Verificando destructuring...');
      console.log(`   📊 empresa: ${empresa ? 'OK' : 'PROBLEMA'}`);
      console.log(`   📊 servicos: ${servicos ? 'OK' : 'PROBLEMA'}`);
      console.log(`   📊 servicos_por_categoria: ${servicos_por_categoria ? 'OK' : 'PROBLEMA'}`);
      console.log(`   📊 colaboradores: ${colaboradores ? 'OK' : 'PROBLEMA'}`);
      console.log(`   📊 estatisticas: ${estatisticas ? 'OK' : 'PROBLEMA'}`);

      if (!empresa) {
        console.log('   ❌ PROBLEMA: empresa é undefined após destructuring');
        return false;
      }

      console.log('');
      console.log('4️⃣ Verificando campos da empresa...');
      console.log(`   📊 empresa.nome_empresa: ${empresa.nome_empresa}`);
      console.log(`   📊 empresa.endereco: ${empresa.endereco}`);
      console.log(`   📊 empresa.numero: ${empresa.numero}`);
      
      // Simular formatação do endereço
      const enderecoCompleto = `${empresa.endereco}, ${empresa.numero}${empresa.complemento ? `, ${empresa.complemento}` : ''} - ${empresa.bairro}, ${empresa.cidade}/${empresa.estado}`;
      console.log(`   📊 enderecoCompleto: ${enderecoCompleto}`);

      console.log('');
      console.log('✅ SIMULAÇÃO COMPLETA - TUDO FUNCIONANDO');
      return true;

    } catch (error) {
      console.log(`   ❌ ERRO CAPTURADO: ${error.message}`);
      console.log(`   🔧 Este erro faria o frontend mostrar "Empresa não encontrada"`);
      return false;
    }

  } catch (error) {
    console.error('❌ Erro geral no debug:', error);
    return false;
  }
}

async function verificarServidor() {
  try {
    console.log('🌐 Verificando se o servidor está rodando...');
    
    const response = await fetch('http://localhost:3000/api/health');
    if (response.ok) {
      console.log('✅ Servidor está rodando');
      return true;
    } else {
      console.log('❌ Servidor retornou erro');
      return false;
    }
  } catch (error) {
    console.log('❌ Servidor não está acessível:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 INICIANDO DEBUG COMPLETO...\n');

  // Verificar servidor
  const servidorOK = await verificarServidor();
  if (!servidorOK) {
    console.log('\n❌ PROBLEMA: Servidor não está rodando ou acessível');
    console.log('🔧 SOLUÇÃO: Execute "npm run dev" no diretório servicetech');
    return;
  }

  console.log('');
  
  // Debug do frontend
  const frontendOK = await debugFrontend();

  console.log('\n' + '=' .repeat(60));
  if (frontendOK) {
    console.log('🎉 DEBUG CONCLUÍDO - NENHUM PROBLEMA ENCONTRADO');
    console.log('=' .repeat(60));
    console.log('');
    console.log('🤔 Se a página ainda mostra "Empresa não encontrada":');
    console.log('1. Limpe o cache do navegador (Ctrl+F5)');
    console.log('2. Abra o console do navegador (F12)');
    console.log('3. Verifique se há erros JavaScript');
    console.log('4. Confirme se a API está sendo chamada');
    console.log('5. Verifique se o Next.js recompilou o código');
  } else {
    console.log('❌ PROBLEMA ENCONTRADO NO DEBUG');
    console.log('=' .repeat(60));
    console.log('Verifique os logs acima para identificar o problema específico.');
  }
}

// Executar debug
main();
