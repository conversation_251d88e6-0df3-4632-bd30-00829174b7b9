'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useAgendamentos } from '@/hooks/useAgendamentos';
import { useDisponibilidade } from '@/hooks/useDisponibilidade';
import { SeletorServicosMultiplos } from './SeletorServicosMultiplos';
import { SeletorColaborador } from './SeletorColaborador';
import { SeletorHorario } from './SeletorHorario';
import { ResumoAgendamento } from './ResumoAgendamento';

interface DadosEmpresa {
  empresa_id: number;
  nome_empresa: string;
  endereco: string;
  numero: string;
  bairro: string;
  cidade: string;
  estado: string;
  // Campos do Stripe Connect
  pagamentos_online_habilitados?: boolean;
  stripe_charges_enabled?: boolean;
  servicos: Array<{
    servico_id: number;
    nome_servico: string;
    descricao: string;
    duracao_minutos: number;
    preco: number;
    categoria: string;
  }>;
  servicos_por_categoria: Record<string, any[]>;
  colaboradores: Array<{
    colaborador_user_id: string;
    name: string;
    ativo_como_prestador: boolean;
  }>;
}

interface FormularioAgendamentoProps {
  dadosEmpresa: DadosEmpresa;
}

type EtapaFluxo = 'servico' | 'colaborador' | 'horario' | 'resumo';

export function FormularioAgendamento({ dadosEmpresa }: FormularioAgendamentoProps) {
  const router = useRouter();
  const [etapaAtual, setEtapaAtual] = useState<EtapaFluxo>('servico');
  const [carregandoDados] = useState(false);

  const {
    fluxoAgendamento,
    combosDisponiveis,
    comboAplicado,
    loading: loadingAgendamento,
    error: errorAgendamento,
    iniciarFluxoAgendamento,
    adicionarServico,
    removerServico,
    selecionarColaborador,
    selecionarHorario,
    definirObservacoes,
    definirFormaPagamento,
    finalizarAgendamento,
    limparErro
  } = useAgendamentos();

  const {
    horariosDisponiveis,
    loading: loadingDisponibilidade,
    error: errorDisponibilidade,
    buscarDisponibilidadeProximosDias,
    limparErro: limparErroDisponibilidade
  } = useDisponibilidade();

  // Inicializar fluxo
  useEffect(() => {
    iniciarFluxoAgendamento(dadosEmpresa.empresa_id);
  }, [dadosEmpresa.empresa_id, iniciarFluxoAgendamento]);

  // Buscar disponibilidade quando serviços e colaborador forem selecionados
  useEffect(() => {
    if (fluxoAgendamento?.servicos_selecionados && fluxoAgendamento.servicos_selecionados.length > 0 && etapaAtual === 'horario') {
      // Para múltiplos serviços, usar o primeiro serviço para calcular disponibilidade
      // TODO: Implementar lógica mais sofisticada para múltiplos serviços
      const primeiroServico = fluxoAgendamento.servicos_selecionados[0];
      buscarDisponibilidadeProximosDias(
        dadosEmpresa.empresa_id,
        primeiroServico.servico_id,
        14, // Próximos 14 dias
        fluxoAgendamento.colaborador_selecionado?.colaborador_user_id
      );
    }
  }, [
    fluxoAgendamento?.servicos_selecionados,
    fluxoAgendamento?.colaborador_selecionado,
    etapaAtual,
    dadosEmpresa.empresa_id,
    buscarDisponibilidadeProximosDias
  ]);

  const enderecoCompleto = `${dadosEmpresa.endereco}, ${dadosEmpresa.numero} - ${dadosEmpresa.bairro}, ${dadosEmpresa.cidade}/${dadosEmpresa.estado}`;

  const handleAdicionarServico = (servico: any) => {
    adicionarServico(servico);
  };

  const handleRemoverServico = (servico_id: number) => {
    removerServico(servico_id);
  };

  const handleContinuarParaColaborador = () => {
    if (fluxoAgendamento?.servicos_selecionados && fluxoAgendamento.servicos_selecionados.length > 0) {
      setEtapaAtual('colaborador');
    }
  };

  const handleSelecionarColaborador = (colaborador?: any) => {
    selecionarColaborador(colaborador);
    setEtapaAtual('horario');
  };

  const handleSelecionarHorario = (horario: any) => {
    selecionarHorario(horario);
    setEtapaAtual('resumo');
  };

  const handleFinalizar = async () => {
    const resultado = await finalizarAgendamento();

    if (resultado.sucesso) {
      if (resultado.requiresPagamento && resultado.agendamento) {
        // Redirecionar para página de pagamento
        router.push(`/agendamento/pagamento/${resultado.agendamento.agendamento_id}`);
      } else {
        // Redirecionar para página de confirmação (pagamento local)
        router.push('/agendamento/confirmacao');
      }
    }
  };

  const handleVoltar = () => {
    switch (etapaAtual) {
      case 'colaborador':
        setEtapaAtual('servico');
        break;
      case 'horario':
        setEtapaAtual('colaborador');
        break;
      case 'resumo':
        setEtapaAtual('horario');
        break;
    }
  };

  const obterTituloEtapa = () => {
    switch (etapaAtual) {
      case 'servico':
        return 'Escolha os Serviços';
      case 'colaborador':
        return 'Escolha o Profissional';
      case 'horario':
        return 'Escolha o Horário';
      case 'resumo':
        return 'Confirme os Detalhes';
      default:
        return 'Agendamento';
    }
  };

  const obterProgresso = () => {
    switch (etapaAtual) {
      case 'servico':
        return 25;
      case 'colaborador':
        return 50;
      case 'horario':
        return 75;
      case 'resumo':
        return 100;
      default:
        return 0;
    }
  };

  if (!fluxoAgendamento) {
    return (
      <div className="text-center py-8">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
        <p className="text-[var(--text-secondary)] mt-4">Inicializando agendamento...</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header com informações da empresa */}
      <Card className="bg-gradient-to-r from-[var(--primary)] to-[var(--primary-dark)] text-white">
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-white/20 rounded-lg flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <div className="flex-1">
              <h1 className="text-2xl font-bold">{dadosEmpresa.nome_empresa}</h1>
              <p className="text-white/80">{enderecoCompleto}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Barra de progresso */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-[var(--text-primary)]">
              {obterTituloEtapa()}
            </h2>
            <span className="text-sm text-[var(--text-secondary)]">
              Etapa {etapaAtual === 'servico' ? 1 : etapaAtual === 'colaborador' ? 2 : etapaAtual === 'horario' ? 3 : 4} de 4
            </span>
          </div>
          
          <div className="w-full bg-[var(--surface)] rounded-full h-2">
            <div 
              className="bg-[var(--primary)] h-2 rounded-full transition-all duration-300"
              style={{ width: `${obterProgresso()}%` }}
            ></div>
          </div>
          
          <div className="flex justify-between mt-2 text-xs text-[var(--text-secondary)]">
            <span className={etapaAtual === 'servico' ? 'text-[var(--primary)] font-medium' : ''}>
              Serviço
            </span>
            <span className={etapaAtual === 'colaborador' ? 'text-[var(--primary)] font-medium' : ''}>
              Profissional
            </span>
            <span className={etapaAtual === 'horario' ? 'text-[var(--primary)] font-medium' : ''}>
              Horário
            </span>
            <span className={etapaAtual === 'resumo' ? 'text-[var(--primary)] font-medium' : ''}>
              Confirmação
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Exibir erros */}
      {(errorAgendamento || errorDisponibilidade) && (
        <Card className="bg-[var(--error-light)] border-[var(--error)]">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-[var(--error)] rounded-full flex items-center justify-center mt-0.5">
                <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-[var(--text-primary)] mb-1">
                  Erro no Agendamento
                </h4>
                <p className="text-sm text-[var(--text-secondary)]">
                  {errorAgendamento || errorDisponibilidade}
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  limparErro();
                  limparErroDisponibilidade();
                }}
              >
                Fechar
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Conteúdo da etapa atual */}
      <div className="min-h-[400px]">
        {etapaAtual === 'servico' && (
          <div className="space-y-6">
            <SeletorServicosMultiplos
              servicos={dadosEmpresa.servicos}
              servicosPorCategoria={dadosEmpresa.servicos_por_categoria}
              servicosSelecionados={fluxoAgendamento.servicos_selecionados || []}
              combosDisponiveis={combosDisponiveis}
              comboAplicado={comboAplicado}
              onAdicionarServico={handleAdicionarServico}
              onRemoverServico={handleRemoverServico}
              loading={loadingAgendamento}
            />

            {/* Botão para continuar */}
            {fluxoAgendamento?.servicos_selecionados?.length > 0 && (
              <div className="flex justify-end">
                <Button
                  onClick={handleContinuarParaColaborador}
                  className="px-8"
                >
                  Continuar
                </Button>
              </div>
            )}
          </div>
        )}

        {etapaAtual === 'colaborador' && (
          <SeletorColaborador
            colaboradores={dadosEmpresa.colaboradores}
            colaboradorSelecionado={fluxoAgendamento.colaborador_selecionado}
            onSelecionarColaborador={handleSelecionarColaborador}
            loading={carregandoDados}
          />
        )}

        {etapaAtual === 'horario' && (
          <SeletorHorario
            horariosDisponiveis={horariosDisponiveis}
            horarioSelecionado={fluxoAgendamento.horario_selecionado}
            onSelecionarHorario={handleSelecionarHorario}
            loading={loadingDisponibilidade}
            colaboradorSelecionado={fluxoAgendamento.colaborador_selecionado}
          />
        )}

        {etapaAtual === 'resumo' && (
          <ResumoAgendamento
            fluxoAgendamento={fluxoAgendamento}
            nomeEmpresa={dadosEmpresa.nome_empresa}
            enderecoEmpresa={enderecoCompleto}
            onDefinirObservacoes={definirObservacoes}
            onDefinirFormaPagamento={definirFormaPagamento}
            onFinalizar={handleFinalizar}
            onVoltar={handleVoltar}
            loading={loadingAgendamento}
            aceitaPagamentoOnline={dadosEmpresa.pagamentos_online_habilitados &&
                                   dadosEmpresa.stripe_charges_enabled}
          />
        )}
      </div>

      {/* Botões de navegação (exceto na etapa de resumo) */}
      {etapaAtual !== 'resumo' && (
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handleVoltar}
            disabled={etapaAtual === 'servico'}
          >
            Voltar
          </Button>
          
          <div className="text-sm text-[var(--text-secondary)] self-center">
            {etapaAtual === 'servico' && 'Selecione um ou mais serviços para continuar'}
            {etapaAtual === 'colaborador' && 'Escolha um profissional ou continue com qualquer um'}
            {etapaAtual === 'horario' && 'Selecione um horário disponível'}
          </div>
        </div>
      )}
    </div>
  );
}
