'use client';

import { useEmpresaProprietario } from './useEmpresaProprietario';

export function usePlanoSaas() {
  const { planoSaas, loading, error } = useEmpresaProprietario();

  // Calcular dias restantes até o vencimento
  const calcularDiasRestantes = () => {
    if (!planoSaas?.data_fim) return 30; // Valor padrão
    const hoje = new Date();
    const dataFim = new Date(planoSaas.data_fim);
    const diffTime = dataFim.getTime() - hoje.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const diasRestantes = calcularDiasRestantes();
  const planoExpirado = diasRestantes <= 0;
  const precisaRenovar = diasRestantes <= 7 && diasRestantes > 0;

  return {
    planoAtual: planoSaas ? {
      nome: planoSaas.nome_plano,
      valor: planoSaas.preco_mensal,
      descricao: `Plano ${planoSaas.nome_plano} - Recursos ${planoSaas.recursos_premium ? 'Premium' : 'Básicos'}`
    } : null,
    limitesPlano: planoSaas ? {
      agendamentos: planoSaas.limite_servicos * 10, // Estimativa
      usuarios: planoSaas.limite_colaboradores,
      armazenamento: planoSaas.recursos_premium ? 5000 : 1000
    } : null,
    usoAtual: {
      agendamentos: 0, // Seria calculado com base nas métricas
      usuarios: 1,
      armazenamento: 250
    },
    diasRestantes,
    proximoVencimento: planoSaas?.data_fim || null,
    statusPagamento: planoSaas?.status_assinatura || 'ativo',
    loading,
    error,
    podeUpgrade: !planoSaas?.recursos_premium,
    precisaRenovar,
    planoExpirado
  };
}
