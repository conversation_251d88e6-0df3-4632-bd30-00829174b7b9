// Funções para gerenciar dados do onboarding no localStorage

// Chaves para armazenamento no localStorage
const ONBOARDING_STORAGE_KEY = 'servicetech_onboarding';
const PLANO_STORAGE_KEY = 'planoSelecionado';
const ESTABELECIMENTO_STORAGE_KEY = 'dadosEstabelecimento';
const SERVICOS_STORAGE_KEY = 'servicosCadastrados';
const HORARIOS_STORAGE_KEY = 'horariosComerciais';
const PAGAMENTO_STORAGE_KEY = 'dadosPagamento';

// Plano selecionado
export const salvarPlanoSelecionado = (plano: string) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(PLANO_STORAGE_KEY, plano);
  }
};

export const obterPlanoSelecionado = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem(PLANO_STORAGE_KEY);
  }
  return null;
};

// Dados do estabelecimento
export const salvarDadosEstabelecimento = (dados: any) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(ESTABELECIMENTO_STORAGE_KEY, JSON.stringify(dados));
  }
};

export const obterDadosEstabelecimento = (): any | null => {
  if (typeof window !== 'undefined') {
    const dados = localStorage.getItem(ESTABELECIMENTO_STORAGE_KEY);
    return dados ? JSON.parse(dados) : null;
  }
  return null;
};

// Serviços cadastrados
export const salvarServicosCadastrados = (servicos: any[]) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(SERVICOS_STORAGE_KEY, JSON.stringify(servicos));
  }
};

export const obterServicosCadastrados = (): any[] => {
  if (typeof window !== 'undefined') {
    const servicos = localStorage.getItem(SERVICOS_STORAGE_KEY);
    return servicos ? JSON.parse(servicos) : [];
  }
  return [];
};

// Horários comerciais
export const salvarHorariosComerciais = (horarios: any[]) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(HORARIOS_STORAGE_KEY, JSON.stringify(horarios));
  }
};

export const obterHorariosComerciais = (): any[] => {
  if (typeof window !== 'undefined') {
    const horarios = localStorage.getItem(HORARIOS_STORAGE_KEY);
    return horarios ? JSON.parse(horarios) : [];
  }
  return [];
};

// Dados de pagamento
export const salvarDadosPagamento = (dados: any) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(PAGAMENTO_STORAGE_KEY, JSON.stringify(dados));
  }
};

export const obterDadosPagamento = (): any | null => {
  if (typeof window !== 'undefined') {
    const dados = localStorage.getItem(PAGAMENTO_STORAGE_KEY);
    return dados ? JSON.parse(dados) : null;
  }
  return null;
};

// Gerenciar estado completo do onboarding
export const salvarEstadoOnboarding = (dados: any) => {
  if (typeof window !== 'undefined') {
    if (dados.plano) salvarPlanoSelecionado(dados.plano);
    if (dados.estabelecimento) salvarDadosEstabelecimento(dados.estabelecimento);
    if (dados.servicos) salvarServicosCadastrados(dados.servicos);
    if (dados.horariosComerciais) salvarHorariosComerciais(dados.horariosComerciais);
    if (dados.pagamento) salvarDadosPagamento(dados.pagamento);
    
    localStorage.setItem(ONBOARDING_STORAGE_KEY, JSON.stringify({
      plano: obterPlanoSelecionado(),
      estabelecimento: obterDadosEstabelecimento(),
      servicos: obterServicosCadastrados(),
      horariosComerciais: obterHorariosComerciais(),
      pagamento: obterDadosPagamento(),
    }));
  }
};

export const obterEstadoOnboarding = () => {
  if (typeof window !== 'undefined') {
    const dados = localStorage.getItem(ONBOARDING_STORAGE_KEY);
    if (dados) {
      try {
        return JSON.parse(dados);
      } catch (error) {
        console.error('Erro ao obter dados completos do onboarding:', error);
      }
    }
    
    return {
      plano: obterPlanoSelecionado(),
      estabelecimento: obterDadosEstabelecimento(),
      servicos: obterServicosCadastrados(),
      horariosComerciais: obterHorariosComerciais(),
      pagamento: obterDadosPagamento(),
    };
  }
  return null;
};

// Limpar dados do onboarding
export const limparDadosOnboarding = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(PLANO_STORAGE_KEY);
    localStorage.removeItem(ESTABELECIMENTO_STORAGE_KEY);
    localStorage.removeItem(SERVICOS_STORAGE_KEY);
    localStorage.removeItem(HORARIOS_STORAGE_KEY);
    localStorage.removeItem(PAGAMENTO_STORAGE_KEY);
    localStorage.removeItem(ONBOARDING_STORAGE_KEY);
  }
};

// Função para validar CNPJ
export function validarCNPJ(cnpj: string): boolean {
  cnpj = cnpj.replace(/[^\d]/g, '');

  if (cnpj.length !== 14) return false;

  // Elimina CNPJs inválidos conhecidos
  if (
    cnpj === '00000000000000' ||
    cnpj === '11111111111111' ||
    cnpj === '22222222222222' ||
    cnpj === '33333333333333' ||
    cnpj === '44444444444444' ||
    cnpj === '55555555555555' ||
    cnpj === '66666666666666' ||
    cnpj === '77777777777777' ||
    cnpj === '88888888888888' ||
    cnpj === '99999999999999'
  )
    return false;

  // Valida DVs
  let tamanho = cnpj.length - 2;
  let numeros = cnpj.substring(0, tamanho);
  const digitos = cnpj.substring(tamanho);
  let soma = 0;
  let pos = tamanho - 7;

  for (let i = tamanho; i >= 1; i--) {
    soma += parseInt(numeros.charAt(tamanho - i)) * pos--;
    if (pos < 2) pos = 9;
  }

  let resultado = soma % 11 < 2 ? 0 : 11 - (soma % 11);
  if (resultado !== parseInt(digitos.charAt(0))) return false;

  tamanho = tamanho + 1;
  numeros = cnpj.substring(0, tamanho);
  soma = 0;
  pos = tamanho - 7;

  for (let i = tamanho; i >= 1; i--) {
    soma += parseInt(numeros.charAt(tamanho - i)) * pos--;
    if (pos < 2) pos = 9;
  }

  resultado = soma % 11 < 2 ? 0 : 11 - (soma % 11);
  if (resultado !== parseInt(digitos.charAt(1))) return false;

  return true;
}

// Função para validar e-mail
export function validarEmail(email: string): boolean {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
}

// Função para validar telefone
export function validarTelefone(telefone: string): boolean {
  const telefoneNumeros = telefone.replace(/\D/g, '');
  return telefoneNumeros.length >= 10 && telefoneNumeros.length <= 11;
}

// Função para validar CEP
export function validarCEP(cep: string): boolean {
  const cepNumeros = cep.replace(/\D/g, '');
  return cepNumeros.length === 8;
}

// Função para formatar CNPJ
export function formatarCNPJ(cnpj: string): string {
  cnpj = cnpj.replace(/\D/g, '');
  return cnpj.replace(
    /^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/,
    '$1.$2.$3/$4-$5'
  );
}

// Função para formatar telefone
export function formatarTelefone(telefone: string): string {
  telefone = telefone.replace(/\D/g, '');
  if (telefone.length === 11) {
    return telefone.replace(
      /^(\d{2})(\d{5})(\d{4})$/,
      '($1) $2-$3'
    );
  } else {
    return telefone.replace(
      /^(\d{2})(\d{4})(\d{4})$/,
      '($1) $2-$3'
    );
  }
}

// Função para formatar CEP
export function formatarCEP(cep: string): string {
  cep = cep.replace(/\D/g, '');
  return cep.replace(/^(\d{5})(\d{3})$/, '$1-$2');
}

// Função para buscar endereço pelo CEP usando a API ViaCEP
export async function buscarEnderecoPorCEP(cep: string): Promise<any> {
  cep = cep.replace(/\D/g, '');
  if (cep.length !== 8) {
    throw new Error('CEP inválido');
  }

  try {
    const response = await fetch(`https://viacep.com.br/ws/${cep}/json/`);
    const data = await response.json();

    if (data.erro) {
      throw new Error('CEP não encontrado');
    }

    return {
      endereco: data.logradouro,
      bairro: data.bairro,
      cidade: data.localidade,
      estado: data.uf,
    };
  } catch (error) {
    console.error('Erro ao buscar endereço por CEP:', error);
    throw error;
  }
}