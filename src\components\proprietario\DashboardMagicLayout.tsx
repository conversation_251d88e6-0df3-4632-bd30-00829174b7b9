'use client';

import React from 'react';
import { BentoGrid } from '@/components/ui/bento-grid';
import { TextAnimate } from '@/components/ui/text-animate';
import { VisualEffects } from '@/components/ui/visual-effects';
import { useResponsiveConfig, PerformanceOptimizer } from '@/components/ui/responsive-config';
import { InformacoesEmpresaMagic } from './InformacoesEmpresaMagic';
import { MetricasNegocioMagic } from './MetricasNegocioMagic';
import { StatusPlanoSaasMagic } from './StatusPlanoSaasMagic';
import { AlertasDashboardMagic } from './AlertasDashboardMagic';
import { AcoesRapidasMagic } from './AcoesRapidasMagic';
import { motion } from 'framer-motion';

interface DashboardMagicLayoutProps {
  className?: string;
}

export function DashboardMagicLayout({ className = '' }: Readonly<DashboardMagicLayoutProps>) {
  const {
    isMobile,
    isTablet,
    isDesktop,
    prefersReducedMotion,
    particles,
    animation
  } = useResponsiveConfig();

  // Classes CSS simplificadas
  const classes = {
    padding: isMobile ? 'p-4' : 'p-6',
    grid: isMobile ? 'grid-cols-1' : 'grid-cols-6',
    header: isMobile ? 'text-2xl' : 'text-4xl',
    text: isMobile ? 'text-base' : 'text-lg',
    colSpan: isMobile ? 'col-span-1' : 'col-span-3'
  };

  return (
    <PerformanceOptimizer>
      <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 ${classes.padding} ${className}`}>
        {/* Efeitos visuais de fundo */}
        <VisualEffects
          showParticles={!prefersReducedMotion}
          showScrollProgress={true}
          showFloatingElements={!prefersReducedMotion && isDesktop}
          particleColor="#3B82F6"
          particleQuantity={isMobile ? 15 : isDesktop ? 50 : 25}
        />

      {/* Header da Dashboard */}
      <motion.div
        className="mb-8 relative z-10"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <TextAnimate
          animation={prefersReducedMotion ? "fadeIn" : "blurInUp"}
          className={`${classes.header} font-bold text-gray-900 dark:text-white mb-2`}
          by="word"
        >
          Dashboard do Proprietário
        </TextAnimate>
        <motion.p
          className={`text-gray-600 dark:text-gray-400 ${classes.text}`}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.6 }}
        >
          Gerencie seu negócio com eficiência e estilo
        </motion.p>
      </motion.div>

      {/* Alertas no topo (se houver) */}
      <motion.div 
        className="mb-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.6 }}
      >
        <AlertasDashboardMagic className="max-h-48 overflow-y-auto" />
      </motion.div>

      {/* Layout Bento Grid Principal */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.4, duration: 0.8 }}
      >
        <BentoGrid className={`${classes.grid} gap-6 auto-rows-[minmax(200px,auto)]`}>
          {/* Informações da Empresa - Destaque principal */}
          <motion.div
            className={`${classes.colSpan} row-span-1`}
            initial={{ opacity: 0, x: prefersReducedMotion ? 0 : -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5, duration: 0.6 }}
          >
            <InformacoesEmpresaMagic className="h-full" />
          </motion.div>

          {/* Status do Plano SaaS - Lateral direita */}
          <motion.div
            className={`${classes.colSpan} row-span-1`}
            initial={{ opacity: 0, x: prefersReducedMotion ? 0 : 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
          >
            <StatusPlanoSaasMagic className="h-full" />
          </motion.div>

          {/* Métricas de Negócio - Seção ampla */}
          <motion.div
            className={`col-span-full ${isMobile ? 'row-span-1' : 'row-span-2'}`}
            initial={{ opacity: 0, y: prefersReducedMotion ? 0 : 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7, duration: 0.6 }}
          >
            <MetricasNegocioMagic className="h-full" />
          </motion.div>

          {/* Ações Rápidas - Seção inferior */}
          <motion.div
            className={`col-span-full ${isMobile ? 'row-span-1' : 'row-span-2'}`}
            initial={{ opacity: 0, y: prefersReducedMotion ? 0 : 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
          >
            <AcoesRapidasMagic className="h-full" />
          </motion.div>
        </BentoGrid>
      </motion.div>



      {/* Indicador de scroll - apenas em desktop */}
      {isDesktop && (
        <motion.div
          className="fixed bottom-6 right-6 w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white shadow-lg cursor-pointer z-50"
          whileHover={{ scale: prefersReducedMotion ? 1 : 1.1 }}
          whileTap={{ scale: prefersReducedMotion ? 1 : 0.9 }}
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0, duration: 0.6 }}
        >
          <motion.div
            animate={prefersReducedMotion ? {} : { y: [-2, 2, -2] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            ↑
          </motion.div>
        </motion.div>
      )}

    </div>
    </PerformanceOptimizer>
  );
}
