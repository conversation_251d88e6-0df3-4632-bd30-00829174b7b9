'use client';

import React from 'react';
import { BentoGrid } from '@/components/ui/bento-grid';
import { TextAnimate } from '@/components/ui/text-animate';
import { VisualEffects } from '@/components/ui/visual-effects';
import { useResponsiveConfig, PerformanceOptimizer } from '@/components/ui/responsive-config';
import { InformacoesEmpresaMagic } from './InformacoesEmpresaMagic';
import { MetricasNegocioMagic } from './MetricasNegocioMagic';
import { StatusPlanoSaasMagic } from './StatusPlanoSaasMagic';
import { AlertasDashboardMagic } from './AlertasDashboardMagic';
import { AcoesRapidasMagic } from './AcoesRapidasMagic';
import { motion } from 'motion/react';

interface DashboardMagicLayoutProps {
  className?: string;
}

export function DashboardMagicLayout({ className = '' }: Readonly<DashboardMagicLayoutProps>) {
  const {
    isMobile,
    isTablet,
    isDesktop,
    prefersReducedMotion,
    bentoGrid,
    particles,
    animation,
    layout
  } = useResponsiveConfig();

  return (
    <PerformanceOptimizer>
      <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-${layout.padding} ${className}`}>
        {/* Efeitos visuais de fundo */}
        <VisualEffects
          showParticles={!prefersReducedMotion}
          showScrollProgress={true}
          showFloatingElements={!prefersReducedMotion && isDesktop}
          particleColor="#3B82F6"
          particleQuantity={particles.quantity}
        />

      {/* Header da Dashboard */}
      <motion.div
        className="mb-8 relative z-10"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: animation.duration }}
      >
        <TextAnimate
          animation={prefersReducedMotion ? "fadeIn" : "blurInUp"}
          className={`${layout.headerSize} font-bold text-gray-900 dark:text-white mb-2`}
          by="word"
        >
          Dashboard do Proprietário
        </TextAnimate>
        <motion.p
          className={`text-gray-600 dark:text-gray-400 ${isMobile ? 'text-base' : 'text-lg'}`}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: animation.stagger * 3, duration: animation.duration }}
        >
          Gerencie seu negócio com eficiência e estilo
        </motion.p>
      </motion.div>

      {/* Alertas no topo (se houver) */}
      <motion.div 
        className="mb-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.6 }}
      >
        <AlertasDashboardMagic className="max-h-48 overflow-y-auto" />
      </motion.div>

      {/* Layout Bento Grid Principal */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.4, duration: 0.8 }}
      >
        <BentoGrid className={`
          ${isMobile ? 'grid-cols-1' : isTablet ? 'grid-cols-2' : 'grid-cols-6'}
          gap-${bentoGrid.gap}
          auto-rows-[${bentoGrid.autoRows}]
        `}>
          {/* Informações da Empresa - Destaque principal */}
          <motion.div
            className={`
              ${isMobile ? 'col-span-1' : isTablet ? 'col-span-2' : 'col-span-3'}
              row-span-1
            `}
            initial={{ opacity: 0, x: prefersReducedMotion ? 0 : -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: animation.stagger * 5, duration: animation.duration }}
          >
            <InformacoesEmpresaMagic className="h-full" />
          </motion.div>

          {/* Status do Plano SaaS - Lateral direita */}
          <motion.div
            className={`
              ${isMobile ? 'col-span-1' : isTablet ? 'col-span-2' : 'col-span-3'}
              row-span-1
            `}
            initial={{ opacity: 0, x: prefersReducedMotion ? 0 : 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: animation.stagger * 6, duration: animation.duration }}
          >
            <StatusPlanoSaasMagic className="h-full" />
          </motion.div>

          {/* Métricas de Negócio - Seção ampla */}
          <motion.div
            className={`
              ${isMobile ? 'col-span-1' : isTablet ? 'col-span-2' : 'col-span-6'}
              ${isMobile ? 'row-span-1' : 'row-span-2'}
            `}
            initial={{ opacity: 0, y: prefersReducedMotion ? 0 : 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: animation.stagger * 7, duration: animation.duration }}
          >
            <MetricasNegocioMagic className="h-full" />
          </motion.div>

          {/* Ações Rápidas - Seção inferior */}
          <motion.div
            className={`
              ${isMobile ? 'col-span-1' : isTablet ? 'col-span-2' : 'col-span-6'}
              ${isMobile ? 'row-span-1' : 'row-span-2'}
            `}
            initial={{ opacity: 0, y: prefersReducedMotion ? 0 : 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: animation.stagger * 8, duration: animation.duration }}
          >
            <AcoesRapidasMagic className="h-full" />
          </motion.div>
        </BentoGrid>
      </motion.div>



      {/* Indicador de scroll - apenas em desktop */}
      {isDesktop && (
        <motion.div
          className="fixed bottom-6 right-6 w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white shadow-lg cursor-pointer z-50"
          whileHover={{ scale: prefersReducedMotion ? 1 : 1.1 }}
          whileTap={{ scale: prefersReducedMotion ? 1 : 0.9 }}
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: animation.stagger * 10, duration: animation.duration }}
        >
          <motion.div
            animate={prefersReducedMotion ? {} : { y: [-2, 2, -2] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            ↑
          </motion.div>
        </motion.div>
      )}

    </div>
    </PerformanceOptimizer>
  );
}
