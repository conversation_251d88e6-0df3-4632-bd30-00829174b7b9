// Tipos para o módulo de planos de assinatura de serviços (Premium)

// Status possíveis para assinaturas
export type StatusAssinatura = 
  | 'ativa' 
  | 'pausada' 
  | 'cancelada' 
  | 'expirada' 
  | 'trial';

// Interface principal do plano de assinatura (baseada no schema do banco)
export interface PlanoAssinatura {
  plano_cliente_id: number;
  cliente_user_id: string;
  empresa_id: number;
  servico_id: number;
  nome_plano_empresa: string;
  descricao_plano?: string;
  preco_mensal_assinatura: number;
  limite_usos_mes?: number; // null = ilimitado
  usos_consumidos_ciclo_atual: number;
  stripe_subscription_id?: string;
  status: StatusAssinatura;
  data_inicio_ciclo: string;
  data_fim_ciclo: string;
  data_proxima_cobranca?: string;
  data_cancelamento?: string;
  motivo_cancelamento?: string;
  ativo: boolean;
  created_at: string;
  updated_at: string;
}

// Interface para dados expandidos (com joins)
export interface PlanoAssinaturaExpandido extends PlanoAssinatura {
  // Dados do serviço
  servico: {
    nome_servico: string;
    descricao: string;
    preco: number;
    duracao_minutos: number;
    categoria: string;
  };
  // Dados da empresa
  empresa: {
    nome_empresa: string;
    telefone?: string;
    endereco?: string;
    cidade?: string;
    estado?: string;
  };
  // Dados do cliente
  cliente: {
    email: string;
    nome?: string;
    telefone?: string;
  };
}

// Interface para criar novo plano de assinatura (proprietário)
export interface CriarPlanoAssinaturaData {
  servico_id: number;
  nome_plano_empresa: string;
  descricao_plano?: string;
  preco_mensal_assinatura: number;
  limite_usos_mes?: number; // null = ilimitado
}

// Interface para cliente assinar um plano
export interface AssinarPlanoData {
  empresa_id: number;
  servico_id: number;
  plano_template_id?: number; // Se baseado em template da empresa
  metodo_pagamento?: 'stripe' | 'local';
}

// Interface para atualizar plano de assinatura
export interface AtualizarPlanoAssinaturaData {
  nome_plano_empresa?: string;
  descricao_plano?: string;
  preco_mensal_assinatura?: number;
  limite_usos_mes?: number;
  status?: StatusAssinatura;
  motivo_cancelamento?: string;
}

// Interface para template de plano (configurado pela empresa)
export interface TemplatePlanoAssinatura {
  template_id?: number;
  empresa_id: number;
  servico_id: number;
  nome_plano: string;
  descricao: string;
  preco_mensal: number;
  limite_usos_mes?: number;
  desconto_percentual?: number; // Desconto para assinantes
  beneficios: string[]; // Lista de benefícios
  ativo: boolean;
  created_at?: string;
  updated_at?: string;
}

// Interface para estatísticas de assinaturas (proprietário)
export interface EstatisticasAssinaturas {
  total_assinantes: number;
  assinantes_ativos: number;
  assinantes_cancelados: number;
  receita_mensal_recorrente: number;
  receita_total_periodo: number;
  taxa_cancelamento: number;
  servico_mais_assinado: {
    servico_id: number;
    nome_servico: string;
    total_assinantes: number;
  };
  crescimento_mensal: {
    mes: string;
    novos_assinantes: number;
    cancelamentos: number;
    receita: number;
  }[];
}

// Interface para benefícios de assinatura
export interface BeneficiosAssinatura {
  tem_assinatura_ativa: boolean;
  desconto_percentual: number;
  usos_restantes?: number; // null se ilimitado
  usos_totais_mes?: number; // null se ilimitado
  data_renovacao: string;
  beneficios_extras: string[];
}

// Interface para filtros de assinaturas
export interface FiltrosAssinaturas {
  status?: StatusAssinatura[];
  empresa_id?: number;
  servico_id?: number;
  data_inicio?: string;
  data_fim?: string;
  apenas_ativos?: boolean;
  ordenar_por?: 'data_criacao' | 'data_renovacao' | 'valor' | 'nome_cliente';
  ordem?: 'asc' | 'desc';
  limite?: number;
  offset?: number;
}

// Interface para resposta da API
export interface RespostaApiAssinaturas {
  success: boolean;
  data?: PlanoAssinatura[] | PlanoAssinaturaExpandido[];
  assinatura?: PlanoAssinatura | PlanoAssinaturaExpandido;
  estatisticas?: EstatisticasAssinaturas;
  beneficios?: BeneficiosAssinatura;
  total?: number;
  error?: string;
  message?: string;
}

// Interface para webhook do Stripe (assinaturas)
export interface StripeAssinaturaWebhook {
  subscription_id: string;
  customer_id: string;
  status: 'active' | 'canceled' | 'past_due' | 'unpaid' | 'trialing';
  current_period_start: number;
  current_period_end: number;
  cancel_at_period_end: boolean;
  canceled_at?: number;
  amount: number;
  currency: string;
}

// Interface para validação de dados
export interface ValidacaoPlanoAssinatura {
  nome_plano_empresa: string[];
  preco_mensal_assinatura: string[];
  limite_usos_mes: string[];
  servico_id: string[];
}

// Constantes para validação
export const LIMITES_PLANOS_ASSINATURA = {
  nome_plano_min: 3,
  nome_plano_max: 255,
  descricao_max: 1000,
  preco_min: 0.01,
  preco_max: 9999.99,
  usos_min: 1,
  usos_max: 999,
} as const;

// Status válidos para transições
export const TRANSICOES_STATUS_VALIDAS: Record<StatusAssinatura, StatusAssinatura[]> = {
  ativa: ['pausada', 'cancelada'],
  pausada: ['ativa', 'cancelada'],
  cancelada: [], // Não pode sair do estado cancelado
  expirada: ['ativa'], // Pode reativar se pagar
  trial: ['ativa', 'cancelada'],
};

// Tipos para hooks
export interface UseAssinaturasResult {
  assinaturas: PlanoAssinaturaExpandido[];
  loading: boolean;
  error: string | null;
  criarAssinatura: (data: AssinarPlanoData) => Promise<boolean>;
  cancelarAssinatura: (id: number, motivo?: string) => Promise<boolean>;
  pausarAssinatura: (id: number) => Promise<boolean>;
  reativarAssinatura: (id: number) => Promise<boolean>;
  obterBeneficios: (servicoId: number) => Promise<BeneficiosAssinatura | null>;
  refresh: () => void;
}

export interface UsePlanosAssinaturaResult {
  planos: TemplatePlanoAssinatura[];
  assinantes: PlanoAssinaturaExpandido[];
  estatisticas: EstatisticasAssinaturas | null;
  loading: boolean;
  error: string | null;
  criarPlano: (data: CriarPlanoAssinaturaData) => Promise<boolean>;
  atualizarPlano: (id: number, data: AtualizarPlanoAssinaturaData) => Promise<boolean>;
  excluirPlano: (id: number) => Promise<boolean>;
  obterEstatisticas: () => Promise<void>;
  refresh: () => void;
}

// Utilitários de tipo
export type PlanoAssinaturaKeys = keyof PlanoAssinatura;
export type StatusAssinaturaKeys = keyof typeof TRANSICOES_STATUS_VALIDAS;
