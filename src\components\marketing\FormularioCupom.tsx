'use client';

import { useState, useEffect } from 'react';
import { useCupons } from '@/hooks/useCupons';
import { useServicos } from '@/hooks/useServicos';
import { Cupom, CriarCupomData } from '@/types/marketing';

interface FormularioCupomProps {
  cupom?: Cupom | null;
  onSalvar: () => void;
  onCancelar: () => void;
}

export function FormularioCupom({ cupom, onSalvar, onCancelar }: FormularioCupomProps) {
  const { criarCupom, atualizarCupom, carregando, erro } = useCupons();
  const { servicos, buscarServicos } = useServicos();

  const [dados, setDados] = useState<CriarCupomData>({
    codigo_cupom: '',
    nome_cupom: '',
    descricao: '',
    tipo_desconto: 'percentual',
    valor_desconto: 0,
    valor_minimo_pedido: 0,
    limite_usos_total: undefined,
    limite_usos_por_cliente: 1,
    data_inicio: '',
    data_fim: '',
    ativo: true,
    aplicavel_servicos: undefined
  });

  const [errosValidacao, setErrosValidacao] = useState<Record<string, string>>({});
  const [servicosSelecionados, setServicosSelecionados] = useState<number[]>([]);
  const [aplicarTodosServicos, setAplicarTodosServicos] = useState(true);

  // Carregar dados do cupom para edição
  useEffect(() => {
    if (cupom) {
      setDados({
        codigo_cupom: cupom.codigo_cupom,
        nome_cupom: cupom.nome_cupom,
        descricao: cupom.descricao,
        tipo_desconto: cupom.tipo_desconto,
        valor_desconto: cupom.valor_desconto,
        valor_minimo_pedido: cupom.valor_minimo_pedido,
        limite_usos_total: cupom.limite_usos_total,
        limite_usos_por_cliente: cupom.limite_usos_por_cliente,
        data_inicio: cupom.data_inicio.split('T')[0],
        data_fim: cupom.data_fim.split('T')[0],
        ativo: cupom.ativo,
        aplicavel_servicos: cupom.aplicavel_servicos
      });

      if (cupom.aplicavel_servicos && cupom.aplicavel_servicos.length > 0) {
        setServicosSelecionados(cupom.aplicavel_servicos);
        setAplicarTodosServicos(false);
      }
    }
  }, [cupom]);

  // Carregar serviços
  useEffect(() => {
    buscarServicos();
  }, [buscarServicos]);

  // Validar formulário
  const validarFormulario = (): boolean => {
    const erros: Record<string, string> = {};

    if (!dados.codigo_cupom || dados.codigo_cupom.length < 3) {
      erros.codigo_cupom = 'Código deve ter pelo menos 3 caracteres';
    }

    if (!dados.nome_cupom || dados.nome_cupom.length < 3) {
      erros.nome_cupom = 'Nome deve ter pelo menos 3 caracteres';
    }

    if (!dados.descricao || dados.descricao.length < 10) {
      erros.descricao = 'Descrição deve ter pelo menos 10 caracteres';
    }

    if (!dados.valor_desconto || dados.valor_desconto <= 0) {
      erros.valor_desconto = 'Valor do desconto deve ser maior que zero';
    }

    if (dados.tipo_desconto === 'percentual' && dados.valor_desconto > 90) {
      erros.valor_desconto = 'Desconto percentual não pode ser maior que 90%';
    }

    if (dados.valor_minimo_pedido && dados.valor_minimo_pedido < 0) {
      erros.valor_minimo_pedido = 'Valor mínimo não pode ser negativo';
    }

    if (dados.tipo_desconto === 'valor_fixo' && dados.valor_minimo_pedido && dados.valor_desconto >= dados.valor_minimo_pedido) {
      erros.valor_minimo_pedido = 'Valor mínimo deve ser maior que o desconto';
    }

    if (!dados.data_inicio) {
      erros.data_inicio = 'Data de início é obrigatória';
    }

    if (!dados.data_fim) {
      erros.data_fim = 'Data de fim é obrigatória';
    }

    if (dados.data_inicio && dados.data_fim && new Date(dados.data_fim) <= new Date(dados.data_inicio)) {
      erros.data_fim = 'Data de fim deve ser posterior à data de início';
    }

    if (dados.limite_usos_total && dados.limite_usos_total <= 0) {
      erros.limite_usos_total = 'Limite de usos deve ser maior que zero';
    }

    if (dados.limite_usos_por_cliente && dados.limite_usos_por_cliente <= 0) {
      erros.limite_usos_por_cliente = 'Limite por cliente deve ser maior que zero';
    }

    setErrosValidacao(erros);
    return Object.keys(erros).length === 0;
  };

  // Salvar cupom
  const handleSalvar = async () => {
    if (!validarFormulario()) {
      return;
    }

    const dadosParaSalvar = {
      ...dados,
      codigo_cupom: dados.codigo_cupom.toUpperCase(),
      aplicavel_servicos: aplicarTodosServicos ? undefined : servicosSelecionados
    };

    let sucesso = false;
    if (cupom) {
      sucesso = await atualizarCupom(cupom.cupom_id, dadosParaSalvar);
    } else {
      sucesso = await criarCupom(dadosParaSalvar);
    }

    if (sucesso) {
      onSalvar();
    }
  };

  // Gerar código automático
  const gerarCodigo = () => {
    const codigo = Math.random().toString(36).substring(2, 8).toUpperCase();
    setDados(prev => ({ ...prev, codigo_cupom: codigo }));
  };

  return (
    <div className="space-y-6">
      {/* Cabeçalho */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            {cupom ? 'Editar Cupom' : 'Novo Cupom'}
          </h2>
          <p className="text-gray-600">
            {cupom ? 'Atualize as informações do cupom' : 'Crie um novo cupom promocional'}
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={onCancelar}
            className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors"
          >
            Cancelar
          </button>
          <button
            onClick={handleSalvar}
            disabled={carregando}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            {carregando ? 'Salvando...' : 'Salvar'}
          </button>
        </div>
      </div>

      {/* Mensagem de erro */}
      {erro && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">{erro}</p>
        </div>
      )}

      {/* Formulário */}
      <div className="bg-white rounded-lg shadow border p-6 space-y-6">
        {/* Informações básicas */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Código do Cupom *
            </label>
            <div className="flex space-x-2">
              <input
                type="text"
                value={dados.codigo_cupom}
                onChange={(e) => setDados(prev => ({ ...prev, codigo_cupom: e.target.value.toUpperCase() }))}
                placeholder="Ex: DESCONTO10"
                className={`flex-1 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errosValidacao.codigo_cupom ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              <button
                type="button"
                onClick={gerarCodigo}
                className="bg-gray-100 text-gray-700 px-3 py-2 rounded-md hover:bg-gray-200 transition-colors"
              >
                Gerar
              </button>
            </div>
            {errosValidacao.codigo_cupom && (
              <p className="mt-1 text-sm text-red-600">{errosValidacao.codigo_cupom}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nome do Cupom *
            </label>
            <input
              type="text"
              value={dados.nome_cupom}
              onChange={(e) => setDados(prev => ({ ...prev, nome_cupom: e.target.value }))}
              placeholder="Ex: Desconto de Primeira Compra"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errosValidacao.nome_cupom ? 'border-red-300' : 'border-gray-300'
              }`}
            />
            {errosValidacao.nome_cupom && (
              <p className="mt-1 text-sm text-red-600">{errosValidacao.nome_cupom}</p>
            )}
          </div>
        </div>

        {/* Descrição */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Descrição *
          </label>
          <textarea
            value={dados.descricao}
            onChange={(e) => setDados(prev => ({ ...prev, descricao: e.target.value }))}
            placeholder="Descreva os benefícios e condições do cupom"
            rows={3}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errosValidacao.descricao ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          {errosValidacao.descricao && (
            <p className="mt-1 text-sm text-red-600">{errosValidacao.descricao}</p>
          )}
        </div>

        {/* Configurações de desconto */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tipo de Desconto *
            </label>
            <select
              value={dados.tipo_desconto}
              onChange={(e) => setDados(prev => ({ ...prev, tipo_desconto: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="percentual">Percentual (%)</option>
              <option value="valor_fixo">Valor Fixo (R$)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Valor do Desconto *
            </label>
            <div className="relative">
              <span className="absolute left-3 top-2 text-gray-500">
                {dados.tipo_desconto === 'valor_fixo' ? 'R$' : '%'}
              </span>
              <input
                type="number"
                value={dados.valor_desconto}
                onChange={(e) => setDados(prev => ({ ...prev, valor_desconto: parseFloat(e.target.value) || 0 }))}
                min="0"
                max={dados.tipo_desconto === 'percentual' ? 90 : undefined}
                step={dados.tipo_desconto === 'valor_fixo' ? '0.01' : '1'}
                className={`w-full pl-8 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errosValidacao.valor_desconto ? 'border-red-300' : 'border-gray-300'
                }`}
              />
            </div>
            {errosValidacao.valor_desconto && (
              <p className="mt-1 text-sm text-red-600">{errosValidacao.valor_desconto}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Valor Mínimo do Pedido
            </label>
            <div className="relative">
              <span className="absolute left-3 top-2 text-gray-500">R$</span>
              <input
                type="number"
                value={dados.valor_minimo_pedido || ''}
                onChange={(e) => setDados(prev => ({ ...prev, valor_minimo_pedido: parseFloat(e.target.value) || undefined }))}
                min="0"
                step="0.01"
                placeholder="0.00"
                className={`w-full pl-8 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errosValidacao.valor_minimo_pedido ? 'border-red-300' : 'border-gray-300'
                }`}
              />
            </div>
            {errosValidacao.valor_minimo_pedido && (
              <p className="mt-1 text-sm text-red-600">{errosValidacao.valor_minimo_pedido}</p>
            )}
          </div>
        </div>

        {/* Período de validade */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Data de Início *
            </label>
            <input
              type="date"
              value={dados.data_inicio}
              onChange={(e) => setDados(prev => ({ ...prev, data_inicio: e.target.value }))}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errosValidacao.data_inicio ? 'border-red-300' : 'border-gray-300'
              }`}
            />
            {errosValidacao.data_inicio && (
              <p className="mt-1 text-sm text-red-600">{errosValidacao.data_inicio}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Data de Fim *
            </label>
            <input
              type="date"
              value={dados.data_fim}
              onChange={(e) => setDados(prev => ({ ...prev, data_fim: e.target.value }))}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errosValidacao.data_fim ? 'border-red-300' : 'border-gray-300'
              }`}
            />
            {errosValidacao.data_fim && (
              <p className="mt-1 text-sm text-red-600">{errosValidacao.data_fim}</p>
            )}
          </div>
        </div>

        {/* Limites de uso */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Limite Total de Usos
            </label>
            <input
              type="number"
              value={dados.limite_usos_total || ''}
              onChange={(e) => setDados(prev => ({ ...prev, limite_usos_total: parseInt(e.target.value) || undefined }))}
              min="1"
              placeholder="Ilimitado"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errosValidacao.limite_usos_total ? 'border-red-300' : 'border-gray-300'
              }`}
            />
            {errosValidacao.limite_usos_total && (
              <p className="mt-1 text-sm text-red-600">{errosValidacao.limite_usos_total}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Limite por Cliente
            </label>
            <input
              type="number"
              value={dados.limite_usos_por_cliente}
              onChange={(e) => setDados(prev => ({ ...prev, limite_usos_por_cliente: parseInt(e.target.value) || 1 }))}
              min="1"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errosValidacao.limite_usos_por_cliente ? 'border-red-300' : 'border-gray-300'
              }`}
            />
            {errosValidacao.limite_usos_por_cliente && (
              <p className="mt-1 text-sm text-red-600">{errosValidacao.limite_usos_por_cliente}</p>
            )}
          </div>
        </div>

        {/* Serviços aplicáveis */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Serviços Aplicáveis
          </label>
          
          <div className="space-y-3">
            <label className="flex items-center">
              <input
                type="radio"
                checked={aplicarTodosServicos}
                onChange={() => setAplicarTodosServicos(true)}
                className="mr-2"
              />
              <span>Aplicar a todos os serviços</span>
            </label>
            
            <label className="flex items-center">
              <input
                type="radio"
                checked={!aplicarTodosServicos}
                onChange={() => setAplicarTodosServicos(false)}
                className="mr-2"
              />
              <span>Aplicar apenas a serviços específicos</span>
            </label>
          </div>

          {!aplicarTodosServicos && (
            <div className="mt-3 max-h-40 overflow-y-auto border border-gray-300 rounded-md p-3">
              {servicos.map((servico) => (
                <label key={servico.servico_id} className="flex items-center py-1">
                  <input
                    type="checkbox"
                    checked={servicosSelecionados.includes(servico.servico_id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setServicosSelecionados(prev => [...prev, servico.servico_id]);
                      } else {
                        setServicosSelecionados(prev => prev.filter(id => id !== servico.servico_id));
                      }
                    }}
                    className="mr-2"
                  />
                  <span className="text-sm">
                    {servico.nome_servico} - R$ {servico.preco.toFixed(2)}
                  </span>
                </label>
              ))}
            </div>
          )}
        </div>

        {/* Status */}
        <div>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={dados.ativo}
              onChange={(e) => setDados(prev => ({ ...prev, ativo: e.target.checked }))}
              className="mr-2"
            />
            <span className="text-sm font-medium text-gray-700">Cupom ativo</span>
          </label>
        </div>
      </div>
    </div>
  );
}
