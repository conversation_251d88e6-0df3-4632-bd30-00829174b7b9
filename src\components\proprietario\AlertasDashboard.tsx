'use client';

import React from 'react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useEmpresaProprietario } from '@/hooks/useEmpresaProprietario';

interface Alerta {
  id: string;
  tipo: 'critico' | 'importante' | 'informativo';
  titulo: string;
  descricao: string;
  icone: string;
  acao?: {
    texto: string;
    href: string;
  };
  data?: Date;
}

interface AlertasDashboardProps {
  agendamentosProximoPrazo?: any[];
  className?: string;
}

export function AlertasDashboard({ 
  agendamentosProximoPrazo = [], 
  className = '' 
}: AlertasDashboardProps) {
  const { 
    empresa, 
    planoSaas, 
    metricas, 
    statusConfiguracao,
    precisaConfiguracaoInicial,
    podeReceberPagamentos,
    obterProximosPassos
  } = useEmpresaProprietario();

  const gerarAlertas = (): Alerta[] => {
    const alertas: Alerta[] = [];

    // Alertas críticos
    if (!empresa) {
      alertas.push({
        id: 'sem-empresa',
        tipo: 'critico',
        titulo: 'Empresa não configurada',
        descricao: 'Você precisa criar uma empresa para começar a usar o ServiceTech.',
        icone: '🏢',
        acao: {
          texto: 'Criar Empresa',
          href: '/onboarding'
        }
      });
    }

    if (empresa && !empresa.status || empresa?.status !== 'ativo') {
      alertas.push({
        id: 'empresa-inativa',
        tipo: 'critico',
        titulo: 'Empresa inativa',
        descricao: 'Sua empresa está inativa e não pode receber agendamentos.',
        icone: '⚠️',
        acao: {
          texto: 'Ativar Empresa',
          href: '/proprietario/configuracoes'
        }
      });
    }

    if (!planoSaas || planoSaas.status_assinatura !== 'ativa') {
      alertas.push({
        id: 'plano-inativo',
        tipo: 'critico',
        titulo: 'Plano SaaS inativo',
        descricao: 'Sua assinatura não está ativa. Reative para continuar usando o ServiceTech.',
        icone: '💳',
        acao: {
          texto: 'Reativar Plano',
          href: '/planos'
        }
      });
    }

    // Alertas importantes
    if (agendamentosProximoPrazo.length > 0) {
      alertas.push({
        id: 'agendamentos-prazo',
        tipo: 'importante',
        titulo: `${agendamentosProximoPrazo.length} agendamento(s) próximo(s) ao prazo`,
        descricao: 'Você tem agendamentos pendentes que precisam ser confirmados ou recusados em breve.',
        icone: '⏰',
        acao: {
          texto: 'Ver Agendamentos',
          href: '/proprietario/agendamentos'
        }
      });
    }

    if (!podeReceberPagamentos()) {
      alertas.push({
        id: 'stripe-pendente',
        tipo: 'importante',
        titulo: 'Pagamentos online não configurados',
        descricao: 'Configure o Stripe Connect para receber pagamentos online dos seus clientes.',
        icone: '💳',
        acao: {
          texto: 'Configurar Pagamentos',
          href: '/proprietario/configuracoes?tab=pagamentos'
        }
      });
    }

    if (precisaConfiguracaoInicial()) {
      const proximosPassos = obterProximosPassos();
      if (proximosPassos.length > 0) {
        alertas.push({
          id: 'configuracao-pendente',
          tipo: 'importante',
          titulo: 'Configuração incompleta',
          descricao: `Complete a configuração da sua empresa (${statusConfiguracao?.percentual_conclusao}% concluído).`,
          icone: '⚙️',
          acao: {
            texto: 'Continuar Configuração',
            href: '/proprietario/configuracoes'
          }
        });
      }
    }

    if (metricas && metricas.total_servicos_ativos === 0) {
      alertas.push({
        id: 'sem-servicos',
        tipo: 'importante',
        titulo: 'Nenhum serviço cadastrado',
        descricao: 'Cadastre pelo menos um serviço para começar a receber agendamentos.',
        icone: '🛠️',
        acao: {
          texto: 'Cadastrar Serviços',
          href: '/proprietario/servicos'
        }
      });
    }

    if (metricas && metricas.total_colaboradores_ativos === 0) {
      alertas.push({
        id: 'sem-colaboradores',
        tipo: 'importante',
        titulo: 'Nenhum colaborador ativo',
        descricao: 'Adicione colaboradores ou ative seu perfil como prestador de serviços.',
        icone: '👥',
        acao: {
          texto: 'Gerenciar Colaboradores',
          href: '/proprietario/colaboradores'
        }
      });
    }

    // Alertas informativos
    if (metricas && metricas.agendamentos_pendentes > 0) {
      alertas.push({
        id: 'agendamentos-pendentes',
        tipo: 'informativo',
        titulo: `${metricas.agendamentos_pendentes} agendamento(s) pendente(s)`,
        descricao: 'Você tem agendamentos aguardando confirmação.',
        icone: '📅',
        acao: {
          texto: 'Ver Agendamentos',
          href: '/proprietario/agendamentos?status=pendente'
        }
      });
    }

    if (planoSaas && !planoSaas.recursos_premium) {
      alertas.push({
        id: 'upgrade-premium',
        tipo: 'informativo',
        titulo: 'Desbloqueie recursos Premium',
        descricao: 'Acesse relatórios avançados, campanhas de marketing e muito mais.',
        icone: '🌟',
        acao: {
          texto: 'Upgrade Premium',
          href: '/planos'
        }
      });
    }

    return alertas;
  };

  const alertas = gerarAlertas();

  if (alertas.length === 0) {
    return (
      <Card className={`bg-green-50 border-green-200 ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="text-green-600 text-2xl">✅</div>
            <div>
              <div className="font-semibold text-green-800">Tudo funcionando perfeitamente!</div>
              <div className="text-sm text-green-700">
                Sua empresa está configurada e pronta para receber agendamentos.
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getAlertStyle = (tipo: string) => {
    switch (tipo) {
      case 'critico':
        return {
          bg: 'bg-red-50',
          border: 'border-red-200',
          titleColor: 'text-red-800',
          textColor: 'text-red-700',
          buttonClass: 'bg-red-600 hover:bg-red-700'
        };
      case 'importante':
        return {
          bg: 'bg-orange-50',
          border: 'border-orange-200',
          titleColor: 'text-orange-800',
          textColor: 'text-orange-700',
          buttonClass: 'bg-orange-600 hover:bg-orange-700'
        };
      case 'informativo':
        return {
          bg: 'bg-blue-50',
          border: 'border-blue-200',
          titleColor: 'text-blue-800',
          textColor: 'text-blue-700',
          buttonClass: 'bg-blue-600 hover:bg-blue-700'
        };
      default:
        return {
          bg: 'bg-gray-50',
          border: 'border-gray-200',
          titleColor: 'text-gray-800',
          textColor: 'text-gray-700',
          buttonClass: 'bg-gray-600 hover:bg-gray-700'
        };
    }
  };

  // Ordenar alertas por prioridade
  const alertasOrdenados = alertas.sort((a, b) => {
    const prioridade = { critico: 3, importante: 2, informativo: 1 };
    return prioridade[b.tipo] - prioridade[a.tipo];
  });

  return (
    <div className={`space-y-3 ${className}`}>
      {alertasOrdenados.map((alerta) => {
        const style = getAlertStyle(alerta.tipo);
        
        return (
          <Card key={alerta.id} className={`${style.bg} ${style.border} border-2`}>
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="text-2xl flex-shrink-0">
                  {alerta.icone}
                </div>
                <div className="flex-1 min-w-0">
                  <div className={`font-semibold ${style.titleColor}`}>
                    {alerta.titulo}
                  </div>
                  <div className={`text-sm ${style.textColor} mt-1`}>
                    {alerta.descricao}
                  </div>
                </div>
                {alerta.acao && (
                  <div className="flex-shrink-0">
                    <Link href={alerta.acao.href}>
                      <Button size="sm" className={style.buttonClass}>
                        {alerta.acao.texto}
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
