/**
 * Utilitários para lógica de Round-Robin
 * Implementação da Tarefa #17 - Lógica de Agendamento Round-Robin
 * 
 * Este módulo implementa um algoritmo de round-robin inteligente que:
 * 1. Distribui agendamentos igualmente entre colaboradores
 * 2. Considera o histórico de agendamentos recentes
 * 3. Prioriza colaboradores menos utilizados
 * 4. Mantém estatísticas de distribuição
 */

import { createClient } from '@/utils/supabase/client';
import type { 
  ColaboradorRoundRobin, 
  EstatisticasRoundRobin, 
  ParametrosRoundRobin, 
  ResultadoRoundRobin,
  ConfiguracaoRoundRobin 
} from '@/types/roundRobin';

/**
 * Configuração padrão para o algoritmo round-robin
 */
const CONFIGURACAO_PADRAO: ConfiguracaoRoundRobin = {
  periodo_dias: 30,
  apenas_confirmados: false,
  peso_temporal: 1,
  priorizar_inativos: true
};

/**
 * Busca estatísticas de agendamentos dos colaboradores
 */
export async function buscarEstatisticasColaboradores(
  empresa_id: number,
  servico_id: number,
  colaboradores_ids: string[],
  configuracao: Partial<ConfiguracaoRoundRobin> = {}
): Promise<ColaboradorRoundRobin[]> {
  const config = { ...CONFIGURACAO_PADRAO, ...configuracao };
  const supabase = createClient();

  // Calcular data limite baseada no período
  const dataLimite = new Date();
  dataLimite.setDate(dataLimite.getDate() - config.periodo_dias);

  try {
    // Buscar dados dos colaboradores
    const { data: colaboradores, error: colaboradoresError } = await supabase
      .from('colaboradores_empresa')
      .select(`
        colaborador_user_id,
        auth_users:colaborador_user_id (
          raw_user_meta_data
        )
      `)
      .eq('empresa_id', empresa_id)
      .in('colaborador_user_id', colaboradores_ids)
      .eq('ativo', true)
      .eq('ativo_como_prestador', true);

    if (colaboradoresError) {
      console.error('Erro ao buscar colaboradores:', colaboradoresError);
      throw new Error('Erro ao buscar dados dos colaboradores');
    }

    // Buscar estatísticas de agendamentos
    const statusFiltro = config.apenas_confirmados 
      ? ['Confirmado'] 
      : ['Pendente', 'Confirmado'];

    const { data: agendamentos, error: agendamentosError } = await supabase
      .from('agendamentos')
      .select('colaborador_user_id, created_at')
      .eq('empresa_id', empresa_id)
      .eq('servico_id', servico_id)
      .in('colaborador_user_id', colaboradores_ids)
      .in('status_agendamento', statusFiltro)
      .gte('created_at', dataLimite.toISOString())
      .order('created_at', { ascending: false });

    if (agendamentosError) {
      console.error('Erro ao buscar agendamentos:', agendamentosError);
      throw new Error('Erro ao buscar histórico de agendamentos');
    }

    // Processar dados dos colaboradores
    const colaboradoresRoundRobin: ColaboradorRoundRobin[] = colaboradores.map(colaborador => {
      const agendamentosColaborador = agendamentos?.filter(
        a => a.colaborador_user_id === colaborador.colaborador_user_id
      ) || [];

      const userData = (colaborador as any).auth_users?.raw_user_meta_data || {};
      
      return {
        colaborador_user_id: colaborador.colaborador_user_id,
        name: userData.name || 'Colaborador',
        email: userData.email || '',
        total_agendamentos: agendamentosColaborador.length,
        ultimo_agendamento: agendamentosColaborador.length > 0 
          ? agendamentosColaborador[0].created_at 
          : undefined
      };
    });

    return colaboradoresRoundRobin;

  } catch (error) {
    console.error('Erro ao buscar estatísticas de colaboradores:', error);
    throw error;
  }
}

/**
 * Calcula estatísticas de distribuição de agendamentos
 */
export function calcularEstatisticasDistribuicao(
  colaboradores: ColaboradorRoundRobin[]
): EstatisticasRoundRobin {
  const totalAgendamentos = colaboradores.reduce((sum, c) => sum + c.total_agendamentos, 0);
  
  const distribuicao = colaboradores.map(colaborador => ({
    colaborador_user_id: colaborador.colaborador_user_id,
    name: colaborador.name,
    count: colaborador.total_agendamentos,
    percentual: totalAgendamentos > 0 
      ? (colaborador.total_agendamentos / totalAgendamentos) * 100 
      : 0
  }));

  // Encontrar colaboradores com mais e menos agendamentos
  const ordenados = [...colaboradores].sort((a, b) => a.total_agendamentos - b.total_agendamentos);
  const menosUtilizado = ordenados[0];
  const maisUtilizado = ordenados[ordenados.length - 1];

  return {
    total_colaboradores: colaboradores.length,
    total_agendamentos: totalAgendamentos,
    distribuicao,
    colaborador_menos_utilizado: menosUtilizado.colaborador_user_id,
    colaborador_mais_utilizado: maisUtilizado.colaborador_user_id,
    diferenca_maxima: maisUtilizado.total_agendamentos - menosUtilizado.total_agendamentos
  };
}

/**
 * Seleciona o próximo colaborador usando algoritmo round-robin
 */
export function selecionarProximoColaborador(
  colaboradores: ColaboradorRoundRobin[],
  configuracao: Partial<ConfiguracaoRoundRobin> = {}
): ResultadoRoundRobin {
  const config = { ...CONFIGURACAO_PADRAO, ...configuracao };
  
  if (colaboradores.length === 0) {
    throw new Error('Nenhum colaborador disponível para seleção');
  }

  if (colaboradores.length === 1) {
    return {
      colaborador_selecionado: colaboradores[0].colaborador_user_id,
      motivo_selecao: 'ultimo_disponivel',
      estatisticas_antes: calcularEstatisticasDistribuicao(colaboradores)
    };
  }

  const estatisticasAntes = calcularEstatisticasDistribuicao(colaboradores);

  // Algoritmo Round-Robin: selecionar colaborador com menos agendamentos
  const colaboradoresOrdenados = [...colaboradores].sort((a, b) => {
    // Primeiro critério: número de agendamentos (menor primeiro)
    if (a.total_agendamentos !== b.total_agendamentos) {
      return a.total_agendamentos - b.total_agendamentos;
    }

    // Segundo critério: priorizar quem não teve agendamentos recentes
    if (config.priorizar_inativos) {
      if (!a.ultimo_agendamento && b.ultimo_agendamento) return -1;
      if (a.ultimo_agendamento && !b.ultimo_agendamento) return 1;
      
      if (a.ultimo_agendamento && b.ultimo_agendamento) {
        // Quem teve agendamento mais antigo tem prioridade
        return new Date(a.ultimo_agendamento).getTime() - new Date(b.ultimo_agendamento).getTime();
      }
    }

    // Terceiro critério: ordem alfabética para consistência
    return a.name.localeCompare(b.name);
  });

  const colaboradorSelecionado = colaboradoresOrdenados[0];

  // Simular estatísticas após a seleção
  const colaboradoresDepois = colaboradores.map(c => 
    c.colaborador_user_id === colaboradorSelecionado.colaborador_user_id
      ? { ...c, total_agendamentos: c.total_agendamentos + 1 }
      : c
  );

  return {
    colaborador_selecionado: colaboradorSelecionado.colaborador_user_id,
    motivo_selecao: 'round_robin',
    estatisticas_antes: estatisticasAntes,
    estatisticas_depois: calcularEstatisticasDistribuicao(colaboradoresDepois)
  };
}

/**
 * Função principal para seleção de colaborador via round-robin
 * Esta é a função que será chamada pela API de agendamentos
 */
export async function selecionarColaboradorRoundRobin(
  parametros: ParametrosRoundRobin,
  configuracao: Partial<ConfiguracaoRoundRobin> = {}
): Promise<ResultadoRoundRobin> {
  try {
    // Buscar estatísticas atualizadas dos colaboradores
    const colaboradoresIds = parametros.colaboradores_disponiveis.map(c => c.colaborador_user_id);
    
    const colaboradoresComEstatisticas = await buscarEstatisticasColaboradores(
      parametros.empresa_id,
      parametros.servico_id,
      colaboradoresIds,
      configuracao
    );

    // Selecionar próximo colaborador
    const resultado = selecionarProximoColaborador(colaboradoresComEstatisticas, configuracao);

    console.log('🎯 Round-Robin - Colaborador selecionado:', {
      colaborador_id: resultado.colaborador_selecionado,
      motivo: resultado.motivo_selecao,
      estatisticas: resultado.estatisticas_antes
    });

    return resultado;

  } catch (error) {
    console.error('❌ Erro na seleção round-robin:', error);
    
    // Fallback: seleção aleatória se houver erro
    const indiceAleatorio = Math.floor(Math.random() * parametros.colaboradores_disponiveis.length);
    const colaboradorFallback = parametros.colaboradores_disponiveis[indiceAleatorio];

    return {
      colaborador_selecionado: colaboradorFallback.colaborador_user_id,
      motivo_selecao: 'ultimo_disponivel',
      estatisticas_antes: calcularEstatisticasDistribuicao(parametros.colaboradores_disponiveis)
    };
  }
}
