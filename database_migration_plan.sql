-- =====================================================
-- PLANO DE MIGRAÇÃO DO BANCO DE DADOS - SERVICETECH
-- =====================================================
-- Este arquivo contém o esquema completo para reestruturação
-- do banco de dados conforme requisitos do lr raiz.md

-- =====================================================
-- 1. NOVAS TABELAS
-- =====================================================

-- Tabela de papéis do sistema
CREATE TABLE IF NOT EXISTS roles (
    role_id SERIAL PRIMARY KEY,
    nome_papel VARCHAR(50) NOT NULL UNIQUE,
    descricao TEXT,
    is_global BOOLEAN DEFAULT false, -- true para Admin/Usuario, false para Proprietario/Colaborador
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tabela de associação usuário-papel-empresa (controle granular)
CREATE TABLE IF NOT EXISTS user_roles (
    user_role_id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role_id INTEGER NOT NULL REFERENCES roles(role_id) ON DELETE CASCADE,
    empresa_id INTEGER REFERENCES empresas(empresa_id) ON DELETE CASCADE,
    ativo BOOLEAN DEFAULT true,
    data_atribuicao TIMESTAMPTZ DEFAULT NOW(),
    data_expiracao TIMESTAMPTZ, -- Para papéis temporários
    atribuido_por UUID REFERENCES auth.users(id), -- Quem atribuiu o papel
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT user_roles_empresa_check CHECK (
        (role_id IN (SELECT role_id FROM roles WHERE is_global = true) AND empresa_id IS NULL) OR
        (role_id IN (SELECT role_id FROM roles WHERE is_global = false) AND empresa_id IS NOT NULL)
    ),
    CONSTRAINT user_roles_unique_active UNIQUE (user_id, role_id, empresa_id, ativo)
);

-- Tabela de convites para colaboradores
CREATE TABLE IF NOT EXISTS convites_colaborador (
    convite_id SERIAL PRIMARY KEY,
    empresa_id INTEGER NOT NULL REFERENCES empresas(empresa_id) ON DELETE CASCADE,
    email_convidado VARCHAR(255) NOT NULL,
    token_convite VARCHAR(255) NOT NULL UNIQUE,
    criado_por UUID NOT NULL REFERENCES auth.users(id),
    status_convite VARCHAR(20) DEFAULT 'pendente' CHECK (status_convite IN ('pendente', 'aceito', 'expirado', 'cancelado')),
    data_expiracao TIMESTAMPTZ NOT NULL DEFAULT (NOW() + INTERVAL '24 hours'),
    data_aceite TIMESTAMPTZ,
    user_id_aceite UUID REFERENCES auth.users(id),
    observacoes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Renomear e reestruturar tabela de assinaturas de serviços
-- (A tabela atual planos_servico_cliente será renomeada)

-- =====================================================
-- 2. MODIFICAÇÕES EM TABELAS EXISTENTES
-- =====================================================

-- Adicionar campos necessários na tabela empresas
ALTER TABLE empresas 
ADD COLUMN IF NOT EXISTS website VARCHAR(255),
ADD COLUMN IF NOT EXISTS instagram VARCHAR(255),
ADD COLUMN IF NOT EXISTS whatsapp VARCHAR(20),
ADD COLUMN IF NOT EXISTS email_contato VARCHAR(255),
ADD COLUMN IF NOT EXISTS configuracao_notificacoes JSONB DEFAULT '{"email": true, "push": true, "sms": false}'::jsonb,
ADD COLUMN IF NOT EXISTS configuracao_agendamento JSONB DEFAULT '{"confirmacao_automatica": false, "prazo_confirmacao_horas": 24}'::jsonb,
ADD COLUMN IF NOT EXISTS dias_funcionamento JSONB DEFAULT '["segunda", "terca", "quarta", "quinta", "sexta", "sabado"]'::jsonb,
ADD COLUMN IF NOT EXISTS timezone VARCHAR(50) DEFAULT 'America/Sao_Paulo';

-- Melhorar tabela de colaboradores_empresa
ALTER TABLE colaboradores_empresa 
ADD COLUMN IF NOT EXISTS papel_empresa VARCHAR(50) DEFAULT 'colaborador' CHECK (papel_empresa IN ('proprietario', 'colaborador', 'gerente')),
ADD COLUMN IF NOT EXISTS data_inicio_trabalho DATE,
ADD COLUMN IF NOT EXISTS data_fim_trabalho DATE,
ADD COLUMN IF NOT EXISTS observacoes_internas TEXT,
ADD COLUMN IF NOT EXISTS configuracao_notificacoes JSONB DEFAULT '{"email": true, "push": true, "sms": false}'::jsonb;

-- Melhorar tabela de agendamentos
ALTER TABLE agendamentos 
ADD COLUMN IF NOT EXISTS tipo_agendamento VARCHAR(20) DEFAULT 'servico' CHECK (tipo_agendamento IN ('servico', 'combo', 'assinatura')),
ADD COLUMN IF NOT EXISTS combo_id INTEGER REFERENCES combos_servicos(combo_id),
ADD COLUMN IF NOT EXISTS assinatura_cliente_id INTEGER, -- Será referência para nova tabela
ADD COLUMN IF NOT EXISTS observacoes_empresa TEXT,
ADD COLUMN IF NOT EXISTS avaliacao_cliente INTEGER CHECK (avaliacao_cliente >= 1 AND avaliacao_cliente <= 5),
ADD COLUMN IF NOT EXISTS comentario_avaliacao TEXT,
ADD COLUMN IF NOT EXISTS data_avaliacao TIMESTAMPTZ;

-- Melhorar tabela de serviços
ALTER TABLE servicos 
ADD COLUMN IF NOT EXISTS imagem_url TEXT,
ADD COLUMN IF NOT EXISTS tags TEXT[],
ADD COLUMN IF NOT EXISTS ordem_exibicao INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS disponivel_online BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS preparacao_minutos INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS limpeza_minutos INTEGER DEFAULT 0;

-- Melhorar tabela de notificações
ALTER TABLE notificacoes 
ADD COLUMN IF NOT EXISTS template_id VARCHAR(100),
ADD COLUMN IF NOT EXISTS parametros_template JSONB,
ADD COLUMN IF NOT EXISTS prioridade VARCHAR(20) DEFAULT 'normal' CHECK (prioridade IN ('baixa', 'normal', 'alta', 'urgente')),
ADD COLUMN IF NOT EXISTS agrupamento_id VARCHAR(100), -- Para agrupar notificações relacionadas
ADD COLUMN IF NOT EXISTS acao_url TEXT, -- URL para ação da notificação
ADD COLUMN IF NOT EXISTS expira_em TIMESTAMPTZ;

-- =====================================================
-- 3. ÍNDICES PARA PERFORMANCE
-- =====================================================

-- Índices para user_roles
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_empresa_id ON user_roles(empresa_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_ativo ON user_roles(ativo) WHERE ativo = true;

-- Índices para convites
CREATE INDEX IF NOT EXISTS idx_convites_token ON convites_colaborador(token_convite);
CREATE INDEX IF NOT EXISTS idx_convites_email ON convites_colaborador(email_convidado);
CREATE INDEX IF NOT EXISTS idx_convites_status ON convites_colaborador(status_convite);
CREATE INDEX IF NOT EXISTS idx_convites_expiracao ON convites_colaborador(data_expiracao);

-- Índices para empresas
CREATE INDEX IF NOT EXISTS idx_empresas_slug ON empresas(slug);
CREATE INDEX IF NOT EXISTS idx_empresas_cidade ON empresas(cidade);
CREATE INDEX IF NOT EXISTS idx_empresas_status ON empresas(status);
CREATE INDEX IF NOT EXISTS idx_empresas_segmento ON empresas(segmento);

-- Índices para agendamentos
CREATE INDEX IF NOT EXISTS idx_agendamentos_data_inicio ON agendamentos(data_hora_inicio);
CREATE INDEX IF NOT EXISTS idx_agendamentos_status ON agendamentos(status_agendamento);
CREATE INDEX IF NOT EXISTS idx_agendamentos_empresa_data ON agendamentos(empresa_id, data_hora_inicio);
CREATE INDEX IF NOT EXISTS idx_agendamentos_colaborador_data ON agendamentos(colaborador_user_id, data_hora_inicio);
CREATE INDEX IF NOT EXISTS idx_agendamentos_cliente_data ON agendamentos(cliente_user_id, data_hora_inicio);

-- Índices para notificações
CREATE INDEX IF NOT EXISTS idx_notificacoes_user_lida ON notificacoes(user_id, lida);
CREATE INDEX IF NOT EXISTS idx_notificacoes_tipo ON notificacoes(tipo_notificacao);
CREATE INDEX IF NOT EXISTS idx_notificacoes_agrupamento ON notificacoes(agrupamento_id);

-- =====================================================
-- 4. FUNÇÕES E TRIGGERS
-- =====================================================

-- Função para verificar limite de colaboradores por plano
CREATE OR REPLACE FUNCTION verificar_limite_colaboradores()
RETURNS TRIGGER AS $$
DECLARE
    limite_plano INTEGER;
    colaboradores_atuais INTEGER;
    plano_empresa INTEGER;
BEGIN
    -- Buscar o plano da empresa
    SELECT plano_saas_id INTO plano_empresa 
    FROM empresas 
    WHERE empresa_id = NEW.empresa_id;
    
    -- Buscar limite do plano
    SELECT limite_colaboradores_extras INTO limite_plano
    FROM planos_saas 
    WHERE plano_saas_id = plano_empresa;
    
    -- Contar colaboradores ativos (excluindo proprietário)
    SELECT COUNT(*) INTO colaboradores_atuais
    FROM colaboradores_empresa ce
    JOIN user_roles ur ON ce.colaborador_user_id = ur.user_id
    JOIN roles r ON ur.role_id = r.role_id
    WHERE ce.empresa_id = NEW.empresa_id 
    AND ce.ativo = true
    AND r.nome_papel = 'Colaborador';
    
    -- Verificar se excede o limite
    IF colaboradores_atuais >= limite_plano THEN
        RAISE EXCEPTION 'Limite de colaboradores do plano excedido. Limite: %, Atual: %', limite_plano, colaboradores_atuais;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para verificar limite de colaboradores
DROP TRIGGER IF EXISTS trigger_limite_colaboradores ON colaboradores_empresa;
CREATE TRIGGER trigger_limite_colaboradores
    BEFORE INSERT ON colaboradores_empresa
    FOR EACH ROW
    EXECUTE FUNCTION verificar_limite_colaboradores();

-- Função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar trigger de updated_at em todas as tabelas relevantes
CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_roles_updated_at BEFORE UPDATE ON user_roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_convites_updated_at BEFORE UPDATE ON convites_colaborador FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 5. POLÍTICAS RLS (ROW LEVEL SECURITY)
-- =====================================================

-- Habilitar RLS nas novas tabelas
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE convites_colaborador ENABLE ROW LEVEL SECURITY;

-- Políticas para tabela roles
CREATE POLICY "Roles são visíveis para todos" ON roles FOR SELECT TO public USING (true);
CREATE POLICY "Apenas administradores podem modificar roles" ON roles FOR ALL TO public
USING (EXISTS (
    SELECT 1 FROM user_roles ur
    JOIN roles r ON ur.role_id = r.role_id
    WHERE ur.user_id = auth.uid()
    AND r.nome_papel = 'Administrador'
    AND ur.ativo = true
));

-- Políticas para tabela user_roles
CREATE POLICY "Usuários podem ver seus próprios papéis" ON user_roles FOR SELECT TO public
USING (user_id = auth.uid());

CREATE POLICY "Proprietários podem gerenciar papéis de sua empresa" ON user_roles FOR ALL TO public
USING (EXISTS (
    SELECT 1 FROM user_roles ur
    JOIN roles r ON ur.role_id = r.role_id
    WHERE ur.user_id = auth.uid()
    AND ur.empresa_id = user_roles.empresa_id
    AND r.nome_papel = 'Proprietário'
    AND ur.ativo = true
));

CREATE POLICY "Administradores podem gerenciar todos os papéis" ON user_roles FOR ALL TO public
USING (EXISTS (
    SELECT 1 FROM user_roles ur
    JOIN roles r ON ur.role_id = r.role_id
    WHERE ur.user_id = auth.uid()
    AND r.nome_papel = 'Administrador'
    AND ur.ativo = true
));

-- Políticas para tabela convites_colaborador
CREATE POLICY "Proprietários podem gerenciar convites de sua empresa" ON convites_colaborador FOR ALL TO public
USING (EXISTS (
    SELECT 1 FROM user_roles ur
    JOIN roles r ON ur.role_id = r.role_id
    WHERE ur.user_id = auth.uid()
    AND ur.empresa_id = convites_colaborador.empresa_id
    AND r.nome_papel = 'Proprietário'
    AND ur.ativo = true
));

CREATE POLICY "Usuários podem ver convites direcionados a eles" ON convites_colaborador FOR SELECT TO public
USING (EXISTS (
    SELECT 1 FROM auth.users u
    WHERE u.id = auth.uid()
    AND u.email = convites_colaborador.email_convidado
));

CREATE POLICY "Usuários podem aceitar convites direcionados a eles" ON convites_colaborador FOR UPDATE TO public
USING (EXISTS (
    SELECT 1 FROM auth.users u
    WHERE u.id = auth.uid()
    AND u.email = convites_colaborador.email_convidado
    AND status_convite = 'pendente'
    AND data_expiracao > NOW()
));

-- =====================================================
-- 6. DADOS INICIAIS
-- =====================================================

-- Inserir papéis básicos do sistema
INSERT INTO roles (nome_papel, descricao, is_global) VALUES
('Administrador', 'Acesso total ao sistema', true),
('Usuário', 'Usuário padrão da plataforma', true),
('Proprietário', 'Proprietário de empresa', false),
('Colaborador', 'Colaborador de empresa', false)
ON CONFLICT (nome_papel) DO NOTHING;

-- =====================================================
-- 7. FUNÇÕES AUXILIARES
-- =====================================================

-- Função para obter papel do usuário em uma empresa
CREATE OR REPLACE FUNCTION get_user_role_in_company(user_uuid UUID, company_id INTEGER)
RETURNS TEXT AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT r.nome_papel INTO user_role
    FROM user_roles ur
    JOIN roles r ON ur.role_id = r.role_id
    WHERE ur.user_id = user_uuid
    AND (ur.empresa_id = company_id OR ur.empresa_id IS NULL)
    AND ur.ativo = true
    ORDER BY r.is_global ASC -- Prioriza papéis específicos da empresa
    LIMIT 1;

    RETURN COALESCE(user_role, 'Usuário');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para verificar se usuário tem permissão em empresa
CREATE OR REPLACE FUNCTION user_has_permission_in_company(user_uuid UUID, company_id INTEGER, required_role TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    user_role TEXT;
BEGIN
    user_role := get_user_role_in_company(user_uuid, company_id);

    RETURN CASE
        WHEN required_role = 'Administrador' THEN user_role = 'Administrador'
        WHEN required_role = 'Proprietário' THEN user_role IN ('Administrador', 'Proprietário')
        WHEN required_role = 'Colaborador' THEN user_role IN ('Administrador', 'Proprietário', 'Colaborador')
        WHEN required_role = 'Usuário' THEN user_role IN ('Administrador', 'Proprietário', 'Colaborador', 'Usuário')
        ELSE false
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 8. MIGRAÇÃO DE DADOS EXISTENTES
-- =====================================================

-- Migrar dados de papéis existentes para nova estrutura
-- (Baseado no campo raw_user_meta_data dos usuários)
INSERT INTO user_roles (user_id, role_id, empresa_id, ativo)
SELECT
    u.id,
    r.role_id,
    CASE
        WHEN u.raw_user_meta_data->>'role' = 'Proprietário' THEN e.empresa_id
        WHEN u.raw_user_meta_data->>'role' = 'Colaborador' THEN ce.empresa_id
        ELSE NULL
    END,
    true
FROM auth.users u
JOIN roles r ON r.nome_papel = COALESCE(u.raw_user_meta_data->>'role', 'Usuário')
LEFT JOIN empresas e ON e.proprietario_user_id = u.id AND u.raw_user_meta_data->>'role' = 'Proprietário'
LEFT JOIN colaboradores_empresa ce ON ce.colaborador_user_id = u.id AND u.raw_user_meta_data->>'role' = 'Colaborador'
WHERE u.raw_user_meta_data->>'role' IS NOT NULL
ON CONFLICT (user_id, role_id, empresa_id, ativo) DO NOTHING;

-- Atualizar colaboradores_empresa com novos campos
UPDATE colaboradores_empresa SET
    papel_empresa = 'colaborador',
    configuracao_notificacoes = '{"email": true, "push": true, "sms": false}'::jsonb
WHERE papel_empresa IS NULL;

-- Atualizar empresas com configurações padrão
UPDATE empresas SET
    configuracao_notificacoes = '{"email": true, "push": true, "sms": false}'::jsonb,
    configuracao_agendamento = '{"confirmacao_automatica": false, "prazo_confirmacao_horas": 24}'::jsonb,
    dias_funcionamento = '["segunda", "terca", "quarta", "quinta", "sexta", "sabado"]'::jsonb,
    timezone = 'America/Sao_Paulo'
WHERE configuracao_notificacoes IS NULL;

-- =====================================================
-- 9. RENOMEAR TABELA DE ASSINATURAS
-- =====================================================

-- Renomear tabela de planos de serviço para assinaturas
ALTER TABLE planos_servico_cliente RENAME TO assinaturas_servico_cliente;
ALTER TABLE assinaturas_servico_cliente RENAME COLUMN plano_cliente_id TO assinatura_id;

-- Adicionar referência na tabela agendamentos
ALTER TABLE agendamentos
ADD CONSTRAINT fk_agendamentos_assinatura
FOREIGN KEY (assinatura_cliente_id) REFERENCES assinaturas_servico_cliente(assinatura_id);

-- =====================================================
-- 10. ATUALIZAR POLÍTICAS RLS EXISTENTES
-- =====================================================

-- Remover políticas antigas que usam raw_user_meta_data
DROP POLICY IF EXISTS "Administradores podem ver todos os agendamentos" ON agendamentos;
DROP POLICY IF EXISTS "Administradores podem gerenciar todas as assinaturas SaaS" ON assinaturas_saas_empresas;
DROP POLICY IF EXISTS "Administradores podem ver todos os serviços" ON servicos;

-- Criar novas políticas baseadas na tabela user_roles
CREATE POLICY "Administradores podem ver todos os agendamentos" ON agendamentos FOR ALL TO public
USING (user_has_permission_in_company(auth.uid(), empresa_id, 'Administrador'));

CREATE POLICY "Administradores podem gerenciar todas as assinaturas SaaS" ON assinaturas_saas_empresas FOR ALL TO public
USING (user_has_permission_in_company(auth.uid(), empresa_id, 'Administrador'));

CREATE POLICY "Administradores podem ver todos os serviços" ON servicos FOR ALL TO public
USING (user_has_permission_in_company(auth.uid(), empresa_id, 'Administrador'));

-- =====================================================
-- 11. SCRIPT DE ROLLBACK
-- =====================================================

/*
-- ROLLBACK SCRIPT - Execute apenas se necessário reverter as mudanças

-- 1. Remover novas colunas
ALTER TABLE empresas
DROP COLUMN IF EXISTS website,
DROP COLUMN IF EXISTS instagram,
DROP COLUMN IF EXISTS whatsapp,
DROP COLUMN IF EXISTS email_contato,
DROP COLUMN IF EXISTS configuracao_notificacoes,
DROP COLUMN IF EXISTS configuracao_agendamento,
DROP COLUMN IF EXISTS dias_funcionamento,
DROP COLUMN IF EXISTS timezone;

ALTER TABLE colaboradores_empresa
DROP COLUMN IF EXISTS papel_empresa,
DROP COLUMN IF EXISTS data_inicio_trabalho,
DROP COLUMN IF EXISTS data_fim_trabalho,
DROP COLUMN IF EXISTS observacoes_internas,
DROP COLUMN IF EXISTS configuracao_notificacoes;

ALTER TABLE agendamentos
DROP COLUMN IF EXISTS tipo_agendamento,
DROP COLUMN IF EXISTS combo_id,
DROP COLUMN IF EXISTS assinatura_cliente_id,
DROP COLUMN IF EXISTS observacoes_empresa,
DROP COLUMN IF EXISTS avaliacao_cliente,
DROP COLUMN IF EXISTS comentario_avaliacao,
DROP COLUMN IF EXISTS data_avaliacao;

ALTER TABLE servicos
DROP COLUMN IF EXISTS imagem_url,
DROP COLUMN IF EXISTS tags,
DROP COLUMN IF EXISTS ordem_exibicao,
DROP COLUMN IF EXISTS disponivel_online,
DROP COLUMN IF EXISTS preparacao_minutos,
DROP COLUMN IF EXISTS limpeza_minutos;

ALTER TABLE notificacoes
DROP COLUMN IF EXISTS template_id,
DROP COLUMN IF EXISTS parametros_template,
DROP COLUMN IF EXISTS prioridade,
DROP COLUMN IF EXISTS agrupamento_id,
DROP COLUMN IF EXISTS acao_url,
DROP COLUMN IF EXISTS expira_em;

-- 2. Renomear tabela de volta
ALTER TABLE assinaturas_servico_cliente RENAME TO planos_servico_cliente;
ALTER TABLE planos_servico_cliente RENAME COLUMN assinatura_id TO plano_cliente_id;

-- 3. Remover novas tabelas
DROP TABLE IF EXISTS convites_colaborador CASCADE;
DROP TABLE IF EXISTS user_roles CASCADE;
DROP TABLE IF EXISTS roles CASCADE;

-- 4. Remover funções
DROP FUNCTION IF EXISTS verificar_limite_colaboradores() CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS get_user_role_in_company(UUID, INTEGER) CASCADE;
DROP FUNCTION IF EXISTS user_has_permission_in_company(UUID, INTEGER, TEXT) CASCADE;

-- 5. Recriar políticas antigas (se necessário)
-- [Adicionar aqui as políticas antigas se precisar restaurar]

*/
