'use client';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { createClient } from '@/utils/supabase/client';
import { usePaymentStatusPolling } from '@/hooks/usePaymentStatusPolling';

export default function OnboardingRedirect() {
  const router = useRouter();
  const { user, loading, refreshUser } = useAuth();
  const [checking, setChecking] = useState(true);
  const [attempts, setAttempts] = useState(0);
  const maxAttempts = 5;

  useEffect(() => {
    const checkOnboardingStatus = async () => {
      if (loading) return;

      if (!user) {
        // Usuário não logado, redirecionar para planos
        router.push('/planos');
        return;
      }

      try {
        // Forçar atualização dos dados do usuário
        await refreshUser();

        // Aguardar um pouco para garantir sincronização
        await new Promise(resolve => setTimeout(resolve, 500));

        // Verificar se o usuário tem pagamento confirmado
        const supabase = createClient();
        const { data: { user: authUser } } = await supabase.auth.getUser();

        console.log('🔍 Verificando status do onboarding:', {
          pagamento_confirmado: authUser?.user_metadata?.pagamento_confirmado,
          onboarding_pendente: authUser?.user_metadata?.onboarding_pendente,
          role: authUser?.user_metadata?.role,
          attempts: attempts + 1
        });

        if (authUser?.user_metadata?.pagamento_confirmado) {
          if (authUser?.user_metadata?.onboarding_pendente) {
            // Pagamento confirmado mas onboarding pendente
            console.log('✅ Redirecionando para início do onboarding');
            router.push('/onboarding/inicio');
          } else {
            // Onboarding já concluído
            console.log('✅ Redirecionando para dashboard');
            router.push('/proprietario/dashboard');
          }
          setChecking(false);
        } else {
          // Se ainda não tem pagamento confirmado, tentar algumas vezes
          const newAttempts = attempts + 1;
          setAttempts(newAttempts);

          if (newAttempts < maxAttempts) {
            console.log(`⏳ Tentativa ${newAttempts}/${maxAttempts} - Aguardando confirmação do pagamento...`);
            // Tentar novamente em 2 segundos
            setTimeout(checkOnboardingStatus, 2000);
          } else {
            console.log('⚠️ Máximo de tentativas atingido, redirecionando para seleção de plano');
            // Sem pagamento confirmado após várias tentativas
            router.push('/onboarding/selecao-plano');
            setChecking(false);
          }
        }
      } catch (error) {
        console.error('❌ Erro ao verificar status do onboarding:', error);
        setChecking(false);
        router.push('/onboarding/selecao-plano');
      }
    };

    checkOnboardingStatus();
  }, [router, user, loading, refreshUser, attempts]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-4">
          <svg className="h-8 w-8 text-blue-600 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
        </div>

        <h2 className="text-2xl font-bold text-gray-800 mb-4">
          Verificando Status
        </h2>

        <p className="text-gray-600 mb-4">
          {checking ? 'Verificando status do onboarding...' : 'Redirecionando...'}
        </p>

        {attempts > 0 && (
          <div className="text-sm text-gray-500 mb-4">
            <p>Tentativa {attempts} de {maxAttempts}</p>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(attempts / maxAttempts) * 100}%` }}
              ></div>
            </div>
          </div>
        )}

        <div className="flex items-center justify-center">
          <div className="inline-block animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Aguarde...</span>
        </div>
      </div>
    </div>
  );
}