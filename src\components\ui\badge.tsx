import React from 'react';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  className?: string;
}

export function Badge({ children, variant = 'default', className = '' }: BadgeProps) {
  const baseClasses = 'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium';
  
  const variantClasses = {
    default: 'bg-[var(--primary)] text-white',
    secondary: 'bg-[var(--surface)] text-[var(--text-secondary)]',
    destructive: 'bg-[var(--error)] text-white',
    outline: 'border border-[var(--border)] text-[var(--text-primary)]'
  };

  return (
    <span className={`${baseClasses} ${variantClasses[variant]} ${className}`}>
      {children}
    </span>
  );
}
