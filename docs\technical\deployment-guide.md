# 🚀 Guia de Deploy - ServiceTech

Deploy completo da aplicação Next.js 15 com Supabase, Stripe e Vercel.

## 🎯 Stack de Produção

- **Frontend**: Next.js 15 + TypeScript + Tailwind CSS
- **Backend**: Next.js API Routes + Supabase
- **Database**: PostgreSQL (Supabase)
- **Auth**: Supabase Auth
- **Payments**: Stripe + Stripe Connect
- **Deploy**: Vercel
- **Monitoring**: Sentry + Custom

## ✅ Pré-requisitos

### Contas Necessárias
- GitHub, Supabase, Stripe, Vercel
- Resend (email), Twilio (SMS), Firebase (push)

### Ferramentas
```bash
node --version  # 18+
npm --version
git --version
npm i -g vercel  # opcional
```

## 🗄️ Configuração do Supabase

### 1. Criar Projeto
1. Acesse [supabase.com](https://supabase.com)
2. New Project → escolha região e senha

### 2. Configurar Banco
Execute o schema completo em `docs/database_schema.md`:
- C<PERSON>r todas as 24 tabelas
- Aplicar 46 políticas RLS
- Configurar triggers e funções
- Criar índices de performance

### 3. Configurar Autenticação
**Settings > Authentication**:
- Site URL: `https://seu-dominio.com`
- Redirect URLs:
  ```
  https://seu-dominio.com/auth/callback
  https://seu-dominio.com/reset-password
  ```

### 4. Habilitar RLS
```sql
-- Para cada tabela
ALTER TABLE nome_da_tabela ENABLE ROW LEVEL SECURITY;

-- Aplicar políticas específicas (ver database_schema.md)
CREATE POLICY "proprietarios_empresas" ON empresas
  FOR ALL USING (proprietario_user_id = auth.uid());
```

### 5. Obter Chaves
- Project URL: `https://xxx.supabase.co`
- Anon Key: `eyJhbGciOiJIUzI1NiIs...`
- Service Role Key: `eyJhbGciOiJIUzI1NiIs...`

## 💳 Configuração do Stripe

### 1. Criar Conta
1. Acesse [stripe.com](https://stripe.com)
2. Complete verificação KYC

### 2. Configurar Webhooks
**Dashboard > Webhooks**:
- Endpoint: `https://seu-dominio.com/api/webhook/stripe`
- Events:
  ```
  payment_intent.succeeded
  payment_intent.payment_failed
  invoice.payment_succeeded
  customer.subscription.deleted
  account.updated
  ```

### 3. Configurar Stripe Connect
**Settings > Connect**:
- Application name: "ServiceTech"
- Redirect URI: `https://seu-dominio.com/api/stripe/connect/oauth`

### 4. Obter Chaves
- Publishable Key: `pk_live_...`
- Secret Key: `sk_live_...`
- Webhook Secret: `whsec_...`
- Connect Client ID: `ca_...`

## 🚀 Deploy no Vercel

### 1. Conectar Repositório
1. Acesse [vercel.com](https://vercel.com)
2. Import Git Repository
3. Selecione repositório ServiceTech

### 2. Configurar Build
Arquivo `vercel.json` (opcional):
```json
{
  "framework": "nextjs",
  "buildCommand": "cd servicetech && npm run build",
  "outputDirectory": "servicetech/.next",
  "installCommand": "cd servicetech && npm install"
}
```

### 3. Variáveis de Ambiente
```bash
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://xxx.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIs...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIs...

# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_CONNECT_CLIENT_ID=ca_...

# Notificações
RESEND_API_KEY=re_...
TWILIO_ACCOUNT_SID=AC...
TWILIO_AUTH_TOKEN=...
TWILIO_PHONE_NUMBER=+1...

# Firebase (Push)
NEXT_PUBLIC_FIREBASE_VAPID_KEY=...
FIREBASE_ADMIN_PRIVATE_KEY=...
FIREBASE_ADMIN_CLIENT_EMAIL=...
FIREBASE_ADMIN_PROJECT_ID=...

# Monitoramento
SENTRY_DSN=https://...
SENTRY_AUTH_TOKEN=...
```

### 4. Deploy
```bash
# Via CLI
vercel --prod

# Ou via Git
git push origin main
```

## 🌐 Configuração de Domínio

### 1. Domínio Personalizado
**Vercel Dashboard > Domains**:
- Add Domain: `servicetech.com`
- Configure DNS:
  ```
  Type: CNAME
  Name: @
  Value: cname.vercel-dns.com
  ```

### 2. SSL/TLS
- Automático via Let's Encrypt
- Redirect HTTP → HTTPS automático

### 3. Atualizar Configurações
Após configurar domínio:

**Supabase**:
- Site URL: `https://servicetech.com`
- Redirect URLs: `https://servicetech.com/auth/callback`

**Stripe**:
- Webhook URL: `https://servicetech.com/api/webhook/stripe`
- Connect Redirect: `https://servicetech.com/api/stripe/connect/oauth`

## 📊 Monitoramento

### 1. Vercel Analytics
Habilitar no dashboard: Analytics > Enable

### 2. Sentry
```javascript
// sentry.client.config.js
import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
});
```

### 3. Health Check
```bash
curl https://servicetech.com/api/health

# Resposta esperada
{
  "status": "healthy",
  "services": {
    "database": "connected",
    "stripe": "connected",
    "email": "connected"
  }
}
```

## 💾 Backup e Recuperação

### Backup Automático
- **Supabase**: Backup diário automático
- **Código**: GitHub (automático)
- **Assets**: Vercel (automático)

### Plano de Recuperação
- **RTO**: 4 horas
- **RPO**: 1 hora
- **Procedimento**: Restaurar banco → Redeploy → Reconfigurar → Testar

## 🔧 Troubleshooting

### Build Failures
```bash
vercel logs --app=servicetech
cd servicetech && npm run build
npm audit fix
```

### Runtime Errors
```bash
vercel logs --follow
# Verificar Sentry Dashboard
# Verificar Supabase Logs
```

### Performance
```bash
npx lighthouse https://servicetech.com
# Vercel Analytics Dashboard
# Google PageSpeed Insights
```

## 📋 Checklist de Deploy

### Pré-Deploy
- [ ] Código testado localmente
- [ ] Build passa sem erros
- [ ] Variáveis de ambiente configuradas
- [ ] Banco de dados atualizado

### Deploy
- [ ] Deploy realizado com sucesso
- [ ] Domínio acessível
- [ ] SSL funcionando
- [ ] APIs respondendo
- [ ] Webhooks configurados

### Pós-Deploy
- [ ] Funcionalidades críticas testadas
- [ ] Monitoramento ativo
- [ ] Logs sendo coletados
- [ ] Equipe notificada

### Testes de Produção
- [ ] **Autenticação**: Login/logout
- [ ] **Agendamento**: Fluxo completo
- [ ] **Pagamento**: Stripe funcionando
- [ ] **Notificações**: Emails enviados
- [ ] **Performance**: Páginas < 3s
- [ ] **Mobile**: Responsividade OK

## 🚨 Rollback

### Quando Fazer
- Erro crítico em produção
- Performance degradada
- Funcionalidade principal quebrada

### Como Fazer
```bash
# Via Vercel Dashboard
Deployments > Previous > Promote to Production

# Via CLI
vercel rollback [deployment-url]

# Via Git
git revert [commit-hash]
git push origin main
```

## 📈 Otimizações de Produção

### Performance (`next.config.js`)
```javascript
module.exports = {
  reactStrictMode: true,
  compress: true,
  poweredByHeader: false,
  images: {
    domains: ['seu-dominio-assets.com'],
    formats: ['image/avif', 'image/webp'],
  },
};
```

### Segurança (`middleware.ts`)
```typescript
export function middleware(request: NextRequest) {
  const response = NextResponse.next();
  
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  
  if (process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', 
      'max-age=31536000; includeSubDomains; preload');
  }
  
  return response;
}
```

### SEO (Metadados)
```typescript
export const metadata: Metadata = {
  title: 'ServiceTech - Sistema de Agendamento',
  description: 'Plataforma completa para agendamento de serviços',
  keywords: ['agendamento online', 'barbearia', 'salão'],
  openGraph: {
    title: 'ServiceTech',
    description: 'Modernize seu estabelecimento',
    url: 'https://servicetech.com',
    locale: 'pt_BR',
  },
};
```

---

**ServiceTech Deploy** - Produção segura e confiável 🚀
