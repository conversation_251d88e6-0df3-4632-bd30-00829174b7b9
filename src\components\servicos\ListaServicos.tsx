'use client';

import React, { memo, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Servico } from '@/types/servicos';

interface ListaServicosProps {
  servicos: Servico[];
  onEditar?: (servico: Servico) => void;
  onExcluir?: (servicoId: number) => void;
  onAlternarStatus?: (servicoId: number, ativo: boolean) => void;
  loading?: boolean;
  mostrarAcoes?: boolean;
  filtroCategoria?: string;
  filtroAtivo?: boolean;
}

// Componente de card individual memoizado
const CardServico = memo(function CardServico({
  servico,
  onEditar,
  onExcluir,
  onAlternarStatus,
  mostrarAcoes = true
}: {
  servico: Servico;
  onEditar?: (servico: Servico) => void;
  onExcluir?: (servicoId: number) => void;
  onAlternarStatus?: (servicoId: number, ativo: boolean) => void;
  mostrarAcoes?: boolean;
}) {
  const handleEditar = useCallback(() => {
    onEditar?.(servico);
  }, [onEditar, servico]);

  const handleExcluir = useCallback(() => {
    onExcluir?.(servico.servico_id!);
  }, [onExcluir, servico.servico_id]);

  const handleAlternarStatus = useCallback(() => {
    onAlternarStatus?.(servico.servico_id!, !servico.ativo);
  }, [onAlternarStatus, servico.servico_id, servico.ativo]);

  const formatarPreco = useMemo(() => {
    return servico.preco?.toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }) ?? 'R$ 0,00';
  }, [servico.preco]);

  const formatarDuracao = useMemo(() => {
    const horas = Math.floor((servico.duracao_minutos ?? 0) / 60);
    const minutos = (servico.duracao_minutos ?? 0) % 60;
    
    if (horas > 0) {
      return minutos > 0 ? `${horas}h ${minutos}min` : `${horas}h`;
    }
    return `${minutos}min`;
  }, [servico.duracao_minutos]);

  return (
    <Card className={`transition-all hover:shadow-md ${!servico.ativo ? 'opacity-60' : ''}`}>
      <CardContent className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <h3 className="text-lg font-semibold text-[var(--text-primary)]">
                {servico.nome_servico}
              </h3>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                servico.ativo 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {servico.ativo ? 'Ativo' : 'Inativo'}
              </span>
              {servico.categoria && (
                <span className="px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                  {servico.categoria}
                </span>
              )}
            </div>
            
            {servico.descricao && (
              <p className="text-[var(--text-secondary)] mb-3 text-sm">
                {servico.descricao}
              </p>
            )}
            
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1">
                <span className="text-[var(--text-secondary)]">Preço:</span>
                <span className="font-medium text-[var(--text-primary)]">{formatarPreco}</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="text-[var(--text-secondary)]">Duração:</span>
                <span className="font-medium text-[var(--text-primary)]">{formatarDuracao}</span>
              </div>
            </div>
          </div>

          {mostrarAcoes && (
            <div className="flex gap-2 ml-4">
              {onEditar && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleEditar}
                >
                  Editar
                </Button>
              )}
              {onAlternarStatus && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleAlternarStatus}
                  className={servico.ativo ? 'text-orange-600' : 'text-green-600'}
                >
                  {servico.ativo ? 'Desativar' : 'Ativar'}
                </Button>
              )}
              {onExcluir && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExcluir}
                  className="text-red-600 hover:text-red-700"
                >
                  Excluir
                </Button>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
});

// Componente principal da lista
const ListaServicos = memo(function ListaServicos({
  servicos,
  onEditar,
  onExcluir,
  onAlternarStatus,
  loading = false,
  mostrarAcoes = true,
  filtroCategoria,
  filtroAtivo
}: ListaServicosProps) {
  
  // Memoizar serviços filtrados
  const servicosFiltrados = useMemo(() => {
    return servicos.filter(servico => {
      // Filtro por categoria
      if (filtroCategoria && servico.categoria !== filtroCategoria) {
        return false;
      }
      
      // Filtro por status ativo
      if (filtroAtivo !== undefined && servico.ativo !== filtroAtivo) {
        return false;
      }
      
      return true;
    });
  }, [servicos, filtroCategoria, filtroAtivo]);

  // Memoizar estatísticas
  const estatisticas = useMemo(() => {
    const total = servicosFiltrados.length;
    const ativos = servicosFiltrados.filter(s => s.ativo).length;
    const inativos = total - ativos;
    const categorias = [...new Set(servicosFiltrados.map(s => s.categoria).filter(Boolean))];
    
    return { total, ativos, inativos, categorias: categorias.length };
  }, [servicosFiltrados]);

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map(i => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="space-y-3">
                <div className="h-6 bg-gray-200 rounded w-1/3"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                <div className="flex gap-4">
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (servicosFiltrados.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <div className="text-6xl mb-4">🛠️</div>
          <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-2">
            Nenhum serviço encontrado
          </h3>
          <p className="text-[var(--text-secondary)]">
            {filtroCategoria || filtroAtivo !== undefined
              ? 'Tente alterar os filtros para ver outros serviços.'
              : 'Cadastre seu primeiro serviço para começar.'
            }
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Estatísticas */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Resumo dos Serviços</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-[var(--primary)]">{estatisticas.total}</div>
              <div className="text-sm text-[var(--text-secondary)]">Total</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{estatisticas.ativos}</div>
              <div className="text-sm text-[var(--text-secondary)]">Ativos</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">{estatisticas.inativos}</div>
              <div className="text-sm text-[var(--text-secondary)]">Inativos</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{estatisticas.categorias}</div>
              <div className="text-sm text-[var(--text-secondary)]">Categorias</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lista de serviços */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {servicosFiltrados.map(servico => (
          <CardServico
            key={servico.servico_id}
            servico={servico}
            onEditar={onEditar}
            onExcluir={onExcluir}
            onAlternarStatus={onAlternarStatus}
            mostrarAcoes={mostrarAcoes}
          />
        ))}
      </div>
    </div>
  );
});

export { ListaServicos };
