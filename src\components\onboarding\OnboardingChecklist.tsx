'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { Button } from '@/components/ui/Button';

interface ChecklistItem {
  id: string;
  title: string;
  description: string;
  action?: () => void;
  actionLabel?: string;
  href?: string;
  completed?: boolean;
  required?: boolean;
}

interface ChecklistByRole {
  [role: string]: ChecklistItem[];
}

const CHECKLISTS: ChecklistByRole = {
  'Proprietário': [
    {
      id: 'empresa-info',
      title: 'Complete as informações da empresa',
      description: 'Adicione nome, endereço, telefone e descrição da sua empresa.',
      href: '/proprietario/empresa',
      actionLabel: 'Configurar',
      required: true,
    },
    {
      id: 'servicos',
      title: 'Cadastre seus serviços',
      description: 'Adicione pelo menos um serviço que sua empresa oferece.',
      href: '/proprietario/servicos',
      actionLabel: 'Adicionar <PERSON>',
      required: true,
    },
    {
      id: 'horarios',
      title: 'Configure os horários de funcionamento',
      description: 'Defina quando sua empresa está aberta para agendamentos.',
      href: '/proprietario/horarios',
      actionLabel: 'Configurar Horários',
      required: true,
    },
    {
      id: 'pagamentos',
      title: 'Configure formas de pagamento',
      description: 'Defina como seus clientes podem pagar pelos serviços.',
      href: '/proprietario/pagamentos',
      actionLabel: 'Configurar Pagamentos',
      required: true,
    },
    {
      id: 'colaboradores',
      title: 'Convide colaboradores',
      description: 'Adicione membros da sua equipe ao sistema.',
      href: '/proprietario/colaboradores',
      actionLabel: 'Convidar Equipe',
      required: false,
    },
    {
      id: 'stripe-connect',
      title: 'Configure Stripe Connect',
      description: 'Conecte sua conta Stripe para receber pagamentos diretamente.',
      href: '/proprietario/pagamentos/stripe',
      actionLabel: 'Conectar Stripe',
      required: false,
    },
    {
      id: 'marketing',
      title: 'Configure campanhas de marketing',
      description: 'Crie cupons e campanhas para atrair mais clientes.',
      href: '/proprietario/marketing',
      actionLabel: 'Criar Campanhas',
      required: false,
    },
  ],
  'Colaborador': [
    {
      id: 'perfil',
      title: 'Complete seu perfil',
      description: 'Adicione suas informações pessoais e foto.',
      href: '/colaborador/perfil',
      actionLabel: 'Editar Perfil',
      required: true,
    },
    {
      id: 'horarios-pessoais',
      title: 'Configure seus horários',
      description: 'Defina quando você está disponível para atendimentos.',
      href: '/colaborador/horarios',
      actionLabel: 'Configurar Horários',
      required: true,
    },
    {
      id: 'servicos-pessoais',
      title: 'Escolha seus serviços',
      description: 'Selecione quais serviços você pode realizar.',
      href: '/colaborador/servicos',
      actionLabel: 'Escolher Serviços',
      required: true,
    },
    {
      id: 'primeiro-agendamento',
      title: 'Gerencie seu primeiro agendamento',
      description: 'Aprenda a confirmar e gerenciar agendamentos.',
      href: '/colaborador/agendamentos',
      actionLabel: 'Ver Agendamentos',
      required: false,
    },
  ],
  'Cliente': [
    {
      id: 'perfil-cliente',
      title: 'Complete seu perfil',
      description: 'Adicione suas informações pessoais para facilitar agendamentos.',
      href: '/cliente/perfil',
      actionLabel: 'Editar Perfil',
      required: false,
    },
    {
      id: 'primeiro-agendamento',
      title: 'Faça seu primeiro agendamento',
      description: 'Encontre um estabelecimento e agende um serviço.',
      href: '/buscar',
      actionLabel: 'Buscar Serviços',
      required: false,
    },
    {
      id: 'favoritos',
      title: 'Salve seus estabelecimentos favoritos',
      description: 'Marque estabelecimentos como favoritos para acesso rápido.',
      href: '/cliente/favoritos',
      actionLabel: 'Ver Favoritos',
      required: false,
    },
    {
      id: 'notificacoes',
      title: 'Configure notificações',
      description: 'Receba lembretes sobre seus agendamentos.',
      href: '/cliente/configuracoes',
      actionLabel: 'Configurar',
      required: false,
    },
  ],
};

interface OnboardingChecklistProps {
  className?: string;
  compact?: boolean;
}

export function OnboardingChecklist({ className = '', compact = false }: OnboardingChecklistProps) {
  const { user } = useAuth();
  const { restartOnboarding, isStepCompleted } = useOnboarding();
  const [isExpanded, setIsExpanded] = useState(!compact);

  if (!user) {
    return null;
  }

  const checklist = CHECKLISTS[user.role] || [];
  const completedCount = checklist.filter(item => isStepCompleted(item.id)).length;
  const totalCount = checklist.length;
  const requiredItems = checklist.filter(item => item.required);
  const completedRequired = requiredItems.filter(item => isStepCompleted(item.id)).length;
  const progressPercentage = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;

  if (checklist.length === 0) {
    return null;
  }

  return (
    <div className={`bg-[var(--surface)] border border-[var(--border-color)] rounded-lg ${className}`}>
      {/* Header */}
      <div 
        className={`p-4 ${compact ? 'cursor-pointer' : ''}`}
        onClick={compact ? () => setIsExpanded(!isExpanded) : undefined}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-[var(--primary)] bg-opacity-10 rounded-full flex items-center justify-center">
                <svg 
                  className="w-5 h-5 text-[var(--primary)]" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" 
                  />
                </svg>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-[var(--text-primary)]">
                Primeiros Passos
              </h3>
              <p className="text-sm text-[var(--text-secondary)]">
                {completedCount} de {totalCount} concluídos
                {requiredItems.length > 0 && (
                  <span className="ml-2">
                    • {completedRequired}/{requiredItems.length} obrigatórios
                  </span>
                )}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {compact && (
              <svg 
                className={`w-5 h-5 text-[var(--text-secondary)] transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            )}
          </div>
        </div>

        {/* Barra de progresso */}
        <div className="mt-3">
          <div className="flex justify-between items-center mb-1">
            <span className="text-xs text-[var(--text-secondary)]">Progresso geral</span>
            <span className="text-xs text-[var(--text-secondary)]">
              {Math.round(progressPercentage)}%
            </span>
          </div>
          <div className="w-full bg-[var(--border-color)] rounded-full h-2">
            <div
              className="bg-[var(--primary)] h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>
      </div>

      {/* Lista de itens */}
      {isExpanded && (
        <div className="border-t border-[var(--border-color)]">
          <div className="p-4 space-y-3">
            {checklist.map((item) => {
              const completed = isStepCompleted(item.id);
              
              return (
                <div
                  key={item.id}
                  className={`flex items-start space-x-3 p-3 rounded-lg transition-colors ${
                    completed 
                      ? 'bg-[var(--success)] bg-opacity-5 border border-[var(--success)] border-opacity-20' 
                      : 'bg-[var(--surface-hover)] border border-transparent'
                  }`}
                >
                  {/* Checkbox */}
                  <div className="flex-shrink-0 mt-0.5">
                    <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                      completed 
                        ? 'bg-[var(--success)] border-[var(--success)]' 
                        : 'border-[var(--border-color)]'
                    }`}>
                      {completed && (
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path 
                            fillRule="evenodd" 
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      )}
                    </div>
                  </div>

                  {/* Conteúdo */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className={`text-sm font-medium ${
                          completed ? 'text-[var(--success)]' : 'text-[var(--text-primary)]'
                        }`}>
                          {item.title}
                          {item.required && (
                            <span className="ml-1 text-xs text-[var(--error)]">*</span>
                          )}
                        </h4>
                        <p className="text-xs text-[var(--text-secondary)] mt-1">
                          {item.description}
                        </p>
                      </div>

                      {/* Ação */}
                      {!completed && (item.href || item.action) && (
                        <div className="ml-3">
                          {item.href ? (
                            <a
                              href={item.href}
                              className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-[var(--text-secondary)] hover:text-[var(--primary)] transition-colors duration-200 rounded-md hover:bg-[var(--surface-hover)]"
                            >
                              {item.actionLabel || 'Ir'}
                            </a>
                          ) : (
                            <Button
                              onClick={item.action}
                              variant="ghost"
                              size="sm"
                              className="text-xs"
                            >
                              {item.actionLabel || 'Fazer'}
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Footer */}
          <div className="border-t border-[var(--border-color)] p-4">
            <div className="flex items-center justify-between">
              <p className="text-xs text-[var(--text-secondary)]">
                Precisa de ajuda? 
                <button 
                  onClick={restartOnboarding}
                  className="ml-1 text-[var(--primary)] hover:underline"
                >
                  Refazer tour guiado
                </button>
              </p>
              
              {completedCount === totalCount && (
                <div className="flex items-center space-x-1 text-[var(--success)]">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path 
                      fillRule="evenodd" 
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" 
                      clipRule="evenodd" 
                    />
                  </svg>
                  <span className="text-xs font-medium">Tudo pronto!</span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
