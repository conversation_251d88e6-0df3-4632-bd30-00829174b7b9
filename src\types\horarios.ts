// Tipos para o módulo de gerenciamento de horários

// Dias da semana
export type DiaSemana = 'segunda' | 'terca' | 'quarta' | 'quinta' | 'sexta' | 'sabado' | 'domingo';

// Horário de funcionamento de um dia específico
export interface HorarioDia {
  ativo: boolean;
  abertura: string; // Formato HH:mm (ex: "08:00")
  fechamento: string; // Formato HH:mm (ex: "18:00")
  pausas?: HorarioPausa[]; // Pausas durante o dia (ex: almoço)
}

// Pausa durante o dia
export interface HorarioPausa {
  inicio: string; // Formato HH:mm
  fim: string; // Formato HH:mm
  descricao?: string; // Ex: "Almoço", "Pausa"
}

// Horários de funcionamento da empresa
export interface HorarioEmpresa {
  segunda: HorarioDia;
  terca: HorarioDia;
  quarta: HorarioDia;
  quinta: HorarioDia;
  sexta: HorarioDia;
  sabado: HorarioDia;
  domingo: HorarioDia;
  fuso_horario?: string; // Ex: "America/Sao_Paulo"
  observacoes?: string;
}

// Horários individuais de colaborador
export interface HorarioColaborador {
  colaborador_user_id: string;
  empresa_id: number;
  horarios: HorarioEmpresa; // Herda estrutura da empresa
  bloqueios_recorrentes?: BloqueioRecorrente[];
  bloqueios_especificos?: BloqueioEspecifico[];
  configuracoes?: {
    herdar_empresa: boolean; // Se deve herdar horários da empresa
    notificar_mudancas: boolean; // Se deve notificar sobre mudanças
  };
}

// Bloqueio recorrente (ex: toda segunda-feira)
export interface BloqueioRecorrente {
  id?: string;
  dia_semana: DiaSemana;
  horario_inicio?: string; // Se não especificado, bloqueia o dia todo
  horario_fim?: string;
  descricao: string;
  ativo: boolean;
  data_inicio?: string; // Data de início do bloqueio (YYYY-MM-DD)
  data_fim?: string; // Data de fim do bloqueio (YYYY-MM-DD)
}

// Bloqueio específico (data específica)
export interface BloqueioEspecifico {
  id?: string;
  data: string; // Formato YYYY-MM-DD
  horario_inicio?: string; // Se não especificado, bloqueia o dia todo
  horario_fim?: string;
  descricao: string;
  tipo: 'folga' | 'compromisso' | 'manutencao' | 'outro';
  criado_por?: string; // ID do usuário que criou
  criado_em?: string; // Timestamp
}

// Dados para criação/atualização de horários da empresa
export interface AtualizarHorarioEmpresaData {
  horario_funcionamento: HorarioEmpresa;
}

// Dados para criação/atualização de horários de colaborador
export interface AtualizarHorarioColaboradorData {
  colaborador_user_id: string;
  horarios_trabalho_individual: HorarioColaborador;
}

// Resposta da API de horários
export interface HorarioApiResponse {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

// Conflito de agendamento
export interface ConflitoBloqueio {
  agendamento_id: number;
  cliente_nome: string;
  servico_nome: string;
  data_hora_inicio: string;
  data_hora_fim: string;
  status: string;
}

// Verificação de conflitos
export interface VerificacaoConflito {
  tem_conflitos: boolean;
  conflitos: ConflitoBloqueio[];
  pode_prosseguir: boolean;
  mensagem?: string;
}

// Disponibilidade de horário
export interface DisponibilidadeHorario {
  data: string; // YYYY-MM-DD
  horarios_disponiveis: string[]; // Array de horários no formato HH:mm
  bloqueios: {
    horario: string;
    motivo: string;
    tipo: 'empresa' | 'colaborador' | 'agendamento';
  }[];
}

// Configurações de horário
export interface ConfiguracaoHorario {
  intervalo_agendamento: number; // Intervalo em minutos (ex: 30)
  antecedencia_minima: number; // Antecedência mínima em horas
  antecedencia_maxima: number; // Antecedência máxima em dias
  permitir_agendamento_feriados: boolean;
  permitir_agendamento_finais_semana: boolean;
}

// Validação de horário
export interface ValidacaoHorario {
  valido: boolean;
  erros: string[];
  avisos?: string[];
}

// Estatísticas de horários
export interface EstatisticasHorario {
  total_horas_semana: number;
  dias_ativos: number;
  pausas_configuradas: number;
  bloqueios_ativos: number;
  disponibilidade_media: number; // Percentual
}

// Formulário de horário
export interface FormularioHorario {
  dia: DiaSemana;
  ativo: boolean;
  abertura: string;
  fechamento: string;
  pausas: HorarioPausa[];
}

// Formulário de bloqueio
export interface FormularioBloqueio {
  tipo: 'recorrente' | 'especifico';
  data?: string; // Para bloqueios específicos
  dia_semana?: DiaSemana; // Para bloqueios recorrentes
  horario_inicio?: string;
  horario_fim?: string;
  descricao: string;
  bloquear_dia_todo: boolean;
}

// Estado do hook de horários
export interface EstadoHorarios {
  horarios_empresa: HorarioEmpresa | null;
  horarios_colaboradores: HorarioColaborador[];
  loading: boolean;
  error: string | null;
  salvando: boolean;
  configuracoes: ConfiguracaoHorario | null;
}

// Ações do hook de horários
export interface AcoesHorarios {
  buscarHorariosEmpresa: () => Promise<void>;
  buscarHorariosColaborador: (colaboradorId: string) => Promise<void>;
  atualizarHorariosEmpresa: (horarios: HorarioEmpresa) => Promise<boolean>;
  atualizarHorariosColaborador: (colaboradorId: string, horarios: HorarioColaborador) => Promise<boolean>;
  adicionarBloqueio: (colaboradorId: string, bloqueio: BloqueioRecorrente | BloqueioEspecifico) => Promise<boolean>;
  removerBloqueio: (colaboradorId: string, bloqueioId: string, tipo: 'recorrente' | 'especifico') => Promise<boolean>;
  verificarConflitos: (colaboradorId: string, data: string, horarioInicio?: string, horarioFim?: string) => Promise<VerificacaoConflito>;
  verificarDisponibilidade: (colaboradorId: string, data: string) => Promise<DisponibilidadeHorario>;
  validarHorario: (horario: HorarioDia) => ValidacaoHorario;
  limparError: () => void;
}
