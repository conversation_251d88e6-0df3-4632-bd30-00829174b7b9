'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { FiltrosAgendamentos as FiltrosType } from '@/types/agendamentos';
import { format } from 'date-fns';

interface FiltrosAgendamentosProps {
  filtros: FiltrosType;
  onAplicarFiltros: (filtros: FiltrosType) => void;
  onLimparFiltros: () => void;
  loading?: boolean;
  mostrarFiltroEmpresa?: boolean;
  mostrarFiltroColaborador?: boolean;
}

export function FiltrosAgendamentos({
  filtros,
  onAplicarFiltros,
  onLimparFiltros,
  loading = false,
  mostrarFiltroEmpresa = false,
  mostrarFiltroColaborador = false
}: FiltrosAgendamentosProps) {
  
  const [filtrosLocais, setFiltrosLocais] = useState<FiltrosType>(filtros);
  const [mostrarFiltrosAvancados, setMostrarFiltrosAvancados] = useState(false);

  // Opções de período rápido
  const periodosRapidos = [
    { 
      label: 'Hoje', 
      value: () => {
        const hoje = new Date();
        const inicio = new Date(hoje.getFullYear(), hoje.getMonth(), hoje.getDate());
        const fim = new Date(hoje.getFullYear(), hoje.getMonth(), hoje.getDate(), 23, 59, 59);
        return { data_inicio: format(inicio, 'yyyy-MM-dd'), data_fim: format(fim, 'yyyy-MM-dd') };
      }
    },
    { 
      label: 'Esta Semana', 
      value: () => {
        const hoje = new Date();
        const inicioSemana = new Date(hoje);
        inicioSemana.setDate(hoje.getDate() - hoje.getDay());
        const fimSemana = new Date(inicioSemana);
        fimSemana.setDate(inicioSemana.getDate() + 6);
        return { 
          data_inicio: format(inicioSemana, 'yyyy-MM-dd'), 
          data_fim: format(fimSemana, 'yyyy-MM-dd') 
        };
      }
    },
    { 
      label: 'Este Mês', 
      value: () => {
        const hoje = new Date();
        const inicioMes = new Date(hoje.getFullYear(), hoje.getMonth(), 1);
        const fimMes = new Date(hoje.getFullYear(), hoje.getMonth() + 1, 0);
        return { 
          data_inicio: format(inicioMes, 'yyyy-MM-dd'), 
          data_fim: format(fimMes, 'yyyy-MM-dd') 
        };
      }
    }
  ];

  const handleAtualizarFiltro = (campo: keyof FiltrosType, valor: any) => {
    setFiltrosLocais(prev => ({
      ...prev,
      [campo]: valor
    }));
  };

  const handleAplicarPeriodoRapido = (periodo: () => { data_inicio: string; data_fim: string }) => {
    const datas = periodo();
    setFiltrosLocais(prev => ({
      ...prev,
      ...datas
    }));
  };

  const handleAplicar = () => {
    onAplicarFiltros(filtrosLocais);
  };

  const handleLimpar = () => {
    setFiltrosLocais({});
    onLimparFiltros();
  };

  const temFiltrosAtivos = Object.keys(filtrosLocais).some(key => 
    filtrosLocais[key as keyof FiltrosType] !== undefined && 
    filtrosLocais[key as keyof FiltrosType] !== ''
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">Filtros</CardTitle>
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setMostrarFiltrosAvancados(!mostrarFiltrosAvancados)}
            >
              {mostrarFiltrosAvancados ? 'Ocultar' : 'Avançados'}
            </Button>
            {temFiltrosAtivos && (
              <Button
                size="sm"
                variant="outline"
                onClick={handleLimpar}
                disabled={loading}
              >
                Limpar
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Períodos Rápidos */}
        <div>
          <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
            Período Rápido
          </label>
          <div className="flex flex-wrap gap-2">
            {periodosRapidos.map(periodo => (
              <Button
                key={periodo.label}
                size="sm"
                variant="outline"
                onClick={() => handleAplicarPeriodoRapido(periodo.value)}
                disabled={loading}
              >
                {periodo.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Status do Agendamento */}
        <div>
          <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
            Status do Agendamento
          </label>
          <select
            value={filtrosLocais.status_agendamento || ''}
            onChange={(e) => handleAtualizarFiltro('status_agendamento', e.target.value || undefined)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
            disabled={loading}
          >
            <option value="">Todos os status</option>
            <option value="Pendente">Pendente</option>
            <option value="Confirmado">Confirmado</option>
            <option value="Recusado">Recusado</option>
            <option value="Cancelado">Cancelado</option>
            <option value="Concluido">Concluído</option>
          </select>
        </div>

        {/* Filtros Avançados */}
        {mostrarFiltrosAvancados && (
          <div className="space-y-4 pt-4 border-t border-gray-200">
            {/* Datas Personalizadas */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                  Data Início
                </label>
                <input
                  type="date"
                  value={filtrosLocais.data_inicio || ''}
                  onChange={(e) => handleAtualizarFiltro('data_inicio', e.target.value || undefined)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
                  disabled={loading}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                  Data Fim
                </label>
                <input
                  type="date"
                  value={filtrosLocais.data_fim || ''}
                  onChange={(e) => handleAtualizarFiltro('data_fim', e.target.value || undefined)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
                  disabled={loading}
                />
              </div>
            </div>

            {/* Forma de Pagamento */}
            <div>
              <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                Forma de Pagamento
              </label>
              <select
                value={filtrosLocais.forma_pagamento || ''}
                onChange={(e) => handleAtualizarFiltro('forma_pagamento', e.target.value || undefined)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
                disabled={loading}
              >
                <option value="">Todas as formas</option>
                <option value="Online">Online</option>
                <option value="Local">Local</option>
              </select>
            </div>

            {/* Status do Pagamento */}
            <div>
              <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                Status do Pagamento
              </label>
              <select
                value={filtrosLocais.status_pagamento || ''}
                onChange={(e) => handleAtualizarFiltro('status_pagamento', e.target.value || undefined)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
                disabled={loading}
              >
                <option value="">Todos os status</option>
                <option value="Pendente">Pendente</option>
                <option value="Pago">Pago</option>
                <option value="Reembolsado">Reembolsado</option>
                <option value="Falhou">Falhou</option>
              </select>
            </div>

            {/* Filtro por Empresa (se aplicável) */}
            {mostrarFiltroEmpresa && (
              <div>
                <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                  ID da Empresa
                </label>
                <input
                  type="number"
                  value={filtrosLocais.empresa_id || ''}
                  onChange={(e) => handleAtualizarFiltro('empresa_id', e.target.value ? parseInt(e.target.value) : undefined)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
                  placeholder="Digite o ID da empresa"
                  disabled={loading}
                />
              </div>
            )}

            {/* Filtro por Colaborador (se aplicável) */}
            {mostrarFiltroColaborador && (
              <div>
                <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                  ID do Colaborador
                </label>
                <input
                  type="text"
                  value={filtrosLocais.colaborador_user_id || ''}
                  onChange={(e) => handleAtualizarFiltro('colaborador_user_id', e.target.value || undefined)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
                  placeholder="Digite o ID do colaborador"
                  disabled={loading}
                />
              </div>
            )}
          </div>
        )}

        {/* Botão Aplicar */}
        <div className="pt-4 border-t border-gray-200">
          <Button
            onClick={handleAplicar}
            disabled={loading}
            className="w-full"
          >
            {loading ? 'Aplicando...' : 'Aplicar Filtros'}
          </Button>
        </div>

        {/* Indicador de filtros ativos */}
        {temFiltrosAtivos && (
          <div className="text-xs text-[var(--text-secondary)]">
            <strong>Filtros ativos:</strong>{' '}
            {Object.entries(filtrosLocais)
              .filter(([_, value]) => value !== undefined && value !== '')
              .map(([key, value]) => {
                const labels: Record<string, string> = {
                  status_agendamento: 'Status',
                  status_pagamento: 'Pagamento',
                  forma_pagamento: 'Forma',
                  data_inicio: 'Início',
                  data_fim: 'Fim',
                  empresa_id: 'Empresa',
                  colaborador_user_id: 'Colaborador'
                };
                return `${labels[key] || key}: ${value}`;
              })
              .join(', ')
            }
          </div>
        )}
      </CardContent>
    </Card>
  );
}
