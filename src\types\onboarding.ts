// Tipos para o fluxo de onboarding

// Tipo para os planos disponíveis
export type PlanoTipo = 'essencial' | 'premium';

// Detalhes do plano
export interface PlanoDetalhes {
  tipo: PlanoTipo;
  nome: string;
  preco: number;
  descricao: string;
  recursos: string[];
  limiteServicos: number;
  limiteColaboradores: number;
}

// Dados do estabelecimento
export interface DadosEstabelecimento {
  nome: string;
  cnpj: string;
  telefone: string;
  email: string;
  cep: string;
  endereco: string;
  numero: string;
  complemento?: string;
  bairro: string;
  cidade: string;
  estado: string;
  responsavel: string;
  responsavelTelefone: string;
  responsavelEmail: string;
}

// Dados do serviço
export interface DadosServico {
  id?: string;
  nome: string;
  descricao: string;
  preco: number;
  duracao: number; // em minutos
  categoria: string;
}

// Dados do pagamento
export interface DadosPagamento {
  metodoPagamento: string;
  numeroCartao?: string;
  nomeCartao?: string;
  validadeCartao?: string;
  cvvCartao?: string;
}

export interface HorarioComercial {
  diaSemana: string;
  aberto: boolean;
  horarioAbertura: string;
  horarioFechamento: string;
  intervaloInicio?: string;
  intervaloFim?: string;
}

// Dados completos do onboarding
export interface DadosOnboarding {
  plano: PlanoTipo;
  estabelecimento: DadosEstabelecimento;
  servicos: DadosServico[];
  pagamento?: DadosPagamento;
  horariosComerciais: HorarioComercial[];
}