'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';

// Tipos base para formulários
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select';
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
  validation?: (value: any) => string | undefined;
  disabled?: boolean;
  helperText?: string;
}

export interface FormConfig {
  title: string;
  description?: string;
  fields: FormField[];
  submitText?: string;
  cancelText?: string;
  onSubmit: (data: Record<string, any>) => Promise<void> | void;
  onCancel?: () => void;
  initialData?: Record<string, any>;
  loading?: boolean;
}

interface FormErrors {
  [key: string]: string;
}

interface BaseFormProps extends FormConfig {
  className?: string;
}

export function BaseForm({
  title,
  description,
  fields,
  submitText = 'Salvar',
  cancelText = 'Cancelar',
  onSubmit,
  onCancel,
  initialData = {},
  loading = false,
  className = ''
}: BaseFormProps) {
  const [formData, setFormData] = useState<Record<string, any>>(initialData);
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Memoizar campos para evitar re-renderizações desnecessárias
  const memoizedFields = useMemo(() => fields, [fields]);

  // Atualizar campo do formulário
  const updateField = useCallback((name: string, value: any) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Limpar erro do campo quando usuário começar a digitar
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  }, [errors]);

  // Validar formulário
  const validateForm = useCallback(() => {
    const newErrors: FormErrors = {};

    memoizedFields.forEach(field => {
      const value = formData[field.name];

      // Validação de campo obrigatório
      if (field.required && (!value || value.toString().trim() === '')) {
        newErrors[field.name] = `${field.label} é obrigatório`;
        return;
      }

      // Validação customizada
      if (field.validation && value) {
        const validationError = field.validation(value);
        if (validationError) {
          newErrors[field.name] = validationError;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [memoizedFields, formData]);

  // Submeter formulário
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Erro ao submeter formulário:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, validateForm, onSubmit]);

  // Renderizar campo do formulário
  const renderField = useCallback((field: FormField) => {
    const value = formData[field.name] ?? '';
    const error = errors[field.name];

    const commonProps = {
      id: field.name,
      label: field.label,
      value,
      error,
      helperText: field.helperText,
      required: field.required,
      placeholder: field.placeholder,
      disabled: field.disabled || loading || isSubmitting,
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        updateField(field.name, e.target.value);
      }
    };

    switch (field.type) {
      case 'textarea':
        return (
          <div key={field.name} className="space-y-2">
            <label className="block text-sm font-medium text-[var(--text-primary)]">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <textarea
              {...commonProps}
              rows={4}
              className="flex w-full rounded-md border bg-[var(--background)] px-3 py-2 text-sm text-[var(--text-primary)] border-[var(--border-color)] focus:outline-none focus:ring-2 focus:ring-[var(--primary)] disabled:cursor-not-allowed disabled:opacity-50"
            />
            {error && <p className="text-red-500 text-sm">{error}</p>}
            {field.helperText && !error && (
              <p className="text-[var(--text-secondary)] text-sm">{field.helperText}</p>
            )}
          </div>
        );

      case 'select':
        return (
          <div key={field.name} className="space-y-2">
            <label className="block text-sm font-medium text-[var(--text-primary)]">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <select
              {...commonProps}
              className="flex w-full rounded-md border bg-[var(--background)] px-3 py-2 text-sm text-[var(--text-primary)] border-[var(--border-color)] focus:outline-none focus:ring-2 focus:ring-[var(--primary)] disabled:cursor-not-allowed disabled:opacity-50"
            >
              <option value="">Selecione...</option>
              {field.options?.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {error && <p className="text-red-500 text-sm">{error}</p>}
            {field.helperText && !error && (
              <p className="text-[var(--text-secondary)] text-sm">{field.helperText}</p>
            )}
          </div>
        );

      default:
        return (
          <div key={field.name}>
            <Input
              {...commonProps}
              type={field.type}
            />
          </div>
        );
    }
  }, [formData, errors, loading, isSubmitting, updateField]);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && (
          <p className="text-[var(--text-secondary)]">{description}</p>
        )}
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 gap-4">
            {memoizedFields.map(renderField)}
          </div>

          <div className="flex gap-4 pt-4">
            <Button
              type="submit"
              loading={isSubmitting || loading}
              disabled={isSubmitting || loading}
              className="flex-1"
            >
              {submitText}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting || loading}
              >
                {cancelText}
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}

// Hook para usar com BaseForm
export function useBaseForm(initialData: Record<string, any> = {}) {
  const [data, setData] = useState(initialData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateData = useCallback((newData: Record<string, any>) => {
    setData(prev => ({ ...prev, ...newData }));
  }, []);

  const resetForm = useCallback(() => {
    setData(initialData);
    setError(null);
  }, [initialData]);

  const submitForm = useCallback(async (submitFn: (data: Record<string, any>) => Promise<void>) => {
    setLoading(true);
    setError(null);
    
    try {
      await submitFn(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [data]);

  return {
    data,
    loading,
    error,
    updateData,
    resetForm,
    submitForm,
    setLoading,
    setError
  };
}
