'use client';

import React, { useMemo } from 'react';
import { BaseForm, FormField } from '@/components/forms/BaseForm';

interface DadosCadastro {
  name: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
}

interface FormularioCadastroProps {
  onSubmit: (dados: DadosCadastro) => Promise<void>;
  loading?: boolean;
}

export function FormularioCadastro({ onSubmit, loading = false }: FormularioCadastroProps) {
  // Função para formatar telefone
  const formatPhone = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    if (numbers.length <= 11) {
      return numbers
        .replace(/(\d{2})(\d)/, '($1) $2')
        .replace(/(\d{4,5})(\d{4})$/, '$1-$2');
    }
    return value;
  };

  // Validação de e-mail
  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Validação de telefone (formato brasileiro)
  const isValidPhone = (phone: string) => {
    const phoneRegex = /^\(\d{2}\)\s\d{4,5}-\d{4}$/;
    return phoneRegex.test(phone);
  };

  // Validação de força da senha
  const isStrongPassword = (password: string) => {
    return password.length >= 8 &&
           /[A-Z]/.test(password) &&
           /[a-z]/.test(password) &&
           /\d/.test(password);
  };

  // Configuração dos campos do formulário
  const formFields: FormField[] = useMemo(() => [
    {
      name: 'name',
      label: 'Nome Completo',
      type: 'text',
      required: true,
      placeholder: 'Digite seu nome completo',
      validation: (value: string) => {
        if (!value?.trim()) return 'Nome é obrigatório';
        if (value.trim().length < 2) return 'Nome deve ter pelo menos 2 caracteres';
        if (value.trim().length > 100) return 'Nome deve ter no máximo 100 caracteres';
      }
    },
    {
      name: 'email',
      label: 'E-mail',
      type: 'email',
      required: true,
      placeholder: 'Digite seu e-mail',
      validation: (value: string) => {
        if (!value?.trim()) return 'E-mail é obrigatório';
        if (!isValidEmail(value)) return 'E-mail inválido';
        if (value.length > 255) return 'E-mail muito longo';
      }
    },
    {
      name: 'phone',
      label: 'Telefone',
      type: 'text',
      required: true,
      placeholder: '(11) 99999-9999',
      helperText: 'Formato: (11) 99999-9999',
      validation: (value: string) => {
        if (!value?.trim()) return 'Telefone é obrigatório';
        if (!isValidPhone(value)) return 'Telefone inválido. Use o formato (11) 99999-9999';
      }
    },
    {
      name: 'password',
      label: 'Senha',
      type: 'password',
      required: true,
      placeholder: 'Mínimo 8 caracteres',
      helperText: 'Deve conter pelo menos 8 caracteres, incluindo maiúscula, minúscula e número',
      validation: (value: string) => {
        if (!value) return 'Senha é obrigatória';
        if (!isStrongPassword(value)) {
          return 'Senha deve ter pelo menos 8 caracteres, incluindo maiúscula, minúscula e número';
        }
      }
    },
    {
      name: 'confirmPassword',
      label: 'Confirmar Senha',
      type: 'password',
      required: true,
      placeholder: 'Digite a senha novamente',
      validation: (value: string, formData?: Record<string, any>) => {
        if (!value) return 'Confirmação de senha é obrigatória';
        if (formData && value !== formData.password) return 'Senhas não coincidem';
      }
    }
  ], []);

  // Função para processar dados antes do envio
  const handleSubmit = async (formData: Record<string, any>) => {
    // Validação adicional para confirmação de senha
    if (formData.password !== formData.confirmPassword) {
      throw new Error('Senhas não coincidem');
    }

    // Formatar telefone antes de enviar
    const dadosProcessados: DadosCadastro = {
      name: formData.name.trim(),
      email: formData.email.trim().toLowerCase(),
      phone: formatPhone(formData.phone),
      password: formData.password,
      confirmPassword: formData.confirmPassword
    };

    await onSubmit(dadosProcessados);
  };

  return (
    <BaseForm
      title="Criar Conta"
      description="Preencha os dados abaixo para criar sua conta"
      fields={formFields}
      onSubmit={handleSubmit}
      submitText="Criar Conta"
      loading={loading}
      initialData={{
        name: '',
        email: '',
        phone: '',
        password: '',
        confirmPassword: ''
      }}
      className="max-w-md w-full"
    />
  );
}

// Versão simplificada para convites de colaboradores
interface FormularioCadastroColaboradorProps {
  onSubmit: (dados: { name: string; password: string; confirmPassword: string }) => Promise<void>;
  loading?: boolean;
  email: string; // Email já definido pelo convite
}

export function FormularioCadastroColaborador({ 
  onSubmit, 
  loading = false, 
  email 
}: FormularioCadastroColaboradorProps) {
  // Validação de força da senha
  const isStrongPassword = (password: string) => {
    return password.length >= 8 &&
           /[A-Z]/.test(password) &&
           /[a-z]/.test(password) &&
           /\d/.test(password);
  };

  // Configuração dos campos do formulário
  const formFields: FormField[] = useMemo(() => [
    {
      name: 'name',
      label: 'Nome Completo',
      type: 'text',
      required: true,
      placeholder: 'Digite seu nome completo',
      validation: (value: string) => {
        if (!value?.trim()) return 'Nome é obrigatório';
        if (value.trim().length < 2) return 'Nome deve ter pelo menos 2 caracteres';
        if (value.trim().length > 100) return 'Nome deve ter no máximo 100 caracteres';
      }
    },
    {
      name: 'password',
      label: 'Senha',
      type: 'password',
      required: true,
      placeholder: 'Mínimo 8 caracteres',
      helperText: 'Deve conter pelo menos 8 caracteres, incluindo maiúscula, minúscula e número',
      validation: (value: string) => {
        if (!value) return 'Senha é obrigatória';
        if (!isStrongPassword(value)) {
          return 'Senha deve ter pelo menos 8 caracteres, incluindo maiúscula, minúscula e número';
        }
      }
    },
    {
      name: 'confirmPassword',
      label: 'Confirmar Senha',
      type: 'password',
      required: true,
      placeholder: 'Digite a senha novamente',
      validation: (value: string, formData?: Record<string, any>) => {
        if (!value) return 'Confirmação de senha é obrigatória';
        if (formData && value !== formData.password) return 'Senhas não coincidem';
      }
    }
  ], []);

  // Função para processar dados antes do envio
  const handleSubmit = async (formData: Record<string, any>) => {
    // Validação adicional para confirmação de senha
    if (formData.password !== formData.confirmPassword) {
      throw new Error('Senhas não coincidem');
    }

    const dadosProcessados = {
      name: formData.name.trim(),
      password: formData.password,
      confirmPassword: formData.confirmPassword
    };

    await onSubmit(dadosProcessados);
  };

  return (
    <div className="space-y-4">
      {/* Mostrar email do convite */}
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <p className="text-sm text-blue-800">
          <strong>Email:</strong> {email}
        </p>
        <p className="text-xs text-blue-600 mt-1">
          Este email foi definido pelo convite e não pode ser alterado.
        </p>
      </div>

      <BaseForm
        title="Finalizar Cadastro"
        description="Complete seu cadastro para aceitar o convite"
        fields={formFields}
        onSubmit={handleSubmit}
        submitText="Finalizar Cadastro"
        loading={loading}
        initialData={{
          name: '',
          password: '',
          confirmPassword: ''
        }}
      />
    </div>
  );
}
