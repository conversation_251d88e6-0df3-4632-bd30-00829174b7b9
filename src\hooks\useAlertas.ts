'use client';

import { useState, useCallback } from 'react';
import { useEmpresaProprietario } from './useEmpresaProprietario';

interface Alerta {
  id: string;
  tipo: 'info' | 'warning' | 'error' | 'success';
  titulo: string;
  mensagem: string;
  timestamp: Date;
  acao?: {
    texto: string;
    href?: string;
    onClick?: () => void;
  };
}

export function useAlertas() {
  const { statusConfiguracao, planoSaas, loading, error } = useEmpresaProprietario();
  const [alertasDismissed, setAlertasDismissed] = useState<string[]>([]);

  // Gerar alertas baseados no status da empresa
  const gerarAlertas = useCallback((): Alerta[] => {
    const alertas: Alerta[] = [];

    if (statusConfiguracao) {
      // Alerta de configuração incompleta
      if (statusConfiguracao.percentual_conclusao < 100) {
        alertas.push({
          id: 'config-incompleta',
          tipo: 'warning',
          titulo: 'Configuração Incompleta',
          mensagem: `Sua empresa está ${statusConfiguracao.percentual_conclusao}% configurada. Complete a configuração para aproveitar todos os recursos.`,
          timestamp: new Date(),
          acao: {
            texto: 'Completar Configuração',
            href: '/proprietario/empresa'
          }
        });
      }

      // Alertas para próximos passos
      statusConfiguracao.proximos_passos.forEach((passo, index) => {
        alertas.push({
          id: `passo-${index}`,
          tipo: 'info',
          titulo: 'Próximo Passo',
          mensagem: passo,
          timestamp: new Date(),
          acao: {
            texto: 'Ver Detalhes',
            href: '/proprietario/empresa'
          }
        });
      });
    }

    // Alerta de plano
    if (planoSaas) {
      const hoje = new Date();
      const dataFim = planoSaas.data_fim ? new Date(planoSaas.data_fim) : null;
      
      if (dataFim) {
        const diffTime = dataFim.getTime() - hoje.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays <= 0) {
          alertas.push({
            id: 'plano-expirado',
            tipo: 'error',
            titulo: 'Plano Expirado',
            mensagem: 'Seu plano expirou. Renove agora para continuar usando todos os recursos.',
            timestamp: new Date(),
            acao: {
              texto: 'Renovar Plano',
              href: '/proprietario/planos'
            }
          });
        } else if (diffDays <= 7) {
          alertas.push({
            id: 'plano-vencendo',
            tipo: 'warning',
            titulo: 'Plano Vencendo',
            mensagem: `Seu plano vence em ${diffDays} dias. Renove para evitar interrupções.`,
            timestamp: new Date(),
            acao: {
              texto: 'Renovar Plano',
              href: '/proprietario/planos'
            }
          });
        }
      }
    }

    // Alerta de boas-vindas
    if (statusConfiguracao?.percentual_conclusao === 100) {
      alertas.push({
        id: 'bem-vindo',
        tipo: 'success',
        titulo: 'Parabéns!',
        mensagem: 'Sua empresa está totalmente configurada e pronta para receber agendamentos.',
        timestamp: new Date()
      });
    }

    // Filtrar alertas que foram dismissed
    return alertas.filter(alerta => !alertasDismissed.includes(alerta.id));
  }, [statusConfiguracao, planoSaas, alertasDismissed]);

  const alertas = gerarAlertas();
  const alertasNaoLidos = alertas.length;

  const marcarComoLido = useCallback((alertaId: string | 'todos') => {
    if (alertaId === 'todos') {
      setAlertasDismissed(prev => [...prev, ...alertas.map(a => a.id)]);
    } else {
      setAlertasDismissed(prev => [...prev, alertaId]);
    }
  }, [alertas]);

  const dismissAlert = useCallback((alertaId: string) => {
    setAlertasDismissed(prev => [...prev, alertaId]);
  }, []);

  return {
    alertas,
    loading,
    error,
    alertasNaoLidos,
    marcarComoLido,
    dismissAlert
  };
}
