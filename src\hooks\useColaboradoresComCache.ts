'use client';

import { useState, useCallback, useMemo } from 'react';
import { 
  Colaborador, 
  CriarConviteData, 
  AtualizarColaboradorData, 
  FiltrosColaboradores, 
  ColaboradorApiResponse,
  AceitarConviteData,
  DadosConvite
} from '@/types/colaboradores';
import { useApiCache } from './useApiCache';

export function useColaboradoresComCache(filtros?: FiltrosColaboradores) {
  const [error, setError] = useState<string | null>(null);

  // Criar chave de cache baseada nos filtros
  const cacheKey = useMemo(() => {
    const params = new URLSearchParams();
    if (filtros?.ativo !== undefined) params.append('ativo', filtros.ativo.toString());
    if (filtros?.ativo_como_prestador !== undefined) params.append('ativo_como_prestador', filtros.ativo_como_prestador.toString());
    if (filtros?.convite_aceito !== undefined) params.append('convite_aceito', filtros.convite_aceito.toString());
    if (filtros?.busca) params.append('busca', filtros.busca);
    return `colaboradores-${params.toString()}`;
  }, [filtros]);

  // Função para buscar colaboradores
  const fetchColaboradores = useCallback(async () => {
    const params = new URLSearchParams();
    
    if (filtros?.ativo !== undefined) {
      params.append('ativo', filtros.ativo.toString());
    }
    if (filtros?.ativo_como_prestador !== undefined) {
      params.append('ativo_como_prestador', filtros.ativo_como_prestador.toString());
    }
    if (filtros?.convite_aceito !== undefined) {
      params.append('convite_aceito', filtros.convite_aceito.toString());
    }
    if (filtros?.busca) {
      params.append('busca', filtros.busca);
    }

    const queryString = params.toString();
    const url = `/api/colaboradores${queryString ? `?${queryString}` : ''}`;
    const response = await fetch(url);
    const data: ColaboradorApiResponse = await response.json();

    if (!response.ok) {
      throw new Error(data.error ?? 'Erro ao buscar colaboradores');
    }

    if (data.success && Array.isArray(data.data)) {
      return data.data;
    } else {
      throw new Error('Formato de resposta inválido');
    }
  }, [filtros]);

  // Usar cache para os colaboradores
  const {
    data: colaboradores,
    loading,
    error: cacheError,
    refetch,
    invalidate,
    mutate
  } = useApiCache<Colaborador[]>(cacheKey, fetchColaboradores, {
    ttl: 3 * 60 * 1000, // 3 minutos (dados mais dinâmicos)
    staleWhileRevalidate: true
  });

  // Sincronizar erro do cache com estado local
  useMemo(() => {
    if (cacheError) {
      setError(cacheError);
    }
  }, [cacheError]);

  // Criar convite para colaborador
  const criarConvite = useCallback(async (dadosConvite: CriarConviteData): Promise<boolean> => {
    setError(null);

    try {
      const response = await fetch('/api/colaboradores', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dadosConvite),
      });

      const data: ColaboradorApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error ?? 'Erro ao criar convite');
      }

      if (data.success && data.data) {
        // Adicionar o novo convite ao cache
        mutate((colaboradoresAtuais) => {
          if (colaboradoresAtuais) {
            return [data.data as Colaborador, ...colaboradoresAtuais];
          }
          return [data.data as Colaborador];
        });

        return true;
      } else {
        throw new Error('Formato de resposta inválido');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao criar convite:', err);
      return false;
    }
  }, [mutate]);

  // Atualizar colaborador
  const atualizarColaborador = useCallback(async (
    associacaoId: number, 
    dadosAtualizacao: AtualizarColaboradorData
  ): Promise<Colaborador | null> => {
    setError(null);

    try {
      const response = await fetch(`/api/colaboradores/${associacaoId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dadosAtualizacao),
      });

      const data: ColaboradorApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error ?? 'Erro ao atualizar colaborador');
      }

      if (data.success && data.data && !Array.isArray(data.data)) {
        const colaboradorAtualizado = data.data as Colaborador;
        
        // Atualizar cache otimisticamente
        mutate((colaboradoresAtuais) => {
          if (colaboradoresAtuais) {
            return colaboradoresAtuais.map(colaborador => 
              colaborador.associacao_id === associacaoId ? colaboradorAtualizado : colaborador
            );
          }
          return [colaboradorAtualizado];
        });

        return colaboradorAtualizado;
      } else {
        throw new Error('Formato de resposta inválido');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao atualizar colaborador:', err);
      return null;
    }
  }, [mutate]);

  // Remover colaborador
  const removerColaborador = useCallback(async (associacaoId: number): Promise<boolean> => {
    setError(null);

    try {
      const response = await fetch(`/api/colaboradores/${associacaoId}`, {
        method: 'DELETE',
      });

      const data: ColaboradorApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error ?? 'Erro ao remover colaborador');
      }

      if (data.success) {
        // Atualizar cache otimisticamente
        mutate((colaboradoresAtuais) => {
          if (colaboradoresAtuais) {
            return colaboradoresAtuais.filter(colaborador => colaborador.associacao_id !== associacaoId);
          }
          return [];
        });

        return true;
      } else {
        throw new Error('Erro ao remover colaborador');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao remover colaborador:', err);
      return false;
    }
  }, [mutate]);

  // Aceitar convite
  const aceitarConvite = useCallback(async (dadosAceite: AceitarConviteData): Promise<any> => {
    setError(null);

    try {
      const response = await fetch('/api/colaboradores/aceitar-convite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dadosAceite),
      });

      const data: ColaboradorApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error ?? 'Erro ao aceitar convite');
      }

      if (data.success) {
        // Invalidar cache para recarregar dados atualizados
        invalidate();
        return data.data;
      } else {
        throw new Error('Erro ao aceitar convite');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao aceitar convite:', err);
      return null;
    }
  }, [invalidate]);

  // Buscar dados do convite
  const buscarDadosConvite = useCallback(async (token: string): Promise<DadosConvite | null> => {
    setError(null);

    try {
      const response = await fetch(`/api/colaboradores/convite/${token}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error ?? 'Erro ao buscar dados do convite');
      }

      if (data.success && data.data) {
        return data.data as DadosConvite;
      } else {
        throw new Error('Convite não encontrado');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao buscar dados do convite:', err);
      return null;
    }
  }, []);

  // Limpar erro
  const limparErro = useCallback(() => {
    setError(null);
  }, []);

  // Invalidar cache e recarregar
  const recarregar = useCallback(() => {
    invalidate();
    return refetch();
  }, [invalidate, refetch]);

  return {
    colaboradores: colaboradores ?? [],
    loading,
    error,
    criarConvite,
    atualizarColaborador,
    removerColaborador,
    aceitarConvite,
    buscarDadosConvite,
    limparErro,
    recarregar,
    invalidateCache: invalidate,
    refetch
  };
}

// Hook para buscar apenas colaboradores ativos
export function useColaboradoresAtivos() {
  return useColaboradoresComCache({ 
    ativo: true, 
    ativo_como_prestador: true,
    convite_aceito: true 
  });
}

// Hook para buscar convites pendentes
export function useConvitesPendentes() {
  return useColaboradoresComCache({ 
    convite_aceito: false 
  });
}

// Hook para buscar colaboradores por busca
export function useBuscarColaboradores(busca: string) {
  return useColaboradoresComCache({ 
    busca,
    ativo: true 
  });
}
