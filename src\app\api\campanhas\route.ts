import { NextRequest, NextResponse } from 'next/server';
import { createClient, createAdminClient } from '@/utils/supabase/server';
import { CriarCampanhaData, FiltrosCampanhas } from '@/types/marketing';

// GET - Listar campanhas da empresa
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ success: false, error: 'Não autorizado' }, { status: 401 });
    }

    // Buscar empresa do usuário usando cliente admin para evitar problemas de RLS
    const supabaseAdmin = createAdminClient();
    const { data: empresa, error: empresaError } = await supabaseAdmin
      .from('empresas')
      .select('empresa_id, plano_saas_id, planos_saas(nome_plano)')
      .eq('proprietario_user_id', user.id)
      .eq('status', 'ativo')
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json({ success: false, error: 'Empresa não encontrada' }, { status: 404 });
    }

    // Verificar se é plano Premium
    const planoSaas = Array.isArray(empresa.planos_saas)
      ? empresa.planos_saas[0]
      : empresa.planos_saas;
    const planoNome = planoSaas?.nome_plano;
    
    if (planoNome?.toLowerCase() !== 'premium') {
      return NextResponse.json({ 
        success: false, 
        error: 'Módulo de marketing disponível apenas no plano Premium' 
      }, { status: 403 });
    }

    // Extrair filtros
    const filtros: FiltrosCampanhas = {
      busca: searchParams.get('busca') || undefined,
      status: (searchParams.get('status') as any) || 'todos',
      tipo_campanha: (searchParams.get('tipo_campanha') as any) || 'todos',
      data_inicio: searchParams.get('data_inicio') || undefined,
      data_fim: searchParams.get('data_fim') || undefined,
    };

    const pagina = parseInt(searchParams.get('pagina') || '1');
    const limite = parseInt(searchParams.get('limite') || '10');
    const offset = (pagina - 1) * limite;

    // Construir query
    let query = supabase
      .from('campanhas_marketing')
      .select(`
        *,
        cupons(cupom_id, codigo_cupom, nome_cupom)
      `, { count: 'exact' })
      .eq('empresa_id', empresa.empresa_id)
      .order('created_at', { ascending: false });

    // Aplicar filtros
    if (filtros.busca) {
      query = query.or(`nome_campanha.ilike.%${filtros.busca}%,descricao.ilike.%${filtros.busca}%`);
    }

    if (filtros.status && filtros.status !== 'todos') {
      query = query.eq('status', filtros.status);
    }

    if (filtros.tipo_campanha && filtros.tipo_campanha !== 'todos') {
      query = query.eq('tipo_campanha', filtros.tipo_campanha);
    }

    if (filtros.data_inicio) {
      query = query.gte('created_at', filtros.data_inicio);
    }

    if (filtros.data_fim) {
      query = query.lte('created_at', filtros.data_fim);
    }

    // Executar query com paginação
    const { data: campanhas, error: campanhasError, count } = await query
      .range(offset, offset + limite - 1);

    if (campanhasError) {
      console.error('Erro ao buscar campanhas:', campanhasError);
      return NextResponse.json({ success: false, error: 'Erro ao buscar campanhas' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: {
        campanhas: campanhas || [],
        total: count || 0,
        pagina,
        total_paginas: Math.ceil((count || 0) / limite)
      }
    });

  } catch (error) {
    console.error('Erro na API de campanhas:', error);
    return NextResponse.json({ success: false, error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// POST - Criar nova campanha
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const body: CriarCampanhaData = await request.json();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ success: false, error: 'Não autorizado' }, { status: 401 });
    }

    // Buscar empresa do usuário usando cliente admin para evitar problemas de RLS
    const supabaseAdmin = createAdminClient();
    const { data: empresa, error: empresaError } = await supabaseAdmin
      .from('empresas')
      .select('empresa_id, plano_saas_id, planos_saas(nome_plano)')
      .eq('proprietario_user_id', user.id)
      .eq('status', 'ativo')
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json({ success: false, error: 'Empresa não encontrada' }, { status: 404 });
    }

    // Verificar se é plano Premium
    const planoSaas2 = Array.isArray(empresa.planos_saas)
      ? empresa.planos_saas[0]
      : empresa.planos_saas;
    const planoNome = planoSaas2?.nome_plano;
    
    if (planoNome?.toLowerCase() !== 'premium') {
      return NextResponse.json({ 
        success: false, 
        error: 'Módulo de marketing disponível apenas no plano Premium' 
      }, { status: 403 });
    }

    // Validações
    const erros = validarDadosCampanha(body);
    if (Object.keys(erros).length > 0) {
      return NextResponse.json({ success: false, error: 'Dados inválidos', erros }, { status: 400 });
    }

    // Verificar se cupom existe (se fornecido)
    if (body.cupom_id) {
      const { data: cupom, error: cupomError } = await supabase
        .from('cupons')
        .select('cupom_id')
        .eq('cupom_id', body.cupom_id)
        .eq('empresa_id', empresa.empresa_id)
        .single();

      if (cupomError || !cupom) {
        return NextResponse.json({ 
          success: false, 
          error: 'Cupom não encontrado ou não pertence à sua empresa' 
        }, { status: 400 });
      }
    }

    // Criar campanha
    const { data: campanha, error: campanhaError } = await supabase
      .from('campanhas_marketing')
      .insert([{
        empresa_id: empresa.empresa_id,
        ...body,
        status: 'rascunho'
      }])
      .select()
      .single();

    if (campanhaError) {
      console.error('Erro ao criar campanha:', campanhaError);
      return NextResponse.json({ success: false, error: 'Erro ao criar campanha' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: campanha,
      message: 'Campanha criada com sucesso'
    });

  } catch (error) {
    console.error('Erro na criação de campanha:', error);
    return NextResponse.json({ success: false, error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// Função de validação
function validarDadosCampanha(dados: CriarCampanhaData): Record<string, string> {
  const erros: Record<string, string> = {};

  if (!dados.nome_campanha || dados.nome_campanha.length < 3) {
    erros.nome_campanha = 'Nome deve ter pelo menos 3 caracteres';
  }

  if (!dados.descricao || dados.descricao.length < 10) {
    erros.descricao = 'Descrição deve ter pelo menos 10 caracteres';
  }

  if (!dados.tipo_campanha || !['email', 'sms', 'ambos'].includes(dados.tipo_campanha)) {
    erros.tipo_campanha = 'Tipo de campanha inválido';
  }

  if ((dados.tipo_campanha === 'email' || dados.tipo_campanha === 'ambos') && !dados.assunto_email) {
    erros.assunto_email = 'Assunto do e-mail é obrigatório';
  }

  if ((dados.tipo_campanha === 'email' || dados.tipo_campanha === 'ambos') && !dados.conteudo_email) {
    erros.conteudo_email = 'Conteúdo do e-mail é obrigatório';
  }

  if ((dados.tipo_campanha === 'sms' || dados.tipo_campanha === 'ambos') && !dados.conteudo_sms) {
    erros.conteudo_sms = 'Conteúdo do SMS é obrigatório';
  }

  if (dados.conteudo_sms && dados.conteudo_sms.length > 160) {
    erros.conteudo_sms = 'Conteúdo do SMS não pode ter mais de 160 caracteres';
  }

  if (!dados.segmentacao || Object.keys(dados.segmentacao).length === 0) {
    erros.segmentacao = 'Pelo menos um critério de segmentação é obrigatório';
  }

  if (dados.data_agendamento) {
    const dataAgendamento = new Date(dados.data_agendamento);
    const agora = new Date();
    
    if (dataAgendamento <= agora) {
      erros.data_agendamento = 'Data de agendamento deve ser futura';
    }
  }

  return erros;
}
