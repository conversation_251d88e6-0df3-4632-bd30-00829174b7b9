import { useState, useEffect } from 'react';
import { PreferenciasNotificacao, TipoNotificacao } from '@/types/notifications';

interface UseNotificationPreferencesReturn {
  preferences: PreferenciasNotificacao | null;
  loading: boolean;
  error: string | null;
  updatePreferences: (preferences: Partial<PreferenciasNotificacao>) => Promise<boolean>;
  resetToDefault: () => Promise<boolean>;
  toggleChannel: (channel: 'email' | 'sms' | 'push') => Promise<boolean>;
  toggleNotificationType: (type: TipoNotificacao) => Promise<boolean>;
}

export function useNotificationPreferences(): UseNotificationPreferencesReturn {
  const [preferences, setPreferences] = useState<PreferenciasNotificacao | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Carregar preferências do usuário
  const loadPreferences = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/notifications/preferences');
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Erro ao carregar preferências');
      }

      setPreferences(result.data);
    } catch (err) {
      console.error('Erro ao carregar preferências:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  };

  // Atualizar preferências
  const updatePreferences = async (newPreferences: Partial<PreferenciasNotificacao>): Promise<boolean> => {
    try {
      setError(null);

      const response = await fetch('/api/notifications/preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newPreferences)
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Erro ao atualizar preferências');
      }

      setPreferences(result.data);
      return true;
    } catch (err) {
      console.error('Erro ao atualizar preferências:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      return false;
    }
  };

  // Resetar para padrão
  const resetToDefault = async (): Promise<boolean> => {
    try {
      setError(null);

      const response = await fetch('/api/notifications/preferences', {
        method: 'POST'
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Erro ao resetar preferências');
      }

      setPreferences(result.data);
      return true;
    } catch (err) {
      console.error('Erro ao resetar preferências:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      return false;
    }
  };

  // Alternar canal de notificação
  const toggleChannel = async (channel: 'email' | 'sms' | 'push'): Promise<boolean> => {
    if (!preferences) return false;

    const fieldMap = {
      email: 'email_enabled',
      sms: 'sms_enabled',
      push: 'push_enabled'
    } as const;

    const field = fieldMap[channel];
    const newValue = !preferences[field];

    return updatePreferences({
      [field]: newValue
    });
  };

  // Alternar tipo de notificação
  const toggleNotificationType = async (type: TipoNotificacao): Promise<boolean> => {
    if (!preferences) return false;

    const currentTypes = preferences.tipos_habilitados || [];
    const isEnabled = currentTypes.includes(type);

    const newTypes = isEnabled
      ? currentTypes.filter(t => t !== type)
      : [...currentTypes, type];

    return updatePreferences({
      tipos_habilitados: newTypes
    });
  };

  // Carregar preferências na inicialização
  useEffect(() => {
    loadPreferences();
  }, []);

  return {
    preferences,
    loading,
    error,
    updatePreferences,
    resetToDefault,
    toggleChannel,
    toggleNotificationType
  };
}

// Hook para verificar se um tipo de notificação está habilitado
export function useNotificationEnabled(type: TipoNotificacao) {
  const { preferences } = useNotificationPreferences();
  
  if (!preferences) return true; // Default habilitado se não carregou ainda

  return preferences.tipos_habilitados?.includes(type) ?? true;
}

// Hook para verificar se um canal está habilitado
export function useChannelEnabled(channel: 'email' | 'sms' | 'push') {
  const { preferences } = useNotificationPreferences();
  
  if (!preferences) return true; // Default habilitado se não carregou ainda

  switch (channel) {
    case 'email':
      return preferences.email_enabled ?? true;
    case 'sms':
      return preferences.sms_enabled ?? true;
    case 'push':
      return preferences.push_enabled ?? true;
    default:
      return false;
  }
}
