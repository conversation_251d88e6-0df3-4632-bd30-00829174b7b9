import { NextResponse } from 'next/server';
import { createClient, createAdminClient } from '@/utils/supabase/server';
import { HorarioEmpresa } from '@/types/horarios';

// GET - Buscar horários da empresa
export async function GET() {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Buscar empresa do usuário (proprietário) usando cliente admin para evitar problemas de RLS
    const supabaseAdmin = createAdminClient();
    const { data: empresa, error: empresaError } = await supabaseAdmin
      .from('empresas')
      .select('empresa_id, horario_funcionamento')
      .eq('proprietario_user_id', user.id)
      .eq('status', 'ativo')
      .single();

    if (empresaError) {
      console.error('Erro ao buscar empresa:', empresaError);
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        empresa_id: empresa.empresa_id,
        horario_funcionamento: empresa.horario_funcionamento || criarHorarioPadrao()
      }
    });

  } catch (error: any) {
    console.error('Erro geral na API de horários da empresa:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// PUT - Atualizar horários da empresa
export async function PUT(request: Request) {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Verificar se é proprietário
    const userRole = user.user_metadata?.role;
    if (userRole !== 'Proprietario') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado. Apenas proprietários podem alterar horários da empresa.' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { horario_funcionamento } = body;

    if (!horario_funcionamento) {
      return NextResponse.json(
        { success: false, error: 'Horário de funcionamento é obrigatório' },
        { status: 400 }
      );
    }

    // Validar estrutura do horário
    const validacao = validarHorarioEmpresa(horario_funcionamento);
    if (!validacao.valido) {
      return NextResponse.json(
        { success: false, error: `Horário inválido: ${validacao.erros.join(', ')}` },
        { status: 400 }
      );
    }

    // Buscar empresa do usuário usando cliente admin para evitar problemas de RLS
    const supabaseAdmin = createAdminClient();
    const { data: empresa, error: empresaError } = await supabaseAdmin
      .from('empresas')
      .select('empresa_id')
      .eq('proprietario_user_id', user.id)
      .eq('status', 'ativo')
      .single();

    if (empresaError) {
      console.error('Erro ao buscar empresa:', empresaError);
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada' },
        { status: 404 }
      );
    }

    // Atualizar horários da empresa
    const { error: updateError } = await supabase
      .from('empresas')
      .update({ 
        horario_funcionamento: horario_funcionamento,
        updated_at: new Date().toISOString()
      })
      .eq('empresa_id', empresa.empresa_id);

    if (updateError) {
      console.error('Erro ao atualizar horários:', updateError);
      return NextResponse.json(
        { success: false, error: 'Erro ao atualizar horários da empresa' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Horários da empresa atualizados com sucesso',
      data: { horario_funcionamento }
    });

  } catch (error: any) {
    console.error('Erro geral na atualização de horários:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// Função para criar horário padrão
function criarHorarioPadrao(): HorarioEmpresa {
  const horarioPadrao = {
    ativo: true,
    abertura: "08:00",
    fechamento: "18:00",
    pausas: [
      {
        inicio: "12:00",
        fim: "13:00",
        descricao: "Almoço"
      }
    ]
  };

  return {
    segunda: horarioPadrao,
    terca: horarioPadrao,
    quarta: horarioPadrao,
    quinta: horarioPadrao,
    sexta: horarioPadrao,
    sabado: {
      ativo: true,
      abertura: "08:00",
      fechamento: "14:00",
      pausas: []
    },
    domingo: {
      ativo: false,
      abertura: "08:00",
      fechamento: "18:00",
      pausas: []
    },
    fuso_horario: "America/Sao_Paulo",
    observacoes: "Horário padrão de funcionamento"
  };
}

// Função para validar horário da empresa
function validarHorarioEmpresa(horario: HorarioEmpresa): { valido: boolean; erros: string[] } {
  const erros: string[] = [];
  const dias = ['segunda', 'terca', 'quarta', 'quinta', 'sexta', 'sabado', 'domingo'] as const;

  for (const dia of dias) {
    const horarioDia = horario[dia];
    
    if (horarioDia.ativo) {
      // Validar horários obrigatórios
      if (!horarioDia.abertura || !horarioDia.fechamento) {
        erros.push(`${dia}: Horário de abertura e fechamento são obrigatórios`);
        continue;
      }

      // Validar formato de horário (HH:mm)
      const regexHorario = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (!regexHorario.test(horarioDia.abertura)) {
        erros.push(`${dia}: Formato de horário de abertura inválido (use HH:mm)`);
      }
      if (!regexHorario.test(horarioDia.fechamento)) {
        erros.push(`${dia}: Formato de horário de fechamento inválido (use HH:mm)`);
      }

      // Validar se fechamento é posterior à abertura
      const abertura = new Date(`2000-01-01T${horarioDia.abertura}:00`);
      const fechamento = new Date(`2000-01-01T${horarioDia.fechamento}:00`);

      if (abertura >= fechamento) {
        erros.push(`${dia}: Horário de fechamento deve ser posterior ao de abertura`);
      }

      // Validar pausas
      if (horarioDia.pausas) {
        for (let i = 0; i < horarioDia.pausas.length; i++) {
          const pausa = horarioDia.pausas[i];
          
          if (!regexHorario.test(pausa.inicio) || !regexHorario.test(pausa.fim)) {
            erros.push(`${dia}: Formato de horário da pausa ${i + 1} inválido`);
            continue;
          }

          const inicioPausa = new Date(`2000-01-01T${pausa.inicio}:00`);
          const fimPausa = new Date(`2000-01-01T${pausa.fim}:00`);

          if (inicioPausa >= fimPausa) {
            erros.push(`${dia}: Horário de fim da pausa ${i + 1} deve ser posterior ao de início`);
          }

          if (inicioPausa < abertura || fimPausa > fechamento) {
            erros.push(`${dia}: Pausa ${i + 1} deve estar dentro do horário de funcionamento`);
          }
        }
      }
    }
  }

  // Verificar se pelo menos um dia está ativo
  const algumDiaAtivo = dias.some(dia => horario[dia].ativo);
  if (!algumDiaAtivo) {
    erros.push('Pelo menos um dia da semana deve estar ativo');
  }

  return {
    valido: erros.length === 0,
    erros
  };
}
