'use client';

import React, { useRef, useEffect } from 'react';
import { useFocusManagement } from '@/components/accessibility/AccessibilityUtils';

interface FormFieldProps {
  id: string;
  label: string;
  type?: string;
  required?: boolean;
  error?: string;
  helperText?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  placeholder?: string;
  disabled?: boolean;
  autoComplete?: string;
  'aria-describedby'?: string;
  children?: React.ReactNode; // Para select options
  as?: 'input' | 'textarea' | 'select';
  rows?: number;
  className?: string;
}

export function FormField({
  id,
  label,
  type = 'text',
  required = false,
  error,
  helperText,
  value,
  onChange,
  onBlur,
  placeholder,
  disabled = false,
  autoComplete,
  children,
  as = 'input',
  rows = 4,
  className = '',
  ...props
}: FormFieldProps) {
  const errorId = error ? `${id}-error` : undefined;
  const helperId = helperText ? `${id}-helper` : undefined;
  const describedBy = [errorId, helperId, props['aria-describedby']].filter(Boolean).join(' ');

  const baseClasses = `
    w-full px-3 py-2 border rounded-md text-sm font-[var(--font-geist-sans)]
    bg-[var(--background)] text-[var(--text-primary)] border-[var(--border-color)]
    focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:ring-offset-2
    focus:border-[var(--primary)] transition-colors duration-200
    disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-gray-50
    ${error ? 'border-[var(--error)] focus:ring-[var(--error)] focus:border-[var(--error)]' : ''}
    ${className}
  `;

  const renderInput = () => {
    const commonProps = {
      id,
      value,
      onChange,
      onBlur,
      placeholder,
      disabled,
      autoComplete,
      required,
      'aria-invalid': error ? true : undefined,
      'aria-describedby': describedBy || undefined,
      className: baseClasses,
    };

    switch (as) {
      case 'textarea':
        return <textarea {...commonProps} rows={rows} />;
      case 'select':
        return (
          <select {...commonProps}>
            {children}
          </select>
        );
      default:
        return <input {...commonProps} type={type} />;
    }
  };

  return (
    <div className="space-y-2">
      <label 
        htmlFor={id}
        className="block text-sm font-medium text-[var(--text-primary)]"
      >
        {label}
        {required && (
          <span 
            className="text-[var(--error)] ml-1" 
            aria-label="campo obrigatório"
            title="Este campo é obrigatório"
          >
            *
          </span>
        )}
      </label>
      
      {renderInput()}
      
      {error && (
        <p 
          id={errorId}
          className="text-[var(--error)] text-sm"
          role="alert"
          aria-live="polite"
        >
          {error}
        </p>
      )}
      
      {helperText && !error && (
        <p 
          id={helperId}
          className="text-[var(--text-secondary)] text-sm"
        >
          {helperText}
        </p>
      )}
    </div>
  );
}

interface AccessibleFormProps {
  children: React.ReactNode;
  onSubmit: (e: React.FormEvent) => void;
  title?: string;
  description?: string;
  className?: string;
  noValidate?: boolean;
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
}

export function AccessibleForm({
  children,
  onSubmit,
  title,
  description,
  className = '',
  noValidate = true,
  ...ariaProps
}: AccessibleFormProps) {
  const formRef = useRef<HTMLFormElement>(null);
  const { trapFocus } = useFocusManagement();

  useEffect(() => {
    if (formRef.current) {
      const cleanup = trapFocus(formRef as React.RefObject<HTMLElement>);
      return cleanup;
    }
  }, [trapFocus]);

  const titleId = title ? `form-title-${Math.random().toString(36).substring(2, 11)}` : undefined;
  const descId = description ? `form-desc-${Math.random().toString(36).substring(2, 11)}` : undefined;

  return (
    <div className={`space-y-6 ${className}`}>
      {title && (
        <div className="text-center">
          <h2 
            id={titleId}
            className="text-2xl font-bold text-[var(--text-primary)] mb-2"
          >
            {title}
          </h2>
          {description && (
            <p 
              id={descId}
              className="text-[var(--text-secondary)]"
            >
              {description}
            </p>
          )}
        </div>
      )}
      
      <form
        ref={formRef}
        onSubmit={onSubmit}
        noValidate={noValidate}
        className="space-y-4"
        aria-labelledby={titleId || ariaProps['aria-labelledby']}
        aria-describedby={descId || ariaProps['aria-describedby']}
        aria-label={ariaProps['aria-label']}
      >
        {children}
      </form>
    </div>
  );
}

// Hook para validação de formulários acessível
export function useFormValidation<T extends Record<string, any>>(
  initialValues: T,
  validationRules: Partial<Record<keyof T, (value: any) => string | undefined>>
) {
  const [values, setValues] = React.useState<T>(initialValues);
  const [errors, setErrors] = React.useState<Partial<Record<keyof T, string>>>({});
  const [touched, setTouched] = React.useState<Partial<Record<keyof T, boolean>>>({});

  const validateField = (name: keyof T, value: any) => {
    const rule = validationRules[name];
    if (rule) {
      return rule(value);
    }
    return undefined;
  };

  const handleChange = (name: keyof T) => (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const value = e.target.value;
    setValues(prev => ({ ...prev, [name]: value }));
    
    // Validar apenas se o campo já foi tocado
    if (touched[name]) {
      const error = validateField(name, value);
      setErrors(prev => ({ ...prev, [name]: error }));
    }
  };

  const handleBlur = (name: keyof T) => (
    e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    setTouched(prev => ({ ...prev, [name]: true }));
    const error = validateField(name, e.target.value);
    setErrors(prev => ({ ...prev, [name]: error }));
  };

  const validateAll = () => {
    const newErrors: Partial<Record<keyof T, string>> = {};
    let isValid = true;

    Object.keys(values).forEach(key => {
      const error = validateField(key as keyof T, values[key as keyof T]);
      if (error) {
        newErrors[key as keyof T] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    setTouched(
      Object.keys(values).reduce((acc, key) => ({ ...acc, [key]: true }), {})
    );

    return isValid;
  };

  const reset = () => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
  };

  return {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    validateAll,
    reset,
    isValid: Object.keys(errors).length === 0,
  };
}

// Componente para grupos de campos relacionados
interface FieldsetProps {
  legend: string;
  children: React.ReactNode;
  className?: string;
  required?: boolean;
}

export function Fieldset({ legend, children, className = '', required = false }: FieldsetProps) {
  return (
    <fieldset className={`border border-[var(--border-color)] rounded-md p-4 ${className}`}>
      <legend className="text-sm font-medium text-[var(--text-primary)] px-2">
        {legend}
        {required && (
          <span 
            className="text-[var(--error)] ml-1" 
            aria-label="grupo obrigatório"
          >
            *
          </span>
        )}
      </legend>
      <div className="space-y-4 mt-2">
        {children}
      </div>
    </fieldset>
  );
}
