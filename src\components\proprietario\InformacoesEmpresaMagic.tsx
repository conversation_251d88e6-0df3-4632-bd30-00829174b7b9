'use client';

import React from 'react';
import Link from 'next/link';
import { MagicCard } from '@/components/ui/magic-card';
import { TextAnimate } from '@/components/ui/text-animate';
import { ShimmerButton } from '@/components/ui/shimmer-button';
import { LogoEmpresa } from '@/components/ui/LogoEmpresa';
import { useEmpresaProprietario } from '@/hooks/useEmpresaProprietario';
import { motion } from 'motion/react';

interface InformacoesEmpresaMagicProps {
  className?: string;
}

export function InformacoesEmpresaMagic({ className = '' }: InformacoesEmpresaMagicProps) {
  const { 
    empresa, 
    planoSaas, 
    statusConfiguracao, 
    loading, 
    error,
    temEmpresa,
    empresaAtiva,
    podeReceberPagamentos,
    precisaConfiguracaoInicial,
    obterProximosPassos
  } = useEmpresaProprietario();

  if (loading) {
    return (
      <MagicCard className={`animate-pulse ${className}`} gradientColor="#3B82F6">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-gray-200 rounded-lg"></div>
          <div className="flex-1 space-y-2">
            <div className="h-6 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </MagicCard>
    );
  }

  if (error) {
    return (
      <MagicCard className={`border-red-200 bg-red-50 ${className}`} gradientColor="#EF4444">
        <div className="text-center space-y-4">
          <div className="text-red-600 text-4xl">⚠️</div>
          <div>
            <h3 className="font-semibold text-red-800 text-lg">Erro ao carregar dados</h3>
            <p className="text-sm text-red-700 mt-2">
              Não foi possível carregar as informações da empresa. Tente novamente.
            </p>
          </div>
        </div>
      </MagicCard>
    );
  }

  if (!temEmpresa) {
    return (
      <MagicCard className={`border-yellow-200 bg-yellow-50 ${className}`} gradientColor="#F59E0B">
        <div className="text-center space-y-4">
          <motion.div 
            className="text-yellow-600 text-4xl"
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            🏢
          </motion.div>
          <div>
            <TextAnimate 
              animation="blurInUp" 
              className="font-semibold text-yellow-800 text-lg"
            >
              Empresa não encontrada
            </TextAnimate>
            <p className="text-sm text-yellow-700 mt-2">
              Você precisa criar uma empresa para acessar o dashboard do proprietário.
            </p>
          </div>
          <Link href="/onboarding">
            <ShimmerButton 
              className="bg-yellow-600 hover:bg-yellow-700"
              shimmerColor="#FCD34D"
            >
              Criar Empresa
            </ShimmerButton>
          </Link>
        </div>
      </MagicCard>
    );
  }

  const proximosPassos = obterProximosPassos();
  const precisaConfiguracao = precisaConfiguracaoInicial();

  return (
    <MagicCard 
      className={`relative overflow-hidden ${className}`}
      gradientColor={empresaAtiva ? "#10B981" : "#F59E0B"}
      gradientSize={300}
    >
      <div className="relative z-10">
        {/* Header com status */}
        <div className="flex items-center justify-between mb-6">
          <TextAnimate 
            animation="slideRight" 
            className="text-2xl font-bold text-white"
            by="word"
          >
            Informações da Empresa
          </TextAnimate>
          
          <motion.div
            className={`px-3 py-1 rounded-full text-xs font-medium ${
              empresaAtiva 
                ? 'bg-green-100 text-green-800 border border-green-200' 
                : 'bg-yellow-100 text-yellow-800 border border-yellow-200'
            }`}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.5 }}
          >
            {empresaAtiva ? '✅ Ativa' : '⚠️ Configuração Pendente'}
          </motion.div>
        </div>

        {/* Conteúdo principal */}
        <div className="flex items-start space-x-6">
          {/* Logo da empresa */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <LogoEmpresa 
              empresa={empresa} 
              size="lg" 
              className="w-20 h-20 rounded-xl shadow-lg border-2 border-white/20"
            />
          </motion.div>

          {/* Informações principais */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div>
                <TextAnimate
                  animation="blurInUp"
                  className="text-xl font-bold text-white truncate"
                  delay={0.3}
                >
                  {empresa?.nome_empresa || 'Nome não definido'}
                </TextAnimate>
                
                {empresa?.segmento && (
                  <motion.p 
                    className="text-sm text-white/80 mt-1"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.4 }}
                  >
                    {empresa.segmento}
                  </motion.p>
                )}
                
                {empresa?.endereco && (
                  <motion.p 
                    className="text-sm text-white/70 mt-1 truncate"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5 }}
                  >
                    📍 {empresa.endereco}
                    {empresa.numero ? `, ${empresa.numero}` : ''}
                    {empresa.bairro ? ` - ${empresa.bairro}` : ''}
                    {empresa.cidade ? `, ${empresa.cidade}` : ''}
                  </motion.p>
                )}
              </div>
            </div>

            {/* Status de configuração */}
            <div className="mt-4 space-y-2">
              <div className="flex items-center space-x-4">
                <motion.div 
                  className={`flex items-center space-x-2 px-3 py-1 rounded-full text-xs ${
                    podeReceberPagamentos 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                >
                  <span>{podeReceberPagamentos ? '💳' : '❌'}</span>
                  <span>
                    {podeReceberPagamentos ? 'Pagamentos Ativos' : 'Pagamentos Pendentes'}
                  </span>
                </motion.div>

                {planoSaas && (
                  <motion.div 
                    className="flex items-center space-x-2 px-3 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7 }}
                  >
                    <span>📋</span>
                    <span>Plano {planoSaas.nome_plano}</span>
                  </motion.div>
                )}
              </div>
            </div>

            {/* Ações rápidas */}
            {precisaConfiguracao && proximosPassos.length > 0 && (
              <motion.div 
                className="mt-4 flex space-x-2"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
              >
                <Link href="/proprietario/empresa">
                  <ShimmerButton 
                    className="text-xs px-4 py-2"
                    shimmerColor="#60A5FA"
                    background="rgba(59, 130, 246, 0.8)"
                  >
                    Configurar Empresa
                  </ShimmerButton>
                </Link>
              </motion.div>
            )}
          </div>
        </div>

        {/* Efeito de partículas de fundo */}
        <div className="absolute inset-0 opacity-10">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -10, 0],
                opacity: [0.3, 1, 0.3],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      </div>
    </MagicCard>
  );
}
