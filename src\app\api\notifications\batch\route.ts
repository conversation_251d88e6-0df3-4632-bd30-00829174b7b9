import { NextResponse } from 'next/server';
import { NotificationService } from '@/services/NotificationService';
import { createAdminClient } from '@/utils/supabase/server';

/**
 * API para processamento em lote de notificações
 * Útil para lembretes automáticos e reenvio de notificações falhadas
 */
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { action, limite } = body;

    console.log(`📧 API Batch: Processando ação ${action}`);

    const notificationService = new NotificationService();
    const supabase = createAdminClient();

    if (action === 'lembretes_confirmacao') {
      // Buscar agendamentos pendentes próximos ao prazo (2 horas)
      const duasHorasEmMs = 2 * 60 * 60 * 1000;
      const agora = new Date();
      const prazoLimite = new Date(agora.getTime() + duasHorasEmMs);

      const { data: agendamentosPendentes, error } = await supabase
        .from('agendamentos')
        .select(`
          agendamento_id,
          cliente_user_id,
          empresa_id,
          colaborador_user_id,
          prazo_confirmacao,
          empresas!inner (
            proprietario_user_id
          )
        `)
        .eq('status_agendamento', 'Pendente')
        .lt('prazo_confirmacao', prazoLimite.toISOString())
        .gt('prazo_confirmacao', agora.toISOString())
        .limit(limite || 10);

      if (error) {
        throw new Error(`Erro ao buscar agendamentos pendentes: ${error.message}`);
      }

      const resultados = [];
      
      for (const agendamento of agendamentosPendentes || []) {
        try {
          const { extrairContextoAgendamento } = await import('@/utils/notificationHelpers');
          const contexto = await extrairContextoAgendamento(agendamento.agendamento_id);
          
          if (contexto) {
            // Enviar lembrete para o proprietário
            const resultado = await notificationService.processarNotificacao({
              tipo: 'lembrete_confirmacao',
              destinatario_id: contexto.proprietarioId,
              contexto: contexto.contextoProprietario,
              canal: 'email',
              agendamento_id: agendamento.agendamento_id,
              empresa_id: agendamento.empresa_id
            });

            resultados.push({
              agendamento_id: agendamento.agendamento_id,
              success: resultado.success,
              error: resultado.error
            });
          }
        } catch (error) {
          console.error(`❌ Erro ao processar lembrete para agendamento ${agendamento.agendamento_id}:`, error);
          resultados.push({
            agendamento_id: agendamento.agendamento_id,
            success: false,
            error: error instanceof Error ? error.message : 'Erro desconhecido'
          });
        }
      }

      return NextResponse.json({
        success: true,
        message: `Processados ${resultados.length} lembretes de confirmação`,
        resultados
      });

    } else if (action === 'lembretes_agendamento') {
      // Buscar agendamentos confirmados para amanhã
      const amanha = new Date();
      amanha.setDate(amanha.getDate() + 1);
      const inicioAmanha = new Date(amanha.getFullYear(), amanha.getMonth(), amanha.getDate());
      const fimAmanha = new Date(inicioAmanha.getTime() + 24 * 60 * 60 * 1000);

      const { data: agendamentosAmanha, error } = await supabase
        .from('agendamentos')
        .select('agendamento_id, cliente_user_id')
        .eq('status_agendamento', 'Confirmado')
        .gte('data_hora_inicio', inicioAmanha.toISOString())
        .lt('data_hora_inicio', fimAmanha.toISOString())
        .limit(limite || 20);

      if (error) {
        throw new Error(`Erro ao buscar agendamentos de amanhã: ${error.message}`);
      }

      const resultados = [];
      
      for (const agendamento of agendamentosAmanha || []) {
        try {
          const { extrairContextoAgendamento } = await import('@/utils/notificationHelpers');
          const contexto = await extrairContextoAgendamento(agendamento.agendamento_id);
          
          if (contexto) {
            // Enviar lembrete para o cliente
            const resultado = await notificationService.processarNotificacao({
              tipo: 'lembrete_agendamento',
              destinatario_id: contexto.clienteId,
              contexto: contexto.contextoCliente,
              canal: 'email',
              agendamento_id: agendamento.agendamento_id
            });

            resultados.push({
              agendamento_id: agendamento.agendamento_id,
              success: resultado.success,
              error: resultado.error
            });
          }
        } catch (error) {
          console.error(`❌ Erro ao processar lembrete para agendamento ${agendamento.agendamento_id}:`, error);
          resultados.push({
            agendamento_id: agendamento.agendamento_id,
            success: false,
            error: error instanceof Error ? error.message : 'Erro desconhecido'
          });
        }
      }

      return NextResponse.json({
        success: true,
        message: `Processados ${resultados.length} lembretes de agendamento`,
        resultados
      });

    } else if (action === 'reenviar_falhadas') {
      // Reenviar notificações que falharam
      const notificacoesPendentes = await notificationService.buscarNotificacoesPendentes(limite || 10);
      const resultados = [];

      for (const notificacao of notificacoesPendentes) {
        const resultado = await notificationService.reenviarNotificacao(notificacao.notificacao_id);
        resultados.push({
          notificacao_id: notificacao.notificacao_id,
          success: resultado.success,
          tentativas: resultado.tentativas,
          error: resultado.error
        });
      }

      return NextResponse.json({
        success: true,
        message: `Processadas ${resultados.length} notificações pendentes`,
        resultados
      });

    } else {
      return NextResponse.json({
        success: false,
        error: 'Ação não reconhecida. Use: lembretes_confirmacao, lembretes_agendamento, reenviar_falhadas'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Erro na API de notificações em lote:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 });
  }
}

/**
 * GET - Estatísticas de notificações
 */
export async function GET() {
  try {
    const supabase = createAdminClient();

    // Buscar estatísticas das últimas 24 horas
    const ultimasDias = new Date();
    ultimasDias.setDate(ultimasDias.getDate() - 1);

    const { data: estatisticas, error } = await supabase
      .from('notificacoes')
      .select('enviada, tipo_notificacao, canal, tentativas_envio')
      .gte('created_at', ultimasDias.toISOString());

    if (error) {
      throw new Error(`Erro ao buscar estatísticas: ${error.message}`);
    }

    const stats = {
      total: estatisticas?.length || 0,
      enviadas: estatisticas?.filter(n => n.enviada).length || 0,
      pendentes: estatisticas?.filter(n => !n.enviada).length || 0,
      por_tipo: {} as Record<string, number>,
      por_canal: {} as Record<string, number>,
      tentativas_multiplas: estatisticas?.filter(n => n.tentativas_envio > 1).length || 0
    };

    // Agrupar por tipo
    estatisticas?.forEach(notif => {
      stats.por_tipo[notif.tipo_notificacao] = (stats.por_tipo[notif.tipo_notificacao] || 0) + 1;
      stats.por_canal[notif.canal] = (stats.por_canal[notif.canal] || 0) + 1;
    });

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('❌ Erro ao buscar estatísticas de notificações:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 });
  }
}
