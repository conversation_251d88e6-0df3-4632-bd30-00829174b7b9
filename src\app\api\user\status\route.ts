import { NextResponse } from 'next/server';
import { createAdminClient } from '@/utils/supabase/server';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({
        error: 'userId é obrigatório'
      }, { status: 400 });
    }

    console.log(`🔍 Verificando status do usuário: ${userId}`);

    const supabase = createAdminClient();

    // Obter dados do usuário
    const { data: { user }, error } = await supabase.auth.admin.getUserById(userId);

    if (error || !user) {
      console.error('❌ Erro ao obter usuário:', error);
      return NextResponse.json({
        error: 'Usuário não encontrado'
      }, { status: 404 });
    }

    const userStatus = {
      id: user.id,
      email: user.email,
      role: user.user_metadata?.role || 'Usuario',
      pagamento_confirmado: user.user_metadata?.pagamento_confirmado || false,
      onboarding_pendente: user.user_metadata?.onboarding_pendente || false,
      plano_selecionado: user.user_metadata?.plano_selecionado || null,
      stripe_payment_id: user.user_metadata?.stripe_payment_id || null,
      data_pagamento: user.user_metadata?.data_pagamento || null,
      updated_at: user.updated_at
    };

    console.log('✅ Status do usuário:', userStatus);

    return NextResponse.json({
      success: true,
      user: userStatus
    });

  } catch (error: any) {
    console.error('❌ Erro ao verificar status do usuário:', error);
    return NextResponse.json({
      error: `Erro interno: ${error.message}`
    }, { status: 500 });
  }
}

// Endpoint para forçar atualização do usuário
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { userId } = body;

    if (!userId) {
      return NextResponse.json({
        error: 'userId é obrigatório'
      }, { status: 400 });
    }

    console.log(`🔄 Forçando atualização do usuário: ${userId}`);

    const supabase = createAdminClient();

    // Obter dados atuais do usuário
    const { data: { user }, error: getUserError } = await supabase.auth.admin.getUserById(userId);

    if (getUserError || !user) {
      console.error('❌ Erro ao obter usuário:', getUserError);
      return NextResponse.json({
        error: 'Usuário não encontrado'
      }, { status: 404 });
    }

    // Forçar uma pequena atualização para trigger refresh
    const { error: updateError } = await supabase.auth.admin.updateUserById(userId, {
      user_metadata: {
        ...user.user_metadata,
        last_status_check: new Date().toISOString()
      }
    });

    if (updateError) {
      console.error('❌ Erro ao atualizar usuário:', updateError);
      return NextResponse.json({
        error: 'Erro ao atualizar usuário'
      }, { status: 500 });
    }

    // Retornar status atualizado
    const { data: { user: updatedUser }, error: getUpdatedError } = await supabase.auth.admin.getUserById(userId);

    if (getUpdatedError || !updatedUser) {
      console.error('❌ Erro ao obter usuário atualizado:', getUpdatedError);
      return NextResponse.json({
        error: 'Erro ao obter dados atualizados'
      }, { status: 500 });
    }

    const userStatus = {
      id: updatedUser.id,
      email: updatedUser.email,
      role: updatedUser.user_metadata?.role || 'Usuario',
      pagamento_confirmado: updatedUser.user_metadata?.pagamento_confirmado || false,
      onboarding_pendente: updatedUser.user_metadata?.onboarding_pendente || false,
      plano_selecionado: updatedUser.user_metadata?.plano_selecionado || null,
      stripe_payment_id: updatedUser.user_metadata?.stripe_payment_id || null,
      data_pagamento: updatedUser.user_metadata?.data_pagamento || null,
      updated_at: updatedUser.updated_at
    };

    console.log('✅ Usuário atualizado:', userStatus);

    return NextResponse.json({
      success: true,
      message: 'Usuário atualizado com sucesso',
      user: userStatus
    });

  } catch (error: any) {
    console.error('❌ Erro ao atualizar usuário:', error);
    return NextResponse.json({
      error: `Erro interno: ${error.message}`
    }, { status: 500 });
  }
}
