/**
 * API para estatísticas do sistema Round-Robin
 * Implementação da Tarefa #17 - Lógica de Agendamento Round-Robin
 * 
 * GET /api/agendamentos/round-robin-stats
 * Retorna estatísticas de distribuição de agendamentos entre colaboradores
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/client';
import { buscarEstatisticasColaboradores, calcularEstatisticasDistribuicao } from '@/utils/roundRobin';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Extrair parâmetros
    const empresa_id = searchParams.get('empresa_id');
    const servico_id = searchParams.get('servico_id');
    const periodo_dias = parseInt(searchParams.get('periodo_dias') || '30');
    const apenas_confirmados = searchParams.get('apenas_confirmados') === 'true';

    if (!empresa_id) {
      return NextResponse.json(
        { success: false, error: 'Parâmetro empresa_id é obrigatório' },
        { status: 400 }
      );
    }

    // Verificar se o usuário tem acesso à empresa
    const { data: colaboradorEmpresa } = await supabase
      .from('colaboradores_empresa')
      .select('associacao_id, papel')
      .eq('empresa_id', empresa_id)
      .eq('colaborador_user_id', user.id)
      .single();

    const { data: empresa } = await supabase
      .from('empresas')
      .select('proprietario_user_id')
      .eq('empresa_id', empresa_id)
      .single();

    // Verificar permissões (proprietário ou colaborador da empresa)
    const isProprietario = empresa?.proprietario_user_id === user.id;
    const isColaborador = !!colaboradorEmpresa;

    if (!isProprietario && !isColaborador) {
      return NextResponse.json(
        { success: false, error: 'Acesso negado' },
        { status: 403 }
      );
    }

    // Buscar colaboradores da empresa
    let query = supabase
      .from('colaboradores_empresa')
      .select('colaborador_user_id')
      .eq('empresa_id', empresa_id)
      .eq('ativo', true)
      .eq('ativo_como_prestador', true);

    // Se servico_id foi especificado, filtrar por colaboradores que fazem esse serviço
    if (servico_id) {
      const { data: colaboradoresServico } = await supabase
        .from('colaborador_servicos')
        .select('colaborador_user_id')
        .eq('servico_id', servico_id)
        .eq('ativo', true);

      if (colaboradoresServico && colaboradoresServico.length > 0) {
        const colaboradoresIds = colaboradoresServico.map(cs => cs.colaborador_user_id);
        query = query.in('colaborador_user_id', colaboradoresIds);
      } else {
        // Nenhum colaborador faz esse serviço
        return NextResponse.json({
          success: true,
          data: {
            empresa_id: parseInt(empresa_id),
            servico_id: servico_id ? parseInt(servico_id) : null,
            periodo_dias,
            apenas_confirmados,
            estatisticas: {
              total_colaboradores: 0,
              total_agendamentos: 0,
              distribuicao: [],
              colaborador_menos_utilizado: null,
              colaborador_mais_utilizado: null,
              diferenca_maxima: 0
            },
            colaboradores: []
          }
        });
      }
    }

    const { data: colaboradores, error: colaboradoresError } = await query;

    if (colaboradoresError) {
      console.error('Erro ao buscar colaboradores:', colaboradoresError);
      return NextResponse.json(
        { success: false, error: 'Erro ao buscar colaboradores' },
        { status: 500 }
      );
    }

    if (!colaboradores || colaboradores.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          empresa_id: parseInt(empresa_id),
          servico_id: servico_id ? parseInt(servico_id) : null,
          periodo_dias,
          apenas_confirmados,
          estatisticas: {
            total_colaboradores: 0,
            total_agendamentos: 0,
            distribuicao: [],
            colaborador_menos_utilizado: null,
            colaborador_mais_utilizado: null,
            diferenca_maxima: 0
          },
          colaboradores: []
        }
      });
    }

    // Buscar estatísticas dos colaboradores
    const colaboradoresIds = colaboradores.map(c => c.colaborador_user_id);
    
    const colaboradoresComEstatisticas = await buscarEstatisticasColaboradores(
      parseInt(empresa_id),
      servico_id ? parseInt(servico_id) : 0, // 0 significa todos os serviços
      colaboradoresIds,
      {
        periodo_dias,
        apenas_confirmados
      }
    );

    // Calcular estatísticas de distribuição
    const estatisticas = calcularEstatisticasDistribuicao(colaboradoresComEstatisticas);

    return NextResponse.json({
      success: true,
      data: {
        empresa_id: parseInt(empresa_id),
        servico_id: servico_id ? parseInt(servico_id) : null,
        periodo_dias,
        apenas_confirmados,
        estatisticas,
        colaboradores: colaboradoresComEstatisticas,
        gerado_em: new Date().toISOString()
      }
    });

  } catch (error: unknown) {
    console.error('Erro geral na API de estatísticas round-robin:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
