import React from 'react';
import Link from 'next/link';
import { Layout } from '@/components/layout/Layout';
import { Button } from '@/components/ui/Button';
import { Breadcrumbs } from '@/components/ui/Breadcrumbs';

const PlanosPage = () => {
  return (
    <Layout>
      <div className="bg-[var(--background)] py-12">
        <div className="container mx-auto px-4">
          {/* Breadcrumbs */}
          <div className="mb-8">
            <Breadcrumbs
              items={[
                { label: 'Início', href: '/' },
                { label: 'Planos' }
              ]}
            />
          </div>

          {/* Header Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-[var(--text-primary)] mb-4">
              Nossos Planos para Estabelecimentos
            </h1>
            <p className="text-xl text-[var(--text-secondary)] max-w-3xl mx-auto">
              Escolha o plano ideal para o seu negócio e comece a transformar a gestão do seu estabelecimento hoje mesmo.
            </p>
          </div>

          {/* Seção de Planos */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            {/* Plano Essencial */}
            <div className="bg-[var(--surface)] rounded-lg shadow-lg p-8 flex flex-col justify-between border-t-4 border-[var(--primary)] hover:shadow-xl transition-shadow duration-300">
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-3xl font-semibold text-[var(--text-primary)]">Plano Essencial</h2>
                  <span className="bg-[var(--primary)] text-[var(--text-on-primary)] px-3 py-1 rounded-full text-sm font-medium">
                    Mais Popular
                  </span>
                </div>
                <p className="text-[var(--text-secondary)] mb-6">Ideal para pequenos estabelecimentos que buscam otimizar a gestão de agendamentos e serviços.</p>
                <div className="text-4xl font-bold text-[var(--primary)] mb-6">
                  R$ 99<span className="text-xl font-normal text-[var(--text-secondary)]">/mês</span>
                </div>
                <ul className="text-[var(--text-primary)] space-y-3 mb-8">
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-[var(--success)] mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                    </svg>
                    Módulo de Agendamento Completo
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-[var(--success)] mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                    </svg>
                    Módulo de Pagamento Completo
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-[var(--success)] mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                    </svg>
                    Gestão de até 6 serviços
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-[var(--success)] mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                    </svg>
                    Até 2 colaboradores extras
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-[var(--success)] mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                    </svg>
                    Relatórios Básicos
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-[var(--success)] mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                    </svg>
                    Suporte por E-mail
                  </li>
                </ul>
              </div>
              <Link href="/comprar-plano?plano=essencial">
                <Button className="w-full bg-[var(--primary)] text-[var(--text-on-primary)] hover:bg-[var(--primary-hover)] py-3 text-lg font-semibold transition-colors duration-300">
                  Escolher Plano Essencial
                </Button>
              </Link>
            </div>

            {/* Plano Premium */}
            <div className="bg-[var(--surface)] rounded-lg shadow-lg p-8 flex flex-col justify-between border-t-4 border-[var(--accent)] hover:shadow-xl transition-shadow duration-300 relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-[var(--accent)] text-[var(--text-on-accent)] px-4 py-2 rounded-full text-sm font-medium shadow-lg">
                  Recomendado
                </span>
              </div>
              <div>
                <h2 className="text-3xl font-semibold text-[var(--text-primary)] mb-4">Plano Premium</h2>
                <p className="text-[var(--text-secondary)] mb-6">Solução completa para estabelecimentos que buscam crescimento e funcionalidades avançadas.</p>
                <div className="text-4xl font-bold text-[var(--accent)] mb-6">
                  R$ 199<span className="text-xl font-normal text-[var(--text-secondary)]">/mês</span>
                </div>
                <ul className="text-[var(--text-primary)] space-y-3 mb-8">
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-[var(--success)] mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                    </svg>
                    Tudo do Plano Essencial
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-[var(--success)] mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                    </svg>
                    Gestão de até 12 serviços
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-[var(--success)] mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                    </svg>
                    Até 6 colaboradores extras
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-[var(--success)] mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                    </svg>
                    Relatórios Completos
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-[var(--success)] mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                    </svg>
                    Módulo de Marketing Básico
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-[var(--success)] mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                    </svg>
                    Assinatura de Serviços Mensal
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-[var(--success)] mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                    </svg>
                    Suporte por E-mail Prioritário
                  </li>
                </ul>
              </div>
              <Link href="/comprar-plano?plano=premium">
                <Button className="w-full bg-[var(--accent)] text-[var(--text-on-accent)] hover:bg-[var(--accent-hover)] py-3 text-lg font-semibold transition-colors duration-300">
                  Escolher Plano Premium
                </Button>
              </Link>
            </div>
          </div>

          {/* Tabela Comparativa */}
          <div className="bg-[var(--surface)] rounded-lg shadow-lg p-8 mb-12">
            <h2 className="text-3xl font-semibold text-[var(--text-primary)] mb-6 text-center">Comparativo de Planos</h2>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-[var(--border-color)]">
                <thead className="bg-[var(--background)]">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-[var(--text-secondary)] uppercase tracking-wider">
                      Funcionalidade
                    </th>
                    <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-[var(--text-secondary)] uppercase tracking-wider">
                      Plano Essencial
                    </th>
                    <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-[var(--text-secondary)] uppercase tracking-wider">
                      Plano Premium
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-[var(--surface)] divide-y divide-[var(--border-color)]">
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--text-primary)]">Módulo de Agendamento</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--success)]">Completo</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--success)]">Completo</td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--text-primary)]">Módulo de Pagamento</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--success)]">Completo</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--success)]">Completo</td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--text-primary)]">Gestão de Serviços (Limite)</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--text-secondary)]">Até 6 serviços</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--text-secondary)]">Até 12 serviços</td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--text-primary)]">Gestão de Colaboradores Extras</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--text-secondary)]">Até 2</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--text-secondary)]">Até 6</td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--text-primary)]">Relatórios</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--text-secondary)]">Básico</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--success)]">Completo</td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--text-primary)]">Módulo de Marketing Básico</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--error)]">Não</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--success)]">Sim</td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--text-primary)]">Assinatura de Serviços Mensal</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--error)]">Não</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--success)]">Sim</td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--text-primary)]">Suporte</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--text-secondary)]">E-mail</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--text-secondary)]">E-mail Prioritário</td>
                  </tr>
              </tbody>
            </table>
          </div>
        </div>

          {/* Seção de FAQ */}
          <div className="bg-[var(--surface)] rounded-lg shadow-lg p-8">
            <h2 className="text-3xl font-semibold text-[var(--text-primary)] mb-6 text-center">Perguntas Frequentes</h2>
            <div className="space-y-6">
              {/* Pergunta 1 */}
              <div className="border-b border-[var(--border-color)] pb-4">
                <h3 className="text-xl font-semibold text-[var(--text-primary)] mb-2">Como funciona o período de teste?</h3>
                <p className="text-[var(--text-secondary)]">Atualmente, não oferecemos um período de teste gratuito. No entanto, você pode cancelar sua assinatura a qualquer momento. Consulte nossa política de cancelamento para mais detalhes.</p>
              </div>
              {/* Pergunta 2 */}
              <div className="border-b border-[var(--border-color)] pb-4">
                <h3 className="text-xl font-semibold text-[var(--text-primary)] mb-2">Posso mudar de plano a qualquer momento?</h3>
                <p className="text-[var(--text-secondary)]">Sim, você pode fazer upgrade ou downgrade do seu plano a qualquer momento. As alterações serão aplicadas no próximo ciclo de faturamento.</p>
              </div>
              {/* Pergunta 3 */}
              <div className="border-b border-[var(--border-color)] pb-4">
                <h3 className="text-xl font-semibold text-[var(--text-primary)] mb-2">Quais formas de pagamento são aceitas?</h3>
                <p className="text-[var(--text-secondary)]">Aceitamos pagamentos via cartão de crédito e Pix através do Stripe, garantindo segurança e praticidade.</p>
              </div>
              {/* Pergunta 4 */}
              <div>
                <h3 className="text-xl font-semibold text-[var(--text-primary)] mb-2">O que acontece se eu exceder o limite de serviços ou colaboradores?</h3>
                <p className="text-[var(--text-secondary)]">Se você exceder os limites do seu plano, será notificado e terá a opção de fazer upgrade para um plano que atenda às suas necessidades ou ajustar o uso.</p>
              </div>
            </div>
          </div>

          {/* CTA Final */}
          <div className="text-center mt-12">
            <h2 className="text-3xl font-bold text-[var(--text-primary)] mb-4">
              Pronto para começar?
            </h2>
            <p className="text-xl text-[var(--text-secondary)] mb-8">
              Escolha seu plano e transforme a gestão do seu estabelecimento hoje mesmo.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link href="/comprar-plano?plano=essencial">
                <Button className="bg-[var(--primary)] text-[var(--text-on-primary)] hover:bg-[var(--primary-hover)] px-8 py-3 text-lg font-semibold">
                  Começar com Essencial
                </Button>
              </Link>
              <Link href="/comprar-plano?plano=premium">
                <Button className="bg-[var(--accent)] text-[var(--text-on-accent)] hover:bg-[var(--accent-hover)] px-8 py-3 text-lg font-semibold">
                  Começar com Premium
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default PlanosPage;