"use client";

import React from 'react';
import { motion } from 'motion/react';
import { cn } from '@/lib/utils';
import Particles from './particles';
import { ScrollProgress } from './scroll-progress';

interface VisualEffectsProps {
  className?: string;
  showParticles?: boolean;
  showScrollProgress?: boolean;
  showFloatingElements?: boolean;
  particleColor?: string;
  particleQuantity?: number;
}

export function VisualEffects({
  className = '',
  showParticles = true,
  showScrollProgress = true,
  showFloatingElements = true,
  particleColor = '#3B82F6',
  particleQuantity = 50
}: VisualEffectsProps) {
  return (
    <div className={cn('fixed inset-0 pointer-events-none overflow-hidden', className)}>
      {/* Scroll Progress Bar */}
      {showScrollProgress && <ScrollProgress />}

      {/* Background Particles */}
      {showParticles && (
        <Particles
          className="absolute inset-0"
          quantity={particleQuantity}
          color={particleColor}
          size={0.8}
          staticity={30}
          ease={80}
        />
      )}

      {/* Floating Geometric Elements */}
      {showFloatingElements && (
        <>
          {/* Círculos flutuantes */}
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={`circle-${i}`}
              className="absolute w-4 h-4 bg-blue-400/10 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -30, 0],
                x: [0, Math.random() * 20 - 10, 0],
                opacity: [0.1, 0.3, 0.1],
                scale: [1, 1.2, 1],
              }}
              transition={{
                duration: 6 + Math.random() * 4,
                repeat: Infinity,
                delay: Math.random() * 5,
                ease: "easeInOut",
              }}
            />
          ))}

          {/* Quadrados rotativos */}
          {[...Array(5)].map((_, i) => (
            <motion.div
              key={`square-${i}`}
              className="absolute w-3 h-3 bg-purple-400/10 rounded-sm"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                rotate: [0, 360],
                y: [0, -40, 0],
                opacity: [0.1, 0.4, 0.1],
              }}
              transition={{
                duration: 8 + Math.random() * 4,
                repeat: Infinity,
                delay: Math.random() * 3,
                ease: "linear",
              }}
            />
          ))}

          {/* Triângulos */}
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={`triangle-${i}`}
              className="absolute w-0 h-0 border-l-2 border-r-2 border-b-4 border-l-transparent border-r-transparent border-b-pink-400/10"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                rotate: [0, 180, 360],
                y: [0, -25, 0],
                opacity: [0.1, 0.3, 0.1],
              }}
              transition={{
                duration: 7 + Math.random() * 3,
                repeat: Infinity,
                delay: Math.random() * 4,
                ease: "easeInOut",
              }}
            />
          ))}
        </>
      )}

      {/* Gradiente animado de fundo */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-br from-blue-50/20 via-purple-50/10 to-pink-50/20 dark:from-blue-900/5 dark:via-purple-900/5 dark:to-pink-900/5"
        animate={{
          background: [
            "linear-gradient(45deg, rgba(59, 130, 246, 0.05), rgba(147, 51, 234, 0.05), rgba(236, 72, 153, 0.05))",
            "linear-gradient(135deg, rgba(147, 51, 234, 0.05), rgba(236, 72, 153, 0.05), rgba(59, 130, 246, 0.05))",
            "linear-gradient(225deg, rgba(236, 72, 153, 0.05), rgba(59, 130, 246, 0.05), rgba(147, 51, 234, 0.05))",
            "linear-gradient(315deg, rgba(59, 130, 246, 0.05), rgba(147, 51, 234, 0.05), rgba(236, 72, 153, 0.05))"
          ]
        }}
        transition={{ 
          duration: 20, 
          repeat: Infinity, 
          ease: "linear" 
        }}
      />

      {/* Ondas de luz */}
      <motion.div
        className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-blue-100/10 to-transparent dark:from-blue-900/10"
        animate={{
          opacity: [0.1, 0.3, 0.1],
          scaleY: [1, 1.2, 1],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      {/* Raios de luz diagonais */}
      <motion.div
        className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-yellow-200/5 to-transparent dark:from-yellow-400/5"
        animate={{
          opacity: [0.1, 0.2, 0.1],
          rotate: [0, 5, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      {/* Efeito de aurora */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-radial from-green-200/5 via-blue-200/5 to-transparent dark:from-green-400/5 dark:via-blue-400/5 rounded-full blur-3xl"
        animate={{
          scale: [1, 1.3, 1],
          opacity: [0.1, 0.2, 0.1],
          x: [0, 50, 0],
          y: [0, -30, 0],
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      {/* Pontos de luz cintilantes */}
      {[...Array(15)].map((_, i) => (
        <motion.div
          key={`sparkle-${i}`}
          className="absolute w-1 h-1 bg-white rounded-full"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            opacity: [0, 1, 0],
            scale: [0, 1, 0],
          }}
          transition={{
            duration: 2 + Math.random() * 2,
            repeat: Infinity,
            delay: Math.random() * 5,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
}
