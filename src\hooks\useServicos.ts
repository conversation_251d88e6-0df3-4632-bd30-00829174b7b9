'use client';

import { useState, useCallback } from 'react';
import { Servico, CriarServicoData, AtualizarServicoData, FiltrosServicos, ServicoApiResponse } from '@/types/servicos';
import { useApiCache } from './useApiCache';

export function useServicos() {
  const [servicos, setServicos] = useState<Servico[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [empresaId, setEmpresaId] = useState<number | null>(null);

  // Buscar serviços
  const buscarServicos = useCallback(async (filtros?: FiltrosServicos) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      
      if (filtros?.ativo !== undefined) {
        params.append('ativo', filtros.ativo.toString());
      }
      if (filtros?.categoria) {
        params.append('categoria', filtros.categoria);
      }
      if (filtros?.busca) {
        params.append('busca', filtros.busca);
      }

      const url = `/api/servicos${params.toString() ? `?${params.toString()}` : ''}`;
      const response = await fetch(url);
      const data: ServicoApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao buscar serviços');
      }

      if (data.success && Array.isArray(data.data)) {
        setServicos(data.data);
        if ('empresa_id' in data) {
          setEmpresaId(data.empresa_id as number);
        }
      } else {
        throw new Error('Formato de resposta inválido');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao buscar serviços:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Criar serviço
  const criarServico = useCallback(async (dadosServico: CriarServicoData): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/servicos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dadosServico),
      });

      const data: ServicoApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao criar serviço');
      }

      if (data.success && data.data && !Array.isArray(data.data)) {
        // Adicionar o novo serviço à lista
        setServicos(prev => [data.data as Servico, ...prev]);
        return true;
      } else {
        throw new Error('Formato de resposta inválido');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao criar serviço:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // Atualizar serviço
  const atualizarServico = useCallback(async (servicoId: number, dadosAtualizacao: AtualizarServicoData): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/servicos/${servicoId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dadosAtualizacao),
      });

      const data: ServicoApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao atualizar serviço');
      }

      if (data.success && data.data && !Array.isArray(data.data)) {
        // Atualizar o serviço na lista
        setServicos(prev => 
          prev.map(servico => 
            servico.servico_id === servicoId ? data.data as Servico : servico
          )
        );
        return true;
      } else {
        throw new Error('Formato de resposta inválido');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao atualizar serviço:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // Excluir serviço
  const excluirServico = useCallback(async (servicoId: number): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/servicos/${servicoId}`, {
        method: 'DELETE',
      });

      const data: ServicoApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao excluir serviço');
      }

      if (data.success) {
        // Remover o serviço da lista
        setServicos(prev => prev.filter(servico => servico.servico_id !== servicoId));
        return true;
      } else {
        throw new Error('Erro ao excluir serviço');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao excluir serviço:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // Buscar serviço específico
  const buscarServico = useCallback(async (servicoId: number): Promise<Servico | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/servicos/${servicoId}`);
      const data: ServicoApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao buscar serviço');
      }

      if (data.success && data.data && !Array.isArray(data.data)) {
        return data.data as Servico;
      } else {
        throw new Error('Serviço não encontrado');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao buscar serviço:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Alternar status ativo/inativo
  const alternarStatusServico = useCallback(async (servicoId: number, ativo: boolean): Promise<boolean> => {
    return await atualizarServico(servicoId, { ativo });
  }, [atualizarServico]);

  // Limpar erro
  const limparError = useCallback(() => {
    setError(null);
  }, []);

  // Estatísticas dos serviços
  const estatisticas = {
    total: servicos.length,
    ativos: servicos.filter(s => s.ativo).length,
    inativos: servicos.filter(s => !s.ativo).length,
    categorias: servicos.reduce((acc, servico) => {
      acc[servico.categoria] = (acc[servico.categoria] || 0) + 1;
      return acc;
    }, {} as { [categoria: string]: number })
  };

  return {
    servicos,
    loading,
    error,
    empresaId,
    estatisticas,
    buscarServicos,
    criarServico,
    atualizarServico,
    excluirServico,
    buscarServico,
    alternarStatusServico,
    limparError
  };
}
