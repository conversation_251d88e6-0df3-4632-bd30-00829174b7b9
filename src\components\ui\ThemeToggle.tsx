'use client';

import React, { useState } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from './Button';

interface ThemeToggleProps {
  variant?: 'button' | 'dropdown';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
}

export function ThemeToggle({ 
  variant = 'button', 
  size = 'md', 
  showLabel = false 
}: ThemeToggleProps) {
  const { theme, setTheme, resolvedTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  // Ícones para cada tema
  const getThemeIcon = (themeName: string) => {
    switch (themeName) {
      case 'light':
        return (
          <svg 
            className="w-4 h-4" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" 
            />
          </svg>
        );
      case 'dark':
        return (
          <svg 
            className="w-4 h-4" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" 
            />
          </svg>
        );
      case 'system':
        return (
          <svg 
            className="w-4 h-4" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" 
            />
          </svg>
        );
      default:
        return null;
    }
  };

  const getThemeLabel = (themeName: string) => {
    switch (themeName) {
      case 'light':
        return 'Claro';
      case 'dark':
        return 'Escuro';
      case 'system':
        return 'Sistema';
      default:
        return '';
    }
  };

  // Toggle simples entre claro e escuro
  if (variant === 'button') {
    const nextTheme = resolvedTheme === 'light' ? 'dark' : 'light';
    
    return (
      <Button
        variant="ghost"
        size={size}
        onClick={() => setTheme(nextTheme)}
        className="relative"
        aria-label={`Mudar para tema ${getThemeLabel(nextTheme)}`}
        title={`Mudar para tema ${getThemeLabel(nextTheme)}`}
      >
        <span className="sr-only">
          Tema atual: {getThemeLabel(resolvedTheme)}. Clique para mudar para {getThemeLabel(nextTheme)}.
        </span>
        {getThemeIcon(resolvedTheme)}
        {showLabel && (
          <span className="ml-2 hidden sm:inline">
            {getThemeLabel(resolvedTheme)}
          </span>
        )}
      </Button>
    );
  }

  // Dropdown com todas as opções
  return (
    <div className="relative">
      <Button
        variant="ghost"
        size={size}
        onClick={() => setIsOpen(!isOpen)}
        className="relative"
        aria-label="Selecionar tema"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        {getThemeIcon(theme)}
        {showLabel && (
          <span className="ml-2 hidden sm:inline">
            {getThemeLabel(theme)}
          </span>
        )}
        <svg 
          className="w-3 h-3 ml-1" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M19 9l-7 7-7-7" 
          />
        </svg>
      </Button>

      {isOpen && (
        <>
          {/* Overlay para fechar o dropdown */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
            aria-hidden="true"
          />
          
          {/* Menu dropdown */}
          <div 
            className="absolute right-0 mt-2 w-48 bg-[var(--surface)] border border-[var(--border-color)] rounded-md shadow-lg z-20"
            role="menu"
            aria-orientation="vertical"
            aria-labelledby="theme-menu"
          >
            {(['light', 'dark', 'system'] as const).map((themeName) => (
              <button
                key={themeName}
                onClick={() => {
                  setTheme(themeName);
                  setIsOpen(false);
                }}
                className={`
                  w-full px-4 py-2 text-left text-sm hover:bg-[var(--surface-hover)] 
                  transition-colors duration-200 flex items-center space-x-2
                  ${theme === themeName ? 'bg-[var(--surface-active)] text-[var(--primary)]' : 'text-[var(--text-primary)]'}
                  first:rounded-t-md last:rounded-b-md
                  focus:outline-none focus:bg-[var(--surface-hover)] focus:ring-2 focus:ring-[var(--primary)] focus:ring-inset
                `}
                role="menuitem"
                aria-current={theme === themeName ? 'true' : 'false'}
              >
                {getThemeIcon(themeName)}
                <span>{getThemeLabel(themeName)}</span>
                {theme === themeName && (
                  <svg 
                    className="w-4 h-4 ml-auto" 
                    fill="currentColor" 
                    viewBox="0 0 20 20"
                    aria-hidden="true"
                  >
                    <path 
                      fillRule="evenodd" 
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
                      clipRule="evenodd" 
                    />
                  </svg>
                )}
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
}
