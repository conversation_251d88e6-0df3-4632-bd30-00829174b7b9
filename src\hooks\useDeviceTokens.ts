import { useState, useEffect } from 'react';
import { DeviceToken } from '@/types/notifications';

interface UseDeviceTokensReturn {
  tokens: DeviceToken[];
  loading: boolean;
  error: string | null;
  registerToken: (token: string, platform: 'web' | 'android' | 'ios') => Promise<boolean>;
  removeToken: (token: string) => Promise<boolean>;
  updateTokenStatus: (token: string, active: boolean) => Promise<boolean>;
  refreshTokens: () => Promise<void>;
}

export function useDeviceTokens(): UseDeviceTokensReturn {
  const [tokens, setTokens] = useState<DeviceToken[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Carregar tokens do usuário
  const loadTokens = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/notifications/device-tokens');
      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Erro ao carregar tokens');
      }

      setTokens(result.data || []);
    } catch (err) {
      console.error('Erro ao carregar tokens:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  };

  // Registrar novo token
  const registerToken = async (token: string, platform: 'web' | 'android' | 'ios'): Promise<boolean> => {
    try {
      setError(null);

      const response = await fetch('/api/notifications/device-tokens', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token, platform })
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Erro ao registrar token');
      }

      // Atualizar lista local
      await loadTokens();
      return true;
    } catch (err) {
      console.error('Erro ao registrar token:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      return false;
    }
  };

  // Remover token
  const removeToken = async (token: string): Promise<boolean> => {
    try {
      setError(null);

      const response = await fetch(`/api/notifications/device-tokens?token=${encodeURIComponent(token)}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Erro ao remover token');
      }

      // Atualizar lista local
      await loadTokens();
      return true;
    } catch (err) {
      console.error('Erro ao remover token:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      return false;
    }
  };

  // Atualizar status do token
  const updateTokenStatus = async (token: string, active: boolean): Promise<boolean> => {
    try {
      setError(null);

      const response = await fetch('/api/notifications/device-tokens', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token, active })
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Erro ao atualizar token');
      }

      // Atualizar lista local
      await loadTokens();
      return true;
    } catch (err) {
      console.error('Erro ao atualizar token:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      return false;
    }
  };

  // Refresh tokens
  const refreshTokens = async () => {
    await loadTokens();
  };

  // Carregar tokens na inicialização
  useEffect(() => {
    loadTokens();
  }, []);

  return {
    tokens,
    loading,
    error,
    registerToken,
    removeToken,
    updateTokenStatus,
    refreshTokens
  };
}

// Hook para verificar se push notifications estão disponíveis
export function usePushNotificationSupport() {
  const [isSupported, setIsSupported] = useState(false);
  const [permission, setPermission] = useState<NotificationPermission>('default');

  useEffect(() => {
    // Verificar se o navegador suporta notificações
    if ('Notification' in window && 'serviceWorker' in navigator) {
      setIsSupported(true);
      setPermission(Notification.permission);
    }
  }, []);

  const requestPermission = async (): Promise<boolean> => {
    if (!isSupported) return false;

    try {
      const permission = await Notification.requestPermission();
      setPermission(permission);
      return permission === 'granted';
    } catch (error) {
      console.error('Erro ao solicitar permissão:', error);
      return false;
    }
  };

  return {
    isSupported,
    permission,
    isGranted: permission === 'granted',
    isDenied: permission === 'denied',
    requestPermission
  };
}

// Hook para gerenciar token FCM
export function useFCMToken() {
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { registerToken } = useDeviceTokens();

  const generateToken = async (): Promise<string | null> => {
    try {
      setLoading(true);
      setError(null);

      // Verificar se Firebase está disponível
      if (typeof window === 'undefined' || !(window as any).firebase) {
        throw new Error('Firebase não está disponível');
      }

      // Aqui seria implementada a lógica do Firebase Messaging
      // Por enquanto, simular um token
      const mockToken = `fcm_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      setToken(mockToken);
      
      // Registrar token no backend
      await registerToken(mockToken, 'web');
      
      return mockToken;
    } catch (err) {
      console.error('Erro ao gerar token FCM:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    token,
    loading,
    error,
    generateToken
  };
}
