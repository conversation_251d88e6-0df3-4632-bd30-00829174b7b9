'use client';

import React from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { ThemeToggle } from '@/components/ui/ThemeToggle';
import OnboardingProgress from './components/OnboardingProgress';

export default function OnboardingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user } = useAuth();

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header simplificado para onboarding */}
      <header className="bg-white border-b border-gray-200 shadow-sm">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <Link
              href="/"
              className="flex items-center space-x-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md"
              aria-label="ServiceTech - Página inicial"
            >
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg" aria-hidden="true">S</span>
              </div>
              <span className="text-xl font-bold text-gray-900">ServiceTech</span>
            </Link>

            {/* Informações do usuário e controles */}
            <div className="flex items-center space-x-4">
              {user && (
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">
                    {user.name || user.email.split('@')[0]}
                  </p>
                  <p className="text-xs text-gray-500">
                    Configurando estabelecimento
                  </p>
                </div>
              )}
              <ThemeToggle variant="button" size="sm" />
            </div>
          </div>
        </div>
      </header>

      {/* Conteúdo principal do onboarding */}
      <main className="flex-1">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-5xl mx-auto">
            <h1 className="text-2xl font-bold text-center text-gray-800 mb-6">
              Onboarding do Estabelecimento
            </h1>
            <OnboardingProgress />
            {children}
          </div>
        </div>
      </main>
    </div>
  );
}