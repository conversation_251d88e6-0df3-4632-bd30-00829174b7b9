'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { FormularioCombo } from '@/components/combos/FormularioCombo';
import { ListaCombos } from '@/components/combos/ListaCombos';
import { useCombos } from '@/hooks/useCombos';
import { ComboCompleto, CriarComboData, FiltrosCombos } from '@/types/combos';

type ModoVisualizacao = 'lista' | 'criar' | 'editar';

export default function CombosPage() {
  const {
    combos,
    loading,
    error,
    estatisticas,
    buscarCombos,
    criarCombo,
    atualizarCombo,
    excluirCombo,
    alternarStatusCombo,
    limparErro
  } = useCombos();

  const [modo, setModo] = useState<ModoVisualizacao>('lista');
  const [comboEditando, setComboEditando] = useState<ComboCompleto | null>(null);

  // Criar novo combo
  const handleCriarCombo = async (dados: CriarComboData): Promise<boolean> => {
    const sucesso = await criarCombo(dados);
    if (sucesso) {
      setModo('lista');
    }
    return sucesso;
  };

  // Editar combo
  const handleEditarCombo = async (dados: CriarComboData): Promise<boolean> => {
    if (!comboEditando) return false;
    
    const sucesso = await atualizarCombo(comboEditando.combo_id, dados);
    if (sucesso) {
      setModo('lista');
      setComboEditando(null);
    }
    return sucesso;
  };

  // Iniciar edição
  const iniciarEdicao = (combo: ComboCompleto) => {
    setComboEditando(combo);
    setModo('editar');
  };

  // Cancelar operação
  const cancelarOperacao = () => {
    setModo('lista');
    setComboEditando(null);
    limparErro();
  };

  // Filtrar combos
  const filtrarCombos = (filtros: FiltrosCombos) => {
    buscarCombos(filtros);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Cabeçalho */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-[var(--text-primary)]">
            Combos de Serviços
          </h1>
          <p className="text-[var(--text-secondary)] mt-2">
            Gerencie combos promocionais para aumentar suas vendas
          </p>
        </div>

        {modo === 'lista' && (
          <Button
            onClick={() => setModo('criar')}
            className="bg-[var(--primary)] hover:bg-[var(--primary-dark)] text-white"
          >
            + Novo Combo
          </Button>
        )}
      </div>

      {/* Exibir erro se houver */}
      {error && (
        <Card className="mb-6 border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex justify-between items-center">
              <div className="text-red-700">
                <strong>Erro:</strong> {error}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={limparErro}
                className="text-red-600 border-red-300"
              >
                Fechar
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Conteúdo principal */}
      {modo === 'lista' && (
        <ListaCombos
          combos={combos}
          estatisticas={estatisticas}
          loading={loading}
          onEditar={iniciarEdicao}
          onExcluir={excluirCombo}
          onAlternarStatus={alternarStatusCombo}
          onFiltrar={filtrarCombos}
        />
      )}

      {modo === 'criar' && (
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={cancelarOperacao}
            >
              ← Voltar
            </Button>
            <h2 className="text-xl font-semibold text-[var(--text-primary)]">
              Criar Novo Combo
            </h2>
          </div>

          <FormularioCombo
            onSalvar={handleCriarCombo}
            onCancelar={cancelarOperacao}
            loading={loading}
          />
        </div>
      )}

      {modo === 'editar' && comboEditando && (
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={cancelarOperacao}
            >
              ← Voltar
            </Button>
            <h2 className="text-xl font-semibold text-[var(--text-primary)]">
              Editar Combo: {comboEditando.nome_combo}
            </h2>
          </div>

          <FormularioCombo
            combo={comboEditando}
            onSalvar={handleEditarCombo}
            onCancelar={cancelarOperacao}
            loading={loading}
          />
        </div>
      )}

      {/* Informações sobre combos */}
      {modo === 'lista' && combos.length === 0 && !loading && (
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Como funcionam os Combos?</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-[var(--text-primary)] mb-2">
                  📦 O que são Combos?
                </h4>
                <p className="text-[var(--text-secondary)] text-sm">
                  Combos são pacotes promocionais que combinam múltiplos serviços 
                  com desconto especial, incentivando clientes a contratar mais serviços.
                </p>
              </div>

              <div>
                <h4 className="font-semibold text-[var(--text-primary)] mb-2">
                  💰 Tipos de Desconto
                </h4>
                <p className="text-[var(--text-secondary)] text-sm">
                  Você pode oferecer descontos em valor fixo (ex: R$ 10,00 off) 
                  ou percentual (ex: 15% de desconto) sobre o valor total dos serviços.
                </p>
              </div>

              <div>
                <h4 className="font-semibold text-[var(--text-primary)] mb-2">
                  🎯 Detecção Automática
                </h4>
                <p className="text-[var(--text-secondary)] text-sm">
                  Quando um cliente seleciona serviços que formam um combo, 
                  o desconto é aplicado automaticamente no agendamento.
                </p>
              </div>

              <div>
                <h4 className="font-semibold text-[var(--text-primary)] mb-2">
                  ⏰ Controle de Validade
                </h4>
                <p className="text-[var(--text-secondary)] text-sm">
                  Configure período de validade e limite de usos para criar 
                  promoções por tempo limitado e gerar urgência.
                </p>
              </div>
            </div>

            <div className="pt-4 border-t border-[var(--border)]">
              <h4 className="font-semibold text-[var(--text-primary)] mb-2">
                💡 Dicas para Combos Eficazes
              </h4>
              <ul className="text-[var(--text-secondary)] text-sm space-y-1">
                <li>• Combine serviços complementares (ex: Corte + Barba)</li>
                <li>• Ofereça descontos atrativos (10-20% é um bom ponto de partida)</li>
                <li>• Use nomes chamativos para seus combos</li>
                <li>• Monitore quais combos são mais populares</li>
                <li>• Crie combos sazonais para datas especiais</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
