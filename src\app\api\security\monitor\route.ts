import { NextRequest, NextResponse } from 'next/server';
import { createSecureApiHandler, withSecurityPreset } from '@/utils/security/apiMiddleware';
import { AuditLogger, getAuditStats } from '@/utils/security/audit';
import { getRateLimitStats } from '@/utils/security/rateLimiting';

/**
 * API para monitoramento de segurança em tempo real
 * Endpoint: GET /api/security/monitor
 */
export const GET = createSecureApiHandler(
  async (request: NextRequest, { user, securityContext }) => {
    try {
      // Log do acesso ao monitoramento
      AuditLogger.adminAction(
        user.id,
        'VIEW_SECURITY_MONITOR',
        'security_monitor',
        undefined,
        true
      );

      const { searchParams } = new URL(request.url);
      const timeRange = searchParams.get('timeRange') || '24h';
      const includeDetails = searchParams.get('includeDetails') === 'true';

      // Coletar estatísticas de auditoria
      const auditStats = getAuditStats();
      
      // Coletar estatísticas de rate limiting
      const rateLimitStats = getRateLimitStats();

      // Calcular métricas de segurança
      const baseMetrics = {
        // Métricas básicas
        totalEvents: auditStats.totalEvents,
        recentFailures: auditStats.recentFailures,
        activeAlerts: auditStats.activeAlerts,

        // Métricas de rate limiting
        rateLimitEntries: rateLimitStats.totalEntries,
        blockedRequests: rateLimitStats.blockedEntries,

        // Métricas por categoria
        eventsByCategory: auditStats.eventsByCategory,
        eventsByRiskLevel: auditStats.eventsByRiskLevel,

        // Métricas de tempo
        timeRange,
        lastUpdated: new Date().toISOString(),

        // Status geral de segurança
        securityStatus: calculateSecurityStatus(auditStats, rateLimitStats),

        // Recomendações
        recommendations: generateSecurityRecommendations(auditStats, rateLimitStats)
      };

      let securityMetrics: Record<string, any> = baseMetrics;

      // Incluir detalhes se solicitado
      if (includeDetails) {
        const { getAuditEvents, getSecurityAlerts } = await import('@/utils/security/audit');

        securityMetrics.recentEvents = getAuditEvents({
          limit: 20,
          startDate: getTimeRangeStart(timeRange)
        });

        securityMetrics.activeAlertsDetails = getSecurityAlerts({
          resolved: false,
          limit: 10
        });
      }

      return NextResponse.json({
        success: true,
        data: securityMetrics,
        processingTime: securityContext.processingTime
      });

    } catch (error: any) {
      console.error('Erro no monitoramento de segurança:', error);
      
      AuditLogger.securityViolation(
        user.id,
        'Error in security monitoring',
        undefined,
        { error: error.message }
      );

      return NextResponse.json(
        { success: false, error: 'Erro interno do servidor' },
        { status: 500 }
      );
    }
  },
  withSecurityPreset('admin', {
    auditAction: 'SECURITY_MONITOR_ACCESS',
    auditResource: 'security_monitor'
  }) as any
);

/**
 * API para configurar alertas de segurança
 * Endpoint: POST /api/security/monitor
 */
export const POST = createSecureApiHandler(
  async (request: NextRequest, { user, securityContext }) => {
    try {
      const { validatedData } = securityContext;
      const { action, config } = validatedData;

      AuditLogger.adminAction(
        user.id,
        'CONFIGURE_SECURITY_MONITOR',
        'security_monitor',
        undefined,
        true
      );

      switch (action) {
        case 'update_thresholds':
          // Atualizar limites de alerta
          const { thresholds } = config;
          
          // Aqui você salvaria as configurações no banco de dados
          // Por enquanto, vamos apenas simular
          
          return NextResponse.json({
            success: true,
            message: 'Limites de alerta atualizados com sucesso',
            data: { thresholds }
          });

        case 'test_alert':
          // Testar sistema de alertas
          const { alertType } = config;
          
          // Simular um alerta de teste
          AuditLogger.securityViolation(
            user.id,
            `Test alert: ${alertType}`,
            undefined,
            { testAlert: true, alertType }
          );
          
          return NextResponse.json({
            success: true,
            message: 'Alerta de teste enviado com sucesso'
          });

        case 'export_logs':
          // Exportar logs de segurança
          const { format, dateRange } = config;
          
          const { getAuditEvents } = await import('@/utils/security/audit');
          const events = getAuditEvents({
            startDate: dateRange.start,
            endDate: dateRange.end,
            limit: 10000
          });
          
          AuditLogger.adminAction(
            user.id,
            'EXPORT_SECURITY_LOGS',
            'security_logs',
            undefined,
            true
          );
          
          return NextResponse.json({
            success: true,
            data: {
              events,
              format,
              exportedAt: new Date().toISOString(),
              totalEvents: events.length
            }
          });

        default:
          return NextResponse.json(
            { success: false, error: 'Ação não reconhecida' },
            { status: 400 }
          );
      }

    } catch (error: any) {
      console.error('Erro na configuração de segurança:', error);
      
      return NextResponse.json(
        { success: false, error: 'Erro interno do servidor' },
        { status: 500 }
      );
    }
  },
  withSecurityPreset('admin', {
    validation: {
      schema: {
        action: {
          required: true,
          pattern: /^(update_thresholds|test_alert|export_logs)$/
        },
        config: {
          required: true,
          custom: (value: any) => typeof value === 'object' || 'Config deve ser um objeto'
        }
      }
    },
    auditAction: 'CONFIGURE_SECURITY_MONITOR',
    auditResource: 'security_monitor'
  }) as any
);

/**
 * Calcula o status geral de segurança
 */
function calculateSecurityStatus(auditStats: any, rateLimitStats: any): {
  level: 'GOOD' | 'WARNING' | 'CRITICAL';
  score: number;
  issues: string[];
} {
  const issues: string[] = [];
  let score = 100;

  // Verificar falhas recentes
  if (auditStats.recentFailures > 50) {
    issues.push('Alto número de falhas de autenticação nas últimas 24h');
    score -= 20;
  } else if (auditStats.recentFailures > 20) {
    issues.push('Número moderado de falhas de autenticação');
    score -= 10;
  }

  // Verificar alertas ativos
  if (auditStats.activeAlerts > 10) {
    issues.push('Muitos alertas de segurança ativos');
    score -= 25;
  } else if (auditStats.activeAlerts > 5) {
    issues.push('Alguns alertas de segurança ativos');
    score -= 15;
  }

  // Verificar eventos críticos
  const criticalEvents = auditStats.eventsByRiskLevel.CRITICAL || 0;
  if (criticalEvents > 5) {
    issues.push('Eventos críticos de segurança detectados');
    score -= 30;
  }

  // Verificar rate limiting
  if (rateLimitStats.blockedEntries > 100) {
    issues.push('Alto número de requisições bloqueadas por rate limiting');
    score -= 15;
  }

  // Determinar nível
  let level: 'GOOD' | 'WARNING' | 'CRITICAL';
  if (score >= 80) {
    level = 'GOOD';
  } else if (score >= 60) {
    level = 'WARNING';
  } else {
    level = 'CRITICAL';
  }

  return { level, score, issues };
}

/**
 * Gera recomendações de segurança
 */
function generateSecurityRecommendations(auditStats: any, rateLimitStats: any): string[] {
  const recommendations: string[] = [];

  if (auditStats.recentFailures > 20) {
    recommendations.push('Considere implementar CAPTCHA após múltiplas tentativas de login');
    recommendations.push('Revise as políticas de senha para aumentar a segurança');
  }

  if (auditStats.activeAlerts > 5) {
    recommendations.push('Revise e resolva os alertas de segurança pendentes');
  }

  if (rateLimitStats.blockedEntries > 50) {
    recommendations.push('Analise os padrões de tráfego para identificar possíveis ataques');
  }

  const criticalEvents = auditStats.eventsByRiskLevel.CRITICAL || 0;
  if (criticalEvents > 0) {
    recommendations.push('Investigue imediatamente os eventos críticos de segurança');
  }

  if (recommendations.length === 0) {
    recommendations.push('Sistema de segurança funcionando adequadamente');
    recommendations.push('Continue monitorando regularmente');
  }

  return recommendations;
}

/**
 * Calcula o início do período baseado no timeRange
 */
function getTimeRangeStart(timeRange: string): string {
  const now = new Date();
  let hoursBack = 24; // padrão 24h

  switch (timeRange) {
    case '1h':
      hoursBack = 1;
      break;
    case '6h':
      hoursBack = 6;
      break;
    case '12h':
      hoursBack = 12;
      break;
    case '24h':
      hoursBack = 24;
      break;
    case '7d':
      hoursBack = 24 * 7;
      break;
    case '30d':
      hoursBack = 24 * 30;
      break;
  }

  return new Date(now.getTime() - hoursBack * 60 * 60 * 1000).toISOString();
}
