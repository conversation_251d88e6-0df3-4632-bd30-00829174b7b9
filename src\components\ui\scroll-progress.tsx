"use client";

import { cn } from "@/lib/utils";
import { motion, useScroll, useSpring } from "motion/react";
import React from "react";

interface ScrollProgressProps {
  className?: string;
  springOptions?: {
    stiffness?: number;
    damping?: number;
    restDelta?: number;
  };
}

export function ScrollProgress({ 
  className,
  springOptions = { stiffness: 400, damping: 90, restDelta: 0.001 }
}: ScrollProgressProps) {
  const { scrollYProgress } = useScroll();
  const scaleX = useSpring(scrollYProgress, springOptions);

  return (
    <motion.div
      className={cn(
        "fixed inset-x-0 top-0 z-[1000] h-1 origin-left bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500",
        className,
      )}
      style={{
        scaleX,
      }}
    />
  );
}
