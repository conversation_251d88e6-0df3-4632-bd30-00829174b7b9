import { NextResponse } from 'next/server';
import { supabaseAdmin } from '@/utils/supabase/admin';

// Simular processamento de pagamento sem verificação de assinatura
export async function POST(request: Request) {
  console.log('🧪 Simulando webhook do Stripe (sem verificação de assinatura)');

  try {
    const body = await request.json();
    const { user_id, plano, cnpj, estabelecimento } = body;

    if (!user_id || !plano || !cnpj || !estabelecimento) {
      return NextResponse.json({
        error: 'Dados obrigatórios: user_id, plano, cnpj, estabelecimento'
      }, { status: 400 });
    }

    console.log('📝 Dados recebidos:', { user_id, plano, cnpj, estabelecimento });

    // Usar a classe administrativa para processar webhook
    const empresaId = await supabaseAdmin.processWebhookPayment({
      userId: user_id,
      plano,
      cnpj,
      estabelecimento
    });



    return NextResponse.json({
      success: true,
      message: 'Pagamento processado com sucesso!',
      data: {
        user_id,
        empresa_id: empresaId,
        plano,
        status: 'ativo'
      }
    });

  } catch (error: any) {
    console.error('❌ Erro geral:', error);
    return NextResponse.json({
      error: `Erro interno: ${error.message}`
    }, { status: 500 });
  }
}
