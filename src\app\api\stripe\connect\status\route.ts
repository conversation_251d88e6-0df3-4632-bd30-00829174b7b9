import { NextRequest, NextResponse } from 'next/server';
import { createClient, createAdminClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Verificar se é proprietário
    if (user.user_metadata?.role !== 'Proprietario') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado. Apenas proprietários podem acessar configurações de pagamento.' },
        { status: 403 }
      );
    }

    // Buscar dados da empresa do proprietário (usando admin client para evitar RLS)
    const supabaseAdmin = createAdminClient();
    const { data: empresa, error: empresaError } = await supabaseAdmin
      .from('empresas')
      .select(`
        empresa_id,
        nome_empresa,
        status
      `)
      .eq('proprietario_user_id', user.id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada' },
        { status: 404 }
      );
    }

    // Como não temos integração Stripe implementada ainda, retornar status padrão
    return NextResponse.json({
      success: true,
      data: {
        connected: false,
        status: {
          status: 'not_connected',
          mensagem: 'Integração Stripe não configurada',
          cor: 'gray'
        },
        empresa: {
          nome: empresa.nome_empresa,
          pagamentos_habilitados: false,
          percentual_comissao: 5.0
        },
        pode_receber_pagamentos: false,
        requer_configuracao: true
      }
    });



  } catch (error: any) {
    console.error('Erro ao verificar status do Stripe Connect:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Verificar se é proprietário
    if (user.user_metadata?.role !== 'Proprietario') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado' },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Funcionalidade de pagamentos não implementada ainda' },
      { status: 501 }
    );

  } catch (error: any) {
    console.error('Erro ao atualizar configuração de pagamentos:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
