'use client';

import React, { Suspense } from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import SecurityDashboard from '@/components/security/SecurityDashboard';

function SecurityPageContent() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <SecurityDashboard />
      </div>
    </div>
  );
}

export default function SecurityPage() {
  return (
    <ProtectedRoute requiredRole="Administrador" fallbackUrl="/acesso-negado">
      <Suspense fallback={
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2">Carregando dashboard de segurança...</span>
        </div>
      }>
        <SecurityPageContent />
      </Suspense>
    </ProtectedRoute>
  );
}
