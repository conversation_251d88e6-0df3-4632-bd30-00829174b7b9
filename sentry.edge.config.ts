/**
 * Configuração do Sentry para Edge Runtime
 */

import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  
  // Configurações de ambiente
  environment: process.env.NODE_ENV ?? 'development',
  release: process.env.NEXT_PUBLIC_APP_VERSION ?? '1.0.0',
  
  // Configurações de performance (reduzidas para edge)
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.05 : 0.5,
  
  // Configurações de debug
  debug: process.env.NODE_ENV === 'development',
  
  // Filtrar dados sensíveis
  beforeSend(event, hint) {
    // Lista de campos sensíveis para filtrar
    const sensitiveFields = [
      'password', 'token', 'secret', 'key', 'authorization',
      'cookie', 'session', 'credit_card', 'ssn', 'cpf', 'cnpj'
    ];

    // Função para filtrar dados sensíveis
    const filterSensitiveData = (obj: any): any => {
      if (!obj || typeof obj !== 'object') return obj;

      const filtered = Array.isArray(obj) ? [] : {};
      
      for (const [key, value] of Object.entries(obj)) {
        const lowerKey = key.toLowerCase();
        const isSensitive = sensitiveFields.some(field => lowerKey.includes(field));
        
        if (isSensitive) {
          (filtered as any)[key] = '[FILTERED]';
        } else if (typeof value === 'object' && value !== null) {
          (filtered as any)[key] = filterSensitiveData(value);
        } else {
          (filtered as any)[key] = value;
        }
      }
      
      return filtered;
    };

    // Filtrar dados do evento
    if (event.request?.data) {
      event.request.data = filterSensitiveData(event.request.data);
    }
    
    if (event.extra) {
      event.extra = filterSensitiveData(event.extra);
    }

    return event;
  },

  // Configurações de contexto inicial
  initialScope: {
    tags: {
      component: 'edge',
      platform: 'edge-runtime'
    }
  },

  // Configurações de erro
  ignoreErrors: [
    // Erros comuns do edge runtime
    'Network request failed',
    'Fetch failed',
    'AbortError',
  ],

  // Configurações de breadcrumbs (reduzidas para edge)
  maxBreadcrumbs: 25,

  // Configurações de contexto
  attachStacktrace: true,
  sendDefaultPii: false,
});
