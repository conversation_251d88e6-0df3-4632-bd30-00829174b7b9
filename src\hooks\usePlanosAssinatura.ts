import { useState, useEffect, useCallback } from 'react';
import { 
  TemplatePlanoAssinatura, 
  PlanoAssinaturaExpandido,
  EstatisticasAssinaturas,
  CriarPlanoAssinaturaData,
  AtualizarPlanoAssinaturaData,
  UsePlanosAssinaturaResult 
} from '@/types/planosAssinatura';

export function usePlanosAssinatura(): UsePlanosAssinaturaResult {
  const [planos, setPlanos] = useState<TemplatePlanoAssinatura[]>([]);
  const [assinantes, setAssinantes] = useState<PlanoAssinaturaExpandido[]>([]);
  const [estatisticas, setEstatisticas] = useState<EstatisticasAssinaturas | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Buscar planos de assinatura da empresa
  const buscarPlanos = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/planos-assinatura');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao buscar planos');
      }

      setPlanos(data.data || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao buscar planos:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Buscar assinantes dos planos
  const buscarAssinantes = useCallback(async () => {
    try {
      const response = await fetch('/api/planos-assinatura/assinantes');
      const data = await response.json();

      if (response.ok) {
        setAssinantes(data.data || []);
      }
    } catch (err) {
      console.error('Erro ao buscar assinantes:', err);
    }
  }, []);

  // Criar novo plano de assinatura
  const criarPlano = useCallback(async (dadosPlano: CriarPlanoAssinaturaData): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/planos-assinatura', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dadosPlano),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao criar plano');
      }

      // Atualizar lista de planos
      await buscarPlanos();
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao criar plano:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [buscarPlanos]);

  // Atualizar plano existente
  const atualizarPlano = useCallback(async (
    id: number, 
    dadosAtualizacao: AtualizarPlanoAssinaturaData
  ): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/planos-assinatura/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dadosAtualizacao),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao atualizar plano');
      }

      // Atualizar lista de planos
      await buscarPlanos();
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao atualizar plano:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [buscarPlanos]);

  // Excluir plano
  const excluirPlano = useCallback(async (id: number): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/planos-assinatura/${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao excluir plano');
      }

      // Atualizar lista de planos
      await buscarPlanos();
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao excluir plano:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [buscarPlanos]);

  // Obter estatísticas de assinaturas
  const obterEstatisticas = useCallback(async () => {
    try {
      const response = await fetch('/api/planos-assinatura/estatisticas');
      const data = await response.json();

      if (response.ok) {
        setEstatisticas(data.data);
      }
    } catch (err) {
      console.error('Erro ao obter estatísticas:', err);
    }
  }, []);

  // Refresh geral
  const refresh = useCallback(() => {
    buscarPlanos();
    buscarAssinantes();
    obterEstatisticas();
  }, [buscarPlanos, buscarAssinantes, obterEstatisticas]);

  // Carregar dados iniciais
  useEffect(() => {
    refresh();
  }, [refresh]);

  return {
    planos,
    assinantes,
    estatisticas,
    loading,
    error,
    criarPlano,
    atualizarPlano,
    excluirPlano,
    obterEstatisticas,
    refresh,
  };
}

// Hook específico para um plano individual
export function usePlanoAssinatura(planoId: number) {
  const [plano, setPlano] = useState<TemplatePlanoAssinatura | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const buscarPlano = useCallback(async () => {
    if (!planoId) return;

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/planos-assinatura/${planoId}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao buscar plano');
      }

      setPlano(data.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao buscar plano:', err);
    } finally {
      setLoading(false);
    }
  }, [planoId]);

  useEffect(() => {
    buscarPlano();
  }, [buscarPlano]);

  return {
    plano,
    loading,
    error,
    refresh: buscarPlano,
  };
}

// Hook para validação de dados de plano
export function useValidacaoPlano() {
  const validarPlano = useCallback((dados: CriarPlanoAssinaturaData) => {
    const erros: string[] = [];

    // Validar nome do plano
    if (!dados.nome_plano_empresa || dados.nome_plano_empresa.trim().length < 3) {
      erros.push('Nome do plano deve ter pelo menos 3 caracteres');
    }

    if (dados.nome_plano_empresa && dados.nome_plano_empresa.length > 255) {
      erros.push('Nome do plano deve ter no máximo 255 caracteres');
    }

    // Validar preço
    if (!dados.preco_mensal_assinatura || dados.preco_mensal_assinatura <= 0) {
      erros.push('Preço deve ser maior que zero');
    }

    if (dados.preco_mensal_assinatura > 9999.99) {
      erros.push('Preço deve ser no máximo R$ 9.999,99');
    }

    // Validar limite de usos
    if (dados.limite_usos_mes !== undefined && dados.limite_usos_mes !== null) {
      if (dados.limite_usos_mes < 1) {
        erros.push('Limite de usos deve ser pelo menos 1');
      }

      if (dados.limite_usos_mes > 999) {
        erros.push('Limite de usos deve ser no máximo 999');
      }
    }

    // Validar serviço
    if (!dados.servico_id || dados.servico_id <= 0) {
      erros.push('Serviço é obrigatório');
    }

    // Validar descrição
    if (dados.descricao_plano && dados.descricao_plano.length > 1000) {
      erros.push('Descrição deve ter no máximo 1000 caracteres');
    }

    return {
      valido: erros.length === 0,
      erros,
    };
  }, []);

  return { validarPlano };
}
