import { useState, useCallback, useEffect } from 'react';

interface StatusConta {
  status: 'not_connected' | 'pending' | 'active' | 'restricted';
  mensagem: string;
  cor: 'gray' | 'yellow' | 'green' | 'red';
}

interface DadosEmpresa {
  nome: string;
  pagamentos_habilitados: boolean;
  percentual_comissao: number;
}

interface StatusStripeConnect {
  connected: boolean;
  account_id?: string;
  status: StatusConta;
  charges_enabled?: boolean;
  payouts_enabled?: boolean;
  details_submitted?: boolean;
  requirements?: {
    currently_due: string[];
    eventually_due: string[];
    past_due: string[];
    pending_verification: string[];
  };
  capabilities?: {
    card_payments: string;
    transfers: string;
  };
  empresa: DadosEmpresa;
  pode_receber_pagamentos: boolean;
  requer_configuracao: boolean;
  dashboard_url?: string;
  erro_verificacao?: boolean;
}

interface EstadoStripeConnect {
  loading: boolean;
  error: string | null;
  status: StatusStripeConnect | null;
  conectando: boolean;
  desconectando: boolean;
  atualizandoConfig: boolean;
}

export function useStripeConnect() {
  const [estado, setEstado] = useState<EstadoStripeConnect>({
    loading: false,
    error: null,
    status: null,
    conectando: false,
    desconectando: false,
    atualizandoConfig: false
  });

  // Buscar status atual da conta Stripe
  const buscarStatus = useCallback(async (): Promise<StatusStripeConnect | null> => {
    setEstado(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch('/api/stripe/connect/status');
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao buscar status');
      }

      const statusData = result.data;
      setEstado(prev => ({
        ...prev,
        status: statusData,
        loading: false
      }));

      return statusData;

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        error: error.message,
        loading: false
      }));
      return null;
    }
  }, []);

  // Iniciar processo de conexão com Stripe
  const conectarStripe = useCallback(async (): Promise<string | null> => {
    setEstado(prev => ({ ...prev, conectando: true, error: null }));

    try {
      const baseUrl = window.location.origin;
      const returnUrl = `${baseUrl}/proprietario/configuracoes?stripe_return=success`;
      const refreshUrl = `${baseUrl}/proprietario/configuracoes?stripe_return=refresh`;

      const response = await fetch('/api/stripe/connect/oauth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          return_url: returnUrl,
          refresh_url: refreshUrl
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao conectar com Stripe');
      }

      setEstado(prev => ({
        ...prev,
        conectando: false
      }));

      return result.data.onboarding_url;

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        error: error.message,
        conectando: false
      }));
      return null;
    }
  }, []);

  // Desconectar conta Stripe
  const desconectarStripe = useCallback(async (): Promise<boolean> => {
    setEstado(prev => ({ ...prev, desconectando: true, error: null }));

    try {
      const response = await fetch('/api/stripe/connect/disconnect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao desconectar');
      }

      // Atualizar status local
      setEstado(prev => ({
        ...prev,
        status: prev.status ? {
          ...prev.status,
          connected: false,
          status: {
            status: 'not_connected',
            mensagem: 'Conta não conectada',
            cor: 'gray'
          },
          pode_receber_pagamentos: false,
          requer_configuracao: true,
          empresa: {
            ...prev.status.empresa,
            pagamentos_habilitados: false
          }
        } : null,
        desconectando: false
      }));

      return true;

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        error: error.message,
        desconectando: false
      }));
      return false;
    }
  }, []);

  // Verificar se pode desconectar
  const verificarPodeDesconectar = useCallback(async (): Promise<{
    pode_desconectar: boolean;
    motivo: string;
    agendamentos_pendentes?: number;
  } | null> => {
    try {
      const response = await fetch('/api/stripe/connect/disconnect');
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao verificar');
      }

      return result.data;

    } catch (error: any) {
      console.error('Erro ao verificar possibilidade de desconexão:', error);
      return null;
    }
  }, []);

  // Atualizar configuração de pagamentos online
  const atualizarConfigPagamentos = useCallback(async (habilitado: boolean): Promise<boolean> => {
    setEstado(prev => ({ ...prev, atualizandoConfig: true, error: null }));

    try {
      const response = await fetch('/api/stripe/connect/status', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pagamentos_online_habilitados: habilitado
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao atualizar configuração');
      }

      // Atualizar status local
      setEstado(prev => ({
        ...prev,
        status: prev.status ? {
          ...prev.status,
          empresa: {
            ...prev.status.empresa,
            pagamentos_habilitados: habilitado
          }
        } : null,
        atualizandoConfig: false
      }));

      return true;

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        error: error.message,
        atualizandoConfig: false
      }));
      return false;
    }
  }, []);

  // Limpar erro
  const limparErro = useCallback(() => {
    setEstado(prev => ({ ...prev, error: null }));
  }, []);

  // Verificar se está configurado corretamente
  const estaConfigurado = useCallback((): boolean => {
    return !!(estado.status?.connected && 
              estado.status?.pode_receber_pagamentos && 
              estado.status?.empresa.pagamentos_habilitados);
  }, [estado.status]);

  // Obter próximos passos
  const obterProximosPassos = useCallback((): string[] => {
    if (!estado.status) return [];

    const passos: string[] = [];

    if (!estado.status.connected) {
      passos.push('Conectar conta Stripe');
    } else if (estado.status.requer_configuracao) {
      passos.push('Completar configuração no Stripe');
    } else if (!estado.status.pode_receber_pagamentos) {
      if (estado.status.requirements?.currently_due.length) {
        passos.push('Enviar documentação pendente');
      }
      if (estado.status.requirements?.past_due.length) {
        passos.push('Resolver documentação em atraso');
      }
    } else if (!estado.status.empresa.pagamentos_habilitados) {
      passos.push('Habilitar pagamentos online na plataforma');
    }

    return passos;
  }, [estado.status]);

  // Carregar status inicial
  useEffect(() => {
    buscarStatus();
  }, [buscarStatus]);

  return {
    // Estado
    loading: estado.loading,
    error: estado.error,
    status: estado.status,
    conectando: estado.conectando,
    desconectando: estado.desconectando,
    atualizandoConfig: estado.atualizandoConfig,

    // Ações
    buscarStatus,
    conectarStripe,
    desconectarStripe,
    verificarPodeDesconectar,
    atualizarConfigPagamentos,
    limparErro,

    // Utilitários
    estaConfigurado,
    obterProximosPassos
  };
}
