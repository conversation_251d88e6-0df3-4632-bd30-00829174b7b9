import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/utils/supabase/server';

// GET - Buscar dados públicos da empresa
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = createAdminClient();
    const resolvedParams = await params;
    const identificador = resolvedParams.id;

    console.log('🔍 API: Recebida requisição para empresa:', identificador);
    console.log('🔍 API: URL da requisição:', request.url);
    console.log('🔍 API: Headers da requisição:', Object.fromEntries(request.headers.entries()));

    // Validar se o identificador foi fornecido
    if (!identificador) {
      console.log('❌ API: Identificador não fornecido');
      return NextResponse.json(
        { success: false, error: 'Identificador da empresa é obrigatório' },
        { status: 400 }
      );
    }

    // Determinar se é ID numérico ou slug
    const isNumericId = !isNaN(Number(identificador));
    console.log('🔍 API: Tipo de identificador - isNumericId:', isNumericId);

    // Construir query baseada no tipo de identificador
    let query = supabase
      .from('empresas')
      .select(`
        empresa_id,
        nome_empresa,
        cnpj,
        telefone,
        endereco,
        numero,
        complemento,
        bairro,
        cidade,
        estado,
        cep,
        descricao,
        logo_url,
        fotos_portfolio_urls,
        horario_funcionamento,
        segmento,
        slug,
        status
      `)
      .eq('status', 'ativo');

    // Aplicar filtro baseado no tipo de identificador
    if (isNumericId) {
      console.log('🔍 API: Buscando por ID numérico:', Number(identificador));
      query = query.eq('empresa_id', Number(identificador));
    } else {
      console.log('🔍 API: Buscando por slug:', identificador);
      query = query.eq('slug', identificador);
    }

    // Buscar dados públicos da empresa
    console.log('🔍 API: Executando query no Supabase...');
    const { data: empresa, error: empresaError } = await query.single();

    console.log('📊 API: Resultado da query empresa:');
    console.log('   - empresa:', empresa ? 'encontrada' : 'null');
    console.log('   - empresaError:', empresaError);

    if (empresaError) {
      console.error('❌ API: Erro ao buscar empresa:', empresaError);
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada' },
        { status: 404 }
      );
    }

    if (!empresa) {
      console.error('❌ API: Empresa não encontrada (data é null)');
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada ou inativa' },
        { status: 404 }
      );
    }

    console.log('✅ API: Empresa encontrada:', empresa.nome_empresa);

    // Buscar serviços ativos da empresa
    const { data: servicos, error: servicosError } = await supabase
      .from('servicos')
      .select(`
        servico_id,
        nome_servico,
        descricao,
        duracao_minutos,
        preco,
        categoria
      `)
      .eq('empresa_id', empresa.empresa_id)
      .eq('ativo', true)
      .order('categoria', { ascending: true })
      .order('nome_servico', { ascending: true });

    if (servicosError) {
      console.error('Erro ao buscar serviços:', servicosError);
      return NextResponse.json(
        { success: false, error: 'Erro ao buscar serviços da empresa' },
        { status: 500 }
      );
    }

    // Buscar colaboradores ativos da empresa (dados públicos)
    const { data: colaboradores, error: colaboradoresError } = await supabase
      .from('colaboradores_empresa')
      .select(`
        colaborador_user_id,
        ativo_como_prestador,
        auth_users:colaborador_user_id (
          raw_user_meta_data
        )
      `)
      .eq('empresa_id', empresa.empresa_id)
      .eq('ativo', true)
      .eq('ativo_como_prestador', true);

    if (colaboradoresError) {
      console.error('Erro ao buscar colaboradores:', colaboradoresError);
      // Não retorna erro, apenas colaboradores vazios
    }

    // Processar dados dos colaboradores
    const colaboradoresProcessados = colaboradores?.map(col => {
      const authUser = Array.isArray(col.auth_users) ? col.auth_users[0] : col.auth_users;
      return {
        colaborador_user_id: col.colaborador_user_id,
        name: authUser?.raw_user_meta_data?.name || 'Colaborador',
        ativo_como_prestador: col.ativo_como_prestador
      };
    }) || [];

    // Agrupar serviços por categoria
    const servicosPorCategoria = servicos?.reduce((acc, servico) => {
      const categoria = servico.categoria || 'Outros';
      if (!acc[categoria]) {
        acc[categoria] = [];
      }
      acc[categoria].push(servico);
      return acc;
    }, {} as Record<string, typeof servicos>) || {};

    // Montar resposta com dados públicos no formato esperado pela página
    const dadosPublicos = {
      empresa: {
        empresa_id: empresa.empresa_id,
        nome_empresa: empresa.nome_empresa,
        telefone: empresa.telefone,
        endereco: empresa.endereco,
        numero: empresa.numero,
        complemento: empresa.complemento,
        bairro: empresa.bairro,
        cidade: empresa.cidade,
        estado: empresa.estado,
        cep: empresa.cep,
        descricao: empresa.descricao,
        logo_url: empresa.logo_url,
        imagem_capa_url: null, // Campo temporariamente removido até migração ser aplicada
        fotos_portfolio_urls: empresa.fotos_portfolio_urls,
        horario_funcionamento: empresa.horario_funcionamento,
        segmento: empresa.segmento,
        slug: empresa.slug,
        // Campos do Stripe Connect (temporariamente removidos até migração)
        pagamentos_online_habilitados: false,
        stripe_charges_enabled: false
      },
      servicos: servicos || [],
      servicos_por_categoria: servicosPorCategoria,
      colaboradores: colaboradoresProcessados,
      estatisticas: {
        total_servicos: servicos?.length || 0,
        total_colaboradores: colaboradoresProcessados.length,
        categorias_servicos: Object.keys(servicosPorCategoria).length
      }
    };

    console.log('✅ API: Retornando dados com sucesso');
    console.log('📊 API: Dados incluem:', Object.keys(dadosPublicos).join(', '));

    return NextResponse.json({
      success: true,
      data: dadosPublicos
    });

  } catch (error: any) {
    console.error('Erro geral na API de empresa:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
