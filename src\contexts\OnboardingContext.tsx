'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useAuth } from './AuthContext';

export type OnboardingStep = {
  id: string;
  title: string;
  description: string;
  target?: string; // Seletor CSS do elemento alvo
  position?: 'top' | 'bottom' | 'left' | 'right';
  action?: () => void;
  condition?: () => boolean; // Condição para mostrar o step
};

export type OnboardingFlow = {
  id: string;
  name: string;
  description: string;
  role: string; // Papel do usuário para este fluxo
  steps: OnboardingStep[];
};

interface OnboardingContextType {
  isActive: boolean;
  currentFlow: OnboardingFlow | null;
  currentStepIndex: number;
  currentStep: OnboardingStep | null;
  startOnboarding: (flowId: string) => void;
  nextStep: () => void;
  previousStep: () => void;
  skipStep: () => void;
  finishOnboarding: () => void;
  restartOnboarding: () => void;
  isStepCompleted: (stepId: string) => boolean;
  markStepCompleted: (stepId: string) => void;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

// Fluxos de onboarding por papel
const ONBOARDING_FLOWS: OnboardingFlow[] = [
  {
    id: 'proprietario-inicial',
    name: 'Primeiros Passos - Proprietário',
    description: 'Configure sua empresa e comece a receber agendamentos',
    role: 'Proprietário',
    steps: [
      {
        id: 'welcome',
        title: 'Bem-vindo ao ServiceTech!',
        description: 'Vamos configurar sua empresa em alguns passos simples.',
      },
      {
        id: 'empresa-info',
        title: 'Informações da Empresa',
        description: 'Complete as informações básicas da sua empresa.',
        target: '[data-onboarding="empresa-form"]',
        position: 'bottom',
      },
      {
        id: 'servicos',
        title: 'Cadastre seus Serviços',
        description: 'Adicione os serviços que sua empresa oferece.',
        target: '[data-onboarding="servicos-section"]',
        position: 'top',
      },
      {
        id: 'horarios',
        title: 'Configure os Horários',
        description: 'Defina os horários de funcionamento.',
        target: '[data-onboarding="horarios-section"]',
        position: 'top',
      },
      {
        id: 'colaboradores',
        title: 'Convide Colaboradores',
        description: 'Adicione colaboradores à sua equipe.',
        target: '[data-onboarding="colaboradores-section"]',
        position: 'top',
      },
      {
        id: 'pagamentos',
        title: 'Configure Pagamentos',
        description: 'Configure as formas de pagamento aceitas.',
        target: '[data-onboarding="pagamentos-section"]',
        position: 'top',
      },
      {
        id: 'complete',
        title: 'Configuração Concluída!',
        description: 'Sua empresa está pronta para receber agendamentos.',
      },
    ],
  },
  {
    id: 'colaborador-inicial',
    name: 'Primeiros Passos - Colaborador',
    description: 'Aprenda a usar o sistema como colaborador',
    role: 'Colaborador',
    steps: [
      {
        id: 'welcome',
        title: 'Bem-vindo à Equipe!',
        description: 'Vamos te mostrar como usar o sistema.',
      },
      {
        id: 'dashboard',
        title: 'Seu Dashboard',
        description: 'Aqui você vê seus agendamentos e estatísticas.',
        target: '[data-onboarding="dashboard"]',
        position: 'bottom',
      },
      {
        id: 'agendamentos',
        title: 'Gerenciar Agendamentos',
        description: 'Veja e gerencie seus agendamentos aqui.',
        target: '[data-onboarding="agendamentos-list"]',
        position: 'top',
      },
      {
        id: 'perfil',
        title: 'Seu Perfil',
        description: 'Configure seus horários e informações pessoais.',
        target: '[data-onboarding="perfil-section"]',
        position: 'left',
      },
      {
        id: 'complete',
        title: 'Tudo Pronto!',
        description: 'Você está pronto para começar a trabalhar.',
      },
    ],
  },
  {
    id: 'cliente-inicial',
    name: 'Primeiros Passos - Cliente',
    description: 'Aprenda a agendar serviços',
    role: 'Cliente',
    steps: [
      {
        id: 'welcome',
        title: 'Bem-vindo ao ServiceTech!',
        description: 'Descubra como agendar seus serviços favoritos.',
      },
      {
        id: 'buscar',
        title: 'Buscar Estabelecimentos',
        description: 'Use a busca para encontrar estabelecimentos próximos.',
        target: '[data-onboarding="search-bar"]',
        position: 'bottom',
      },
      {
        id: 'agendar',
        title: 'Fazer Agendamento',
        description: 'Clique em um estabelecimento para ver serviços e agendar.',
        target: '[data-onboarding="empresa-card"]',
        position: 'top',
      },
      {
        id: 'meus-agendamentos',
        title: 'Seus Agendamentos',
        description: 'Acompanhe seus agendamentos aqui.',
        target: '[data-onboarding="meus-agendamentos"]',
        position: 'left',
      },
      {
        id: 'complete',
        title: 'Pronto para Agendar!',
        description: 'Agora você sabe como usar o ServiceTech.',
      },
    ],
  },
];

export function OnboardingProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const [isActive, setIsActive] = useState(false);
  const [currentFlow, setCurrentFlow] = useState<OnboardingFlow | null>(null);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<string[]>([]);

  // Carregar estado do localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('onboarding-state');
      if (saved) {
        try {
          const state = JSON.parse(saved);
          setCompletedSteps(state.completedSteps || []);
        } catch (error) {
          console.error('Erro ao carregar estado do onboarding:', error);
        }
      }
    }
  }, []);

  // Salvar estado no localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const state = {
        completedSteps,
      };
      localStorage.setItem('onboarding-state', JSON.stringify(state));
    }
  }, [completedSteps]);

  // Verificar se deve iniciar onboarding automaticamente
  useEffect(() => {
    if (user && !isActive) {
      const userRole = user.role;
      const hasCompletedOnboarding = completedSteps.includes(`${userRole}-onboarding-complete`);
      
      if (!hasCompletedOnboarding) {
        // Iniciar onboarding baseado no papel do usuário
        const flowId = getFlowIdForRole(userRole);
        if (flowId) {
          setTimeout(() => startOnboarding(flowId), 1000); // Delay para garantir que a página carregou
        }
      }
    }
  }, [user, completedSteps]);

  const getFlowIdForRole = (role: string): string | null => {
    switch (role) {
      case 'Proprietário':
        return 'proprietario-inicial';
      case 'Colaborador':
        return 'colaborador-inicial';
      case 'Cliente':
        return 'cliente-inicial';
      default:
        return null;
    }
  };

  const startOnboarding = (flowId: string) => {
    const flow = ONBOARDING_FLOWS.find(f => f.id === flowId);
    if (flow) {
      setCurrentFlow(flow);
      setCurrentStepIndex(0);
      setIsActive(true);
    }
  };

  const nextStep = () => {
    if (currentFlow && currentStepIndex < currentFlow.steps.length - 1) {
      setCurrentStepIndex(prev => prev + 1);
    } else {
      finishOnboarding();
    }
  };

  const previousStep = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prev => prev - 1);
    }
  };

  const skipStep = () => {
    nextStep();
  };

  const finishOnboarding = () => {
    if (currentFlow && user) {
      const completionKey = `${user.role}-onboarding-complete`;
      setCompletedSteps(prev => [...prev, completionKey]);
    }
    
    setIsActive(false);
    setCurrentFlow(null);
    setCurrentStepIndex(0);
  };

  const restartOnboarding = () => {
    if (user) {
      const flowId = getFlowIdForRole(user.role);
      if (flowId) {
        startOnboarding(flowId);
      }
    }
  };

  const isStepCompleted = (stepId: string): boolean => {
    return completedSteps.includes(stepId);
  };

  const markStepCompleted = (stepId: string) => {
    setCompletedSteps(prev => {
      if (!prev.includes(stepId)) {
        return [...prev, stepId];
      }
      return prev;
    });
  };

  const currentStep = currentFlow?.steps[currentStepIndex] || null;

  const value: OnboardingContextType = {
    isActive,
    currentFlow,
    currentStepIndex,
    currentStep,
    startOnboarding,
    nextStep,
    previousStep,
    skipStep,
    finishOnboarding,
    restartOnboarding,
    isStepCompleted,
    markStepCompleted,
  };

  return (
    <OnboardingContext.Provider value={value}>
      {children}
    </OnboardingContext.Provider>
  );
}

export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
}
