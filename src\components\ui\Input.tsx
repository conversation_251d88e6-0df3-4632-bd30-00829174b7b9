'use client';

import * as React from 'react';
import { twMerge } from 'tailwind-merge';

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  'aria-label'?: string;
  'aria-describedby'?: string;
  'aria-invalid'?: boolean;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, label, error, helperText, ...props }, ref) => {
    const inputId = props.id ?? `input-${Math.random().toString(36).substring(2, 11)}`;
    const errorId = error ? `${inputId}-error` : undefined;
    const helperId = helperText ? `${inputId}-helper` : undefined;
    const describedBy = [errorId, helperId, props['aria-describedby']].filter(Boolean).join(' ');

    return (
      <div className="w-full">
        {label && (
          <label
            htmlFor={inputId}
            className="block text-sm font-medium text-[var(--text-primary)] mb-2"
          >
            {label}
            {props.required && (
              <span className="text-[var(--error)] ml-1" aria-label="obrigatório">*</span>
            )}
          </label>
        )}
        <input
          {...props}
          id={inputId}
          type={type}
          className={twMerge(
            'flex h-10 w-full rounded-md border bg-[var(--background)] px-3 py-2 text-sm font-[var(--font-geist-sans)] text-[var(--text-primary)] border-[var(--border-color)] ring-offset-[var(--background)] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-[var(--text-secondary)] focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[var(--primary)] focus-visible:ring-offset-2 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-colors',
            error && 'border-[var(--error)] focus:ring-[var(--error)] focus-visible:ring-[var(--error)]',
            className
          )}
          ref={ref}
          aria-invalid={error ? true : props['aria-invalid']}
          aria-describedby={describedBy || undefined}
        />
        {error && (
          <p
            id={errorId}
            className="text-[var(--error)] text-sm mt-1"
            role="alert"
            aria-live="polite"
          >
            {error}
          </p>
        )}
        {helperText && !error && (
          <p
            id={helperId}
            className="text-[var(--text-secondary)] text-sm mt-1"
          >
            {helperText}
          </p>
        )}
      </div>
    );
  }
);
Input.displayName = 'Input';

export { Input };
