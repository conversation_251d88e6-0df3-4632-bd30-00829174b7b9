import type { NextConfig } from "next";
import { withSentryConfig } from '@sentry/nextjs';

const nextConfig: NextConfig = {
  serverExternalPackages: ['@supabase/supabase-js'],
  eslint: {
    // Permitir build mesmo com alguns warnings de ESLint
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Permitir build mesmo com alguns erros de TypeScript
    ignoreBuildErrors: false,
  },
  // Instrumentação habilitada por padrão no Next.js 15
  // Configurações de logging
  logging: {
    fetches: {
      fullUrl: process.env.NODE_ENV === 'development',
    },
  },
};

// Configurações do Sentry
const sentryWebpackPluginOptions = {
  // Para todas as opções disponíveis, veja:
  // https://github.com/getsentry/sentry-webpack-plugin#options

  // Suprimir todos os logs do plugin de upload
  silent: true,

  // Organização e projeto no Sentry.io
  org: process.env.SENTRY_ORG,
  project: process.env.SENTRY_PROJECT,

  // Apenas fazer upload de source maps em produção
  widenClientFileUpload: true,

  // Esconder source maps dos bundles finais
  hideSourceMaps: true,

  // Desabilitar telemetria do Sentry
  disableLogger: true,

  // Configurações de release
  release: {
    name: process.env.NEXT_PUBLIC_APP_VERSION ?? '1.0.0',
    create: process.env.NODE_ENV === 'production',
    finalize: process.env.NODE_ENV === 'production',
  },
};

// Exportar configuração com Sentry apenas se DSN estiver configurado
export default process.env.SENTRY_DSN || process.env.NEXT_PUBLIC_SENTRY_DSN
  ? withSentryConfig(nextConfig, sentryWebpackPluginOptions)
  : nextConfig;
