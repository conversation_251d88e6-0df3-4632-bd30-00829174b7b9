// Utilitários para o módulo de busca de empresas

import { FiltrosBusca } from '@/types/busca';

/**
 * Formata o endereço completo de uma empresa
 */
export function formatarEnderecoCompleto(
  endereco: string,
  numero: string,
  bairro: string,
  cidade: string,
  estado: string
): string {
  const partes = [
    endereco,
    numero,
    bairro,
    `${cidade}/${estado}`
  ].filter(Boolean);
  
  return partes.join(', ');
}

/**
 * Formata valor monetário para exibição
 */
export function formatarPreco(valor: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(valor);
}

/**
 * Formata faixa de preços para exibição
 */
export function formatarFaixaPrecos(precoMinimo: number, precoMaximo: number): string {
  if (precoMinimo === precoMaximo) {
    return formatarPreco(precoMinimo);
  }
  return `${formatarPreco(precoMinimo)} - ${formatarPreco(precoMaximo)}`;
}

/**
 * Valida se os filtros de busca são válidos
 */
export function validarFiltrosBusca(filtros: FiltrosBusca): { valido: boolean; erros: string[] } {
  const erros: string[] = [];

  // Validar preços
  if (filtros.preco_minimo !== undefined && filtros.preco_minimo < 0) {
    erros.push('Preço mínimo não pode ser negativo');
  }

  if (filtros.preco_maximo !== undefined && filtros.preco_maximo < 0) {
    erros.push('Preço máximo não pode ser negativo');
  }

  if (
    filtros.preco_minimo !== undefined && 
    filtros.preco_maximo !== undefined && 
    filtros.preco_minimo > filtros.preco_maximo
  ) {
    erros.push('Preço mínimo não pode ser maior que o preço máximo');
  }

  // Validar paginação
  if (filtros.pagina !== undefined && filtros.pagina < 1) {
    erros.push('Página deve ser maior que 0');
  }

  if (filtros.limite !== undefined && (filtros.limite < 1 || filtros.limite > 50)) {
    erros.push('Limite deve estar entre 1 e 50');
  }

  // Validar estado (se fornecido, deve ter 2 caracteres)
  if (filtros.estado && filtros.estado.length !== 2) {
    erros.push('Estado deve ter 2 caracteres (ex: SP, RJ)');
  }

  return {
    valido: erros.length === 0,
    erros
  };
}

/**
 * Limpa e normaliza os filtros de busca
 */
export function normalizarFiltrosBusca(filtros: FiltrosBusca): FiltrosBusca {
  return {
    ...filtros,
    termo: filtros.termo?.trim() || undefined,
    cidade: filtros.cidade?.trim() || undefined,
    estado: filtros.estado?.trim().toUpperCase() || undefined,
    bairro: filtros.bairro?.trim() || undefined,
    categorias_servicos: filtros.categorias_servicos?.filter(Boolean) || undefined,
    preco_minimo: filtros.preco_minimo !== undefined && filtros.preco_minimo >= 0 ? filtros.preco_minimo : undefined,
    preco_maximo: filtros.preco_maximo !== undefined && filtros.preco_maximo >= 0 ? filtros.preco_maximo : undefined,
    pagina: Math.max(filtros.pagina || 1, 1),
    limite: Math.min(Math.max(filtros.limite || 12, 1), 50)
  };
}

/**
 * Converte filtros para parâmetros de URL
 */
export function filtrosParaURLParams(filtros: FiltrosBusca): URLSearchParams {
  const params = new URLSearchParams();
  
  Object.entries(filtros).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        if (value.length > 0) {
          params.append(key, value.join(','));
        }
      } else {
        params.append(key, value.toString());
      }
    }
  });

  return params;
}

/**
 * Converte parâmetros de URL para filtros
 */
export function urlParamsParaFiltros(searchParams: URLSearchParams): FiltrosBusca {
  const filtros: FiltrosBusca = {};

  // Extrair parâmetros simples
  const termo = searchParams.get('termo');
  if (termo) filtros.termo = termo;

  const cidade = searchParams.get('cidade');
  if (cidade) filtros.cidade = cidade;

  const estado = searchParams.get('estado');
  if (estado) filtros.estado = estado;

  const bairro = searchParams.get('bairro');
  if (bairro) filtros.bairro = bairro;

  const ordenacao = searchParams.get('ordenacao');
  if (ordenacao) filtros.ordenacao = ordenacao as any;

  // Extrair arrays
  const categorias = searchParams.get('categorias_servicos');
  if (categorias) {
    filtros.categorias_servicos = categorias.split(',').filter(Boolean);
  }

  // Extrair números
  const precoMinimo = searchParams.get('preco_minimo');
  if (precoMinimo) {
    const valor = Number(precoMinimo);
    if (!isNaN(valor) && valor >= 0) {
      filtros.preco_minimo = valor;
    }
  }

  const precoMaximo = searchParams.get('preco_maximo');
  if (precoMaximo) {
    const valor = Number(precoMaximo);
    if (!isNaN(valor) && valor >= 0) {
      filtros.preco_maximo = valor;
    }
  }

  const pagina = searchParams.get('pagina');
  if (pagina) {
    const valor = Number(pagina);
    if (!isNaN(valor) && valor >= 1) {
      filtros.pagina = valor;
    }
  }

  const limite = searchParams.get('limite');
  if (limite) {
    const valor = Number(limite);
    if (!isNaN(valor) && valor >= 1 && valor <= 50) {
      filtros.limite = valor;
    }
  }

  return normalizarFiltrosBusca(filtros);
}

/**
 * Gera texto de resumo dos filtros aplicados
 */
export function gerarResumoFiltros(filtros: FiltrosBusca): string {
  const resumos: string[] = [];

  if (filtros.termo) {
    resumos.push(`"${filtros.termo}"`);
  }

  if (filtros.cidade) {
    resumos.push(`em ${filtros.cidade}`);
  }

  if (filtros.estado) {
    resumos.push(`no ${filtros.estado}`);
  }

  if (filtros.categorias_servicos && filtros.categorias_servicos.length > 0) {
    if (filtros.categorias_servicos.length === 1) {
      resumos.push(`categoria ${filtros.categorias_servicos[0]}`);
    } else {
      resumos.push(`${filtros.categorias_servicos.length} categorias`);
    }
  }

  if (filtros.preco_minimo !== undefined || filtros.preco_maximo !== undefined) {
    if (filtros.preco_minimo !== undefined && filtros.preco_maximo !== undefined) {
      resumos.push(`preços entre ${formatarPreco(filtros.preco_minimo)} e ${formatarPreco(filtros.preco_maximo)}`);
    } else if (filtros.preco_minimo !== undefined) {
      resumos.push(`preços a partir de ${formatarPreco(filtros.preco_minimo)}`);
    } else if (filtros.preco_maximo !== undefined) {
      resumos.push(`preços até ${formatarPreco(filtros.preco_maximo)}`);
    }
  }

  if (resumos.length === 0) {
    return 'Todos os estabelecimentos';
  }

  return `Estabelecimentos ${resumos.join(', ')}`;
}

/**
 * Calcula a distância aproximada entre duas coordenadas (em km)
 * Para futuro uso com geolocalização
 */
export function calcularDistancia(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371; // Raio da Terra em km
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

/**
 * Debounce function para otimizar buscas
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
